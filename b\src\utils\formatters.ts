import Decimal from 'decimal.js';

// 配置Decimal.js
Decimal.config({
  precision: 20,
  rounding: Decimal.ROUND_HALF_UP,
  toExpNeg: -7,
  toExpPos: 21,
});

/**
 * 格式化货币
 */
export function formatCurrency(
  value: number | string,
  currency: string = 'USD',
  locale: string = 'zh-CN'
): string {
  const num = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(num)) return '--';
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(num);
}

/**
 * 格式化百分比
 */
export function formatPercent(
  value: number | string,
  decimals: number = 2,
  locale: string = 'zh-CN'
): string {
  const num = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(num)) return '--';
  
  return new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(num / 100);
}

/**
 * 格式化数字
 */
export function formatNumber(
  value: number | string,
  decimals: number = 2,
  locale: string = 'zh-CN'
): string {
  const num = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(num)) return '--';
  
  return new Intl.NumberFormat(locale, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(num);
}

/**
 * 格式化大数字（K, M, B）
 */
export function formatLargeNumber(
  value: number | string,
  decimals: number = 1,
  locale: string = 'zh-CN'
): string {
  const num = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(num)) return '--';
  
  const abs = Math.abs(num);
  const sign = num < 0 ? '-' : '';
  
  if (abs >= 1e9) {
    return `${sign}${(abs / 1e9).toFixed(decimals)}B`;
  } else if (abs >= 1e6) {
    return `${sign}${(abs / 1e6).toFixed(decimals)}M`;
  } else if (abs >= 1e3) {
    return `${sign}${(abs / 1e3).toFixed(decimals)}K`;
  }
  
  return formatNumber(num, decimals, locale);
}

/**
 * 格式化成交量
 */
export function formatVolume(volume: number | string): string {
  return formatLargeNumber(volume, 1);
}

/**
 * 格式化价格变化
 */
export function formatPriceChange(
  change: number | string,
  showSign: boolean = true
): string {
  const num = typeof change === 'string' ? parseFloat(change) : change;
  
  if (isNaN(num)) return '--';
  
  const formatted = formatNumber(Math.abs(num), 2);
  
  if (!showSign) return formatted;
  
  if (num > 0) return `+${formatted}`;
  if (num < 0) return `-${formatted}`;
  return formatted;
}

/**
 * 格式化日期时间
 */
export function formatDateTime(
  timestamp: number | string | Date,
  options: Intl.DateTimeFormatOptions = {}
): string {
  let date: Date;
  
  if (timestamp instanceof Date) {
    date = timestamp;
  } else if (typeof timestamp === 'string') {
    date = new Date(timestamp);
  } else {
    date = new Date(timestamp);
  }
  
  if (isNaN(date.getTime())) return '--';
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    ...options,
  };
  
  return new Intl.DateTimeFormat('zh-CN', defaultOptions).format(date);
}

/**
 * 格式化相对时间
 */
export function formatRelativeTime(timestamp: number | string | Date): string {
  let date: Date;
  
  if (timestamp instanceof Date) {
    date = timestamp;
  } else if (typeof timestamp === 'string') {
    date = new Date(timestamp);
  } else {
    date = new Date(timestamp);
  }
  
  if (isNaN(date.getTime())) return '--';
  
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  
  if (diffSec < 60) return '刚刚';
  if (diffMin < 60) return `${diffMin}分钟前`;
  if (diffHour < 24) return `${diffHour}小时前`;
  if (diffDay < 7) return `${diffDay}天前`;
  
  return formatDateTime(date, { month: 'short', day: 'numeric' });
}

/**
 * 高精度计算
 */
export class PrecisionCalculator {
  static add(a: number | string, b: number | string): number {
    return new Decimal(a).add(new Decimal(b)).toNumber();
  }
  
  static subtract(a: number | string, b: number | string): number {
    return new Decimal(a).sub(new Decimal(b)).toNumber();
  }
  
  static multiply(a: number | string, b: number | string): number {
    return new Decimal(a).mul(new Decimal(b)).toNumber();
  }
  
  static divide(a: number | string, b: number | string): number {
    return new Decimal(a).div(new Decimal(b)).toNumber();
  }
  
  static round(value: number | string, decimals: number = 2): number {
    return new Decimal(value).toDecimalPlaces(decimals).toNumber();
  }
  
  static percentage(value: number | string, total: number | string): number {
    if (new Decimal(total).isZero()) return 0;
    return new Decimal(value).div(new Decimal(total)).mul(100).toNumber();
  }
}

/**
 * 格式化技术指标值
 */
export function formatIndicatorValue(
  value: number | string,
  indicatorType: string
): string {
  const num = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(num)) return '--';
  
  switch (indicatorType.toLowerCase()) {
    case 'rsi':
    case 'stoch':
    case 'williams':
      return formatNumber(num, 1);
    
    case 'macd':
    case 'ema':
    case 'sma':
    case 'bollinger':
      return formatNumber(num, 4);
    
    case 'volume':
      return formatVolume(num);
    
    default:
      return formatNumber(num, 2);
  }
}

/**
 * 格式化市场状态
 */
export function formatMarketStatus(status: string): string {
  const statusMap: Record<string, string> = {
    'open': '开盘',
    'closed': '收盘',
    'pre-market': '盘前',
    'after-hours': '盘后',
    'holiday': '休市',
  };
  
  return statusMap[status.toLowerCase()] || status;
}

/**
 * 格式化订单类型
 */
export function formatOrderType(type: string): string {
  const typeMap: Record<string, string> = {
    'market': '市价单',
    'limit': '限价单',
    'stop': '止损单',
    'stop-limit': '止损限价单',
    'trailing-stop': '跟踪止损',
  };
  
  return typeMap[type.toLowerCase()] || type;
}

/**
 * 格式化订单状态
 */
export function formatOrderStatus(status: string): string {
  const statusMap: Record<string, string> = {
    'pending': '待成交',
    'filled': '已成交',
    'partially-filled': '部分成交',
    'cancelled': '已取消',
    'rejected': '已拒绝',
    'expired': '已过期',
  };
  
  return statusMap[status.toLowerCase()] || status;
}
