import { useAtom, useAtomValue } from 'jotai';
import { createEffect, onCleanup, createSignal, For, Show } from 'solid-js';
import { 
  topMoversAtom, 
  connectionStatusAtom, 
  marketOverviewAtom,
  updateTickerAtom,
  generateMockTickerData,
  watchlistAtom,
  selectedSymbolAtom,
  setConnectionStatusAtom
} from '@/stores/market';
import { useTheme } from '@/context/ThemeContext';
import { css } from '../../styled-system/css';
import { FiTrendingUp, FiTrendingDown, FiMinus, FiWifi, FiWifiOff } from 'solid-icons/fi';

export default function RealTimeTicker() {
  const { theme } = useTheme();
  const topMovers = useAtomValue(topMoversAtom);
  const [connectionStatus, setConnectionStatus] = useAtom(connectionStatusAtom);
  const marketOverview = useAtomValue(marketOverviewAtom);
  const watchlist = useAtomValue(watchlistAtom);
  const [, updateTicker] = useAtom(updateTickerAtom);
  const [selectedSymbol, setSelectedSymbol] = useAtom(selectedSymbolAtom);
  const [, setConnection] = useAtom(setConnectionStatusAtom);
  
  const [lastUpdate, setLastUpdate] = createSignal<number>(Date.now());
  
  // 模拟实时数据连接
  createEffect(() => {
    let interval: number;
    let timeoutId: number;
    
    // 模拟连接延迟
    timeoutId = setTimeout(() => {
      setConnection(true);
      
      // 开始模拟数据推送
      interval = setInterval(() => {
        // 随机更新一个股票的数据
        const randomSymbol = watchlist[Math.floor(Math.random() * watchlist.length)];
        const mockData = generateMockTickerData(randomSymbol);
        updateTicker(mockData);
        setLastUpdate(Date.now());
      }, 1000 + Math.random() * 2000); // 1-3秒随机间隔
      
    }, 1000);
    
    onCleanup(() => {
      clearTimeout(timeoutId);
      clearInterval(interval);
      setConnection(false);
    });
  });
  
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
  };
  
  const formatPercent = (percent: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(percent / 100);
  };
  
  const formatVolume = (volume: number) => {
    if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(1)}M`;
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(1)}K`;
    }
    return volume.toString();
  };
  
  const getTrendIcon = (changePercent: number) => {
    if (changePercent > 0) return FiTrendingUp;
    if (changePercent < 0) return FiTrendingDown;
    return FiMinus;
  };
  
  const getTrendColor = (changePercent: number) => {
    if (changePercent > 0) return 'success.500';
    if (changePercent < 0) return 'danger.500';
    return 'neutral';
  };
  
  return (
    <div class={css({ mb: 6 })}>
      {/* 标题和连接状态 */}
      <div class={css({
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        mb: 4
      })}>
        <h2 class={css({
          fontSize: 'xl',
          fontWeight: 'bold',
          color: 'gray.900',
          _dark: { color: 'gray.100' }
        })}>
          实时行情
        </h2>
        
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          gap: 3
        })}>
          {/* 连接状态 */}
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            px: 3,
            py: 1,
            rounded: 'full',
            fontSize: 'sm',
            fontWeight: 'medium',
            bg: connectionStatus() ? 'success.50' : 'danger.50',
            color: connectionStatus() ? 'success.700' : 'danger.700',
            _dark: {
              bg: connectionStatus() ? 'success.900/30' : 'danger.900/30',
              color: connectionStatus() ? 'success.300' : 'danger.300'
            }
          })}>
            {connectionStatus() ? <FiWifi size={14} /> : <FiWifiOff size={14} />}
            {connectionStatus() ? '已连接' : '连接中...'}
          </div>
          
          {/* 最后更新时间 */}
          <Show when={connectionStatus()}>
            <div class={css({
              fontSize: 'xs',
              color: 'gray.500',
              _dark: { color: 'gray.400' }
            })}>
              {new Date(lastUpdate()).toLocaleTimeString()}
            </div>
          </Show>
        </div>
      </div>
      
      {/* 市场概览 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: { base: '1fr 1fr', md: 'repeat(4, 1fr)' },
        gap: 4,
        mb: 6
      })}>
        <div class={css({
          p: 4,
          bg: 'white',
          rounded: 'lg',
          border: '1px solid',
          borderColor: 'gray.200',
          _dark: {
            bg: 'gray.800',
            borderColor: 'gray.700'
          }
        })}>
          <div class={css({
            fontSize: 'sm',
            color: 'gray.600',
            _dark: { color: 'gray.400' }
          })}>
            总数
          </div>
          <div class={css({
            fontSize: '2xl',
            fontWeight: 'bold',
            color: 'gray.900',
            _dark: { color: 'gray.100' }
          })}>
            {marketOverview().totalSymbols}
          </div>
        </div>
        
        <div class={css({
          p: 4,
          bg: 'white',
          rounded: 'lg',
          border: '1px solid',
          borderColor: 'gray.200',
          _dark: {
            bg: 'gray.800',
            borderColor: 'gray.700'
          }
        })}>
          <div class={css({
            fontSize: 'sm',
            color: 'gray.600',
            _dark: { color: 'gray.400' }
          })}>
            上涨
          </div>
          <div class={css({
            fontSize: '2xl',
            fontWeight: 'bold',
            color: 'success.600'
          })}>
            {marketOverview().gainers}
          </div>
        </div>
        
        <div class={css({
          p: 4,
          bg: 'white',
          rounded: 'lg',
          border: '1px solid',
          borderColor: 'gray.200',
          _dark: {
            bg: 'gray.800',
            borderColor: 'gray.700'
          }
        })}>
          <div class={css({
            fontSize: 'sm',
            color: 'gray.600',
            _dark: { color: 'gray.400' }
          })}>
            下跌
          </div>
          <div class={css({
            fontSize: '2xl',
            fontWeight: 'bold',
            color: 'danger.600'
          })}>
            {marketOverview().losers}
          </div>
        </div>
        
        <div class={css({
          p: 4,
          bg: 'white',
          rounded: 'lg',
          border: '1px solid',
          borderColor: 'gray.200',
          _dark: {
            bg: 'gray.800',
            borderColor: 'gray.700'
          }
        })}>
          <div class={css({
            fontSize: 'sm',
            color: 'gray.600',
            _dark: { color: 'gray.400' }
          })}>
            平均涨幅
          </div>
          <div class={css({
            fontSize: '2xl',
            fontWeight: 'bold',
            color: marketOverview().avgChange >= 0 ? 'success.600' : 'danger.600'
          })}>
            {formatPercent(marketOverview().avgChange)}
          </div>
        </div>
      </div>
      
      {/* 热门股票 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: { base: '1fr', sm: 'repeat(2, 1fr)', lg: 'repeat(3, 1fr)', xl: 'repeat(5, 1fr)' },
        gap: 3
      })}>
        <For each={topMovers()}>
          {(ticker) => {
            const TrendIcon = getTrendIcon(ticker.changePercent);
            const isSelected = () => selectedSymbol() === ticker.symbol;
            
            return (
              <div 
                class={css({
                  p: 4,
                  bg: 'white',
                  rounded: 'lg',
                  border: '2px solid',
                  borderColor: isSelected() ? 'primary.500' : 'gray.200',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  _hover: {
                    borderColor: 'primary.300',
                    shadow: 'md'
                  },
                  _dark: {
                    bg: 'gray.800',
                    borderColor: isSelected() ? 'primary.400' : 'gray.700'
                  }
                })}
                onClick={() => setSelectedSymbol(ticker.symbol)}
              >
                <div class={css({
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  mb: 2
                })}>
                  <div class={css({
                    fontWeight: 'semibold',
                    fontSize: 'sm',
                    color: 'gray.900',
                    _dark: { color: 'gray.100' }
                  })}>
                    {ticker.symbol}
                  </div>
                  <TrendIcon 
                    size={16} 
                    color={`var(--colors-${getTrendColor(ticker.changePercent).replace('.', '-')})`}
                  />
                </div>
                
                <div class={css({
                  fontSize: 'lg',
                  fontWeight: 'bold',
                  fontFamily: 'mono',
                  color: 'gray.900',
                  _dark: { color: 'gray.100' }
                })}>
                  ${formatPrice(ticker.price)}
                </div>
                
                <div class={css({
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  mt: 1,
                  fontSize: 'xs'
                })}>
                  <span class={css({
                    color: getTrendColor(ticker.changePercent),
                    fontWeight: 'medium'
                  })}>
                    {formatPercent(ticker.changePercent)}
                  </span>
                  <span class={css({
                    color: 'gray.500',
                    _dark: { color: 'gray.400' }
                  })}>
                    {formatVolume(ticker.volume)}
                  </span>
                </div>
              </div>
            );
          }}
        </For>
      </div>
    </div>
  );
}
