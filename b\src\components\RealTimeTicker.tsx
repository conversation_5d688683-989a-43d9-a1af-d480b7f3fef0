import { createSignal, createEffect, onCleanup, For, Show } from 'solid-js';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import numeral from 'numeral';

export interface TickerData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  turnover: number;
  high: number;
  low: number;
  open: number;
  prevClose: number;
  timestamp: number;
}

interface RealTimeTickerProps {
  tickers?: TickerData[];
  watchlist?: string[];
  onSymbolClick?: (symbol: string) => void;
  onAddToWatchlist?: (symbol: string) => void;
  onRemoveFromWatchlist?: (symbol: string) => void;
  theme?: 'light' | 'dark';
  compact?: boolean;
}

export default function RealTimeTicker(props: RealTimeTickerProps) {
  const [selectedSymbol, setSelectedSymbol] = createSignal<string | null>(null);
  const [sortBy, setSortBy] = createSignal<'symbol' | 'change' | 'changePercent' | 'volume'>('symbol');
  const [sortOrder, setSortOrder] = createSignal<'asc' | 'desc'>('asc');
  const [updateTime, setUpdateTime] = createSignal(Date.now());

  // 定时更新时间戳
  const timeInterval = setInterval(() => {
    setUpdateTime(Date.now());
  }, 1000);

  onCleanup(() => {
    clearInterval(timeInterval);
  });

  // 格式化价格
  const formatPrice = (price: number) => {
    return numeral(price).format('0,0.00');
  };

  // 格式化变化百分比
  const formatPercent = (percent: number) => {
    const sign = percent >= 0 ? '+' : '';
    return `${sign}${numeral(percent).format('0.00')}%`;
  };

  // 格式化成交量
  const formatVolume = (volume: number) => {
    if (volume >= 100000000) {
      return numeral(volume / 100000000).format('0.00') + '亿';
    } else if (volume >= 10000) {
      return numeral(volume / 10000).format('0.00') + '万';
    }
    return numeral(volume).format('0,0');
  };

  // 格式化成交额
  const formatTurnover = (turnover: number) => {
    if (turnover >= 100000000) {
      return numeral(turnover / 100000000).format('0.00') + '亿';
    } else if (turnover >= 10000) {
      return numeral(turnover / 10000).format('0.00') + '万';
    }
    return numeral(turnover).format('0,0');
  };

  // 获取涨跌颜色
  const getChangeColor = (change: number) => {
    if (change > 0) return props.theme === 'dark' ? '#10b981' : '#059669'; // 绿色 (涨)
    if (change < 0) return props.theme === 'dark' ? '#ef4444' : '#dc2626'; // 红色 (跌)
    return props.theme === 'dark' ? '#9ca3af' : '#6b7280'; // 平盘
  };

  // 排序数据
  const sortedTickers = () => {
    if (!props.tickers) return [];
    
    const sorted = [...props.tickers].sort((a, b) => {
      let aVal, bVal;
      
      switch (sortBy()) {
        case 'symbol':
          aVal = a.symbol;
          bVal = b.symbol;
          break;
        case 'change':
          aVal = a.change;
          bVal = b.change;
          break;
        case 'changePercent':
          aVal = a.changePercent;
          bVal = b.changePercent;
          break;
        case 'volume':
          aVal = a.volume;
          bVal = b.volume;
          break;
        default:
          return 0;
      }
      
      if (typeof aVal === 'string' && typeof bVal === 'string') {
        return sortOrder() === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
      } else {
        return sortOrder() === 'asc' ? (aVal as number) - (bVal as number) : (bVal as number) - (aVal as number);
      }
    });
    
    return sorted;
  };

  // 处理排序
  const handleSort = (field: typeof sortBy extends () => infer T ? T : never) => {
    if (sortBy() === field) {
      setSortOrder(sortOrder() === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  // 处理股票选择
  const handleSymbolClick = (symbol: string) => {
    setSelectedSymbol(selectedSymbol() === symbol ? null : symbol);
    if (props.onSymbolClick) {
      props.onSymbolClick(symbol);
    }
  };

  const SortIcon = (field: string) => (
    <span style={{
      'margin-left': '4px',
      opacity: sortBy() === field ? '1' : '0.3',
      color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
    }}>
      {sortBy() === field && sortOrder() === 'asc' ? '↑' : '↓'}
    </span>
  );

  if (props.compact) {
    // 紧凑模式 - 横向滚动
    return (
      <div style={{
        background: props.theme === 'dark' ? '#1f2937' : '#ffffff',
        'border-radius': '8px',
        'box-shadow': '0 1px 3px 0 rgb(0 0 0 / 0.1)',
        padding: '16px',
        'overflow-x': 'auto',
      }}>
        <div style={{
          display: 'flex',
          gap: '16px',
          'min-width': 'fit-content',
        }}>
          <For each={sortedTickers()}>
            {(ticker) => (
              <div
                onClick={() => handleSymbolClick(ticker.symbol)}
                style={{
                  'min-width': '120px',
                  padding: '12px',
                  'border-radius': '6px',
                  background: selectedSymbol() === ticker.symbol 
                    ? (props.theme === 'dark' ? '#374151' : '#f3f4f6')
                    : 'transparent',
                  border: `1px solid ${props.theme === 'dark' ? '#4b5563' : '#e5e7eb'}`,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                }}
                onMouseEnter={(e) => {
                  if (selectedSymbol() !== ticker.symbol) {
                    e.currentTarget.style.background = props.theme === 'dark' ? '#29313c' : '#f9fafb';
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedSymbol() !== ticker.symbol) {
                    e.currentTarget.style.background = 'transparent';
                  }
                }}
              >
                <div style={{
                  'font-weight': 'bold',
                  'font-size': '14px',
                  color: props.theme === 'dark' ? '#f9fafb' : '#111827',
                  'margin-bottom': '4px',
                }}>
                  {ticker.symbol}
                </div>
                <div style={{
                  'font-size': '18px',
                  'font-weight': 'bold',
                  color: getChangeColor(ticker.change),
                  'margin-bottom': '2px',
                }}>
                  {formatPrice(ticker.price)}
                </div>
                <div style={{
                  'font-size': '12px',
                  color: getChangeColor(ticker.change),
                }}>
                  {formatPercent(ticker.changePercent)}
                </div>
              </div>
            )}
          </For>
        </div>
      </div>
    );
  }

  // 完整模式 - 表格视图
  return (
    <div style={{
      background: props.theme === 'dark' ? '#1f2937' : '#ffffff',
      'border-radius': '8px',
      'box-shadow': '0 1px 3px 0 rgb(0 0 0 / 0.1)',
      overflow: 'hidden',
    }}>
      {/* 标题栏 */}
      <div style={{
        padding: '16px',
        'border-bottom': `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
        display: 'flex',
        'justify-content': 'space-between',
        'align-items': 'center',
      }}>
        <h3 style={{
          margin: '0',
          'font-size': '18px',
          'font-weight': 'bold',
          color: props.theme === 'dark' ? '#f9fafb' : '#111827',
        }}>
          实时行情
        </h3>
        <div style={{
          'font-size': '12px',
          color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
        }}>
          更新时间: {format(updateTime(), 'HH:mm:ss', { locale: zhCN })}
        </div>
      </div>

      {/* 表格头部 */}
      <div style={{
        display: 'grid',
        'grid-template-columns': '1fr 1fr 1fr 1fr 1fr 0.8fr',
        padding: '12px 16px',
        background: props.theme === 'dark' ? '#374151' : '#f9fafb',
        'border-bottom': `1px solid ${props.theme === 'dark' ? '#4b5563' : '#e5e7eb'}`,
      }}>
        <div
          onClick={() => handleSort('symbol')}
          style={{
            'font-weight': '600',
            'font-size': '12px',
            color: props.theme === 'dark' ? '#d1d5db' : '#374151',
            cursor: 'pointer',
            display: 'flex',
            'align-items': 'center',
          }}
        >
          代码/名称 {SortIcon('symbol')}
        </div>
        <div style={{
          'font-weight': '600',
          'font-size': '12px',
          color: props.theme === 'dark' ? '#d1d5db' : '#374151',
          'text-align': 'right',
        }}>
          最新价
        </div>
        <div
          onClick={() => handleSort('change')}
          style={{
            'font-weight': '600',
            'font-size': '12px',
            color: props.theme === 'dark' ? '#d1d5db' : '#374151',
            'text-align': 'right',
            cursor: 'pointer',
            display: 'flex',
            'align-items': 'center',
            'justify-content': 'flex-end',
          }}
        >
          涨跌额 {SortIcon('change')}
        </div>
        <div
          onClick={() => handleSort('changePercent')}
          style={{
            'font-weight': '600',
            'font-size': '12px',
            color: props.theme === 'dark' ? '#d1d5db' : '#374151',
            'text-align': 'right',
            cursor: 'pointer',
            display: 'flex',
            'align-items': 'center',
            'justify-content': 'flex-end',
          }}
        >
          涨跌幅 {SortIcon('changePercent')}
        </div>
        <div
          onClick={() => handleSort('volume')}
          style={{
            'font-weight': '600',
            'font-size': '12px',
            color: props.theme === 'dark' ? '#d1d5db' : '#374151',
            'text-align': 'right',
            cursor: 'pointer',
            display: 'flex',
            'align-items': 'center',
            'justify-content': 'flex-end',
          }}
        >
          成交量 {SortIcon('volume')}
        </div>
        <div style={{
          'font-weight': '600',
          'font-size': '12px',
          color: props.theme === 'dark' ? '#d1d5db' : '#374151',
          'text-align': 'center',
        }}>
          操作
        </div>
      </div>

      {/* 表格内容 */}
      <div style={{ 'max-height': '400px', 'overflow-y': 'auto' }}>
        <Show when={props.tickers && props.tickers.length > 0} fallback={
          <div style={{
            padding: '32px',
            'text-align': 'center',
            color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
          }}>
            暂无数据
          </div>
        }>
          <For each={sortedTickers()}>
            {(ticker) => (
              <div
                onClick={() => handleSymbolClick(ticker.symbol)}
                style={{
                  display: 'grid',
                  'grid-template-columns': '1fr 1fr 1fr 1fr 1fr 0.8fr',
                  padding: '12px 16px',
                  'border-bottom': `1px solid ${props.theme === 'dark' ? '#374151' : '#f3f4f6'}`,
                  cursor: 'pointer',
                  background: selectedSymbol() === ticker.symbol 
                    ? (props.theme === 'dark' ? '#374151' : '#f3f4f6')
                    : 'transparent',
                  transition: 'background-color 0.2s',
                }}
                onMouseEnter={(e) => {
                  if (selectedSymbol() !== ticker.symbol) {
                    e.currentTarget.style.background = props.theme === 'dark' ? '#29313c' : '#f9fafb';
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedSymbol() !== ticker.symbol) {
                    e.currentTarget.style.background = 'transparent';
                  }
                }}
              >
                <div>
                  <div style={{
                    'font-weight': '600',
                    color: props.theme === 'dark' ? '#f9fafb' : '#111827',
                  }}>
                    {ticker.symbol}
                  </div>
                  <div style={{
                    'font-size': '12px',
                    color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
                  }}>
                    {ticker.name}
                  </div>
                </div>
                
                <div style={{
                  'text-align': 'right',
                  'font-weight': '600',
                  'font-size': '14px',
                  color: getChangeColor(ticker.change),
                }}>
                  {formatPrice(ticker.price)}
                </div>
                
                <div style={{
                  'text-align': 'right',
                  'font-weight': '500',
                  color: getChangeColor(ticker.change),
                }}>
                  {ticker.change >= 0 ? '+' : ''}{formatPrice(ticker.change)}
                </div>
                
                <div style={{
                  'text-align': 'right',
                  'font-weight': '500',
                  color: getChangeColor(ticker.change),
                }}>
                  {formatPercent(ticker.changePercent)}
                </div>
                
                <div style={{
                  'text-align': 'right',
                  'font-size': '12px',
                  color: props.theme === 'dark' ? '#d1d5db' : '#374151',
                }}>
                  {formatVolume(ticker.volume)}
                </div>
                
                <div style={{
                  'text-align': 'center',
                }}>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      const isInWatchlist = props.watchlist?.includes(ticker.symbol);
                      if (isInWatchlist) {
                        props.onRemoveFromWatchlist?.(ticker.symbol);
                      } else {
                        props.onAddToWatchlist?.(ticker.symbol);
                      }
                    }}
                    style={{
                      padding: '4px 8px',
                      'border-radius': '4px',
                      border: 'none',
                      'font-size': '12px',
                      cursor: 'pointer',
                      background: props.watchlist?.includes(ticker.symbol) 
                        ? '#ef4444' : '#3b82f6',
                      color: 'white',
                    }}
                  >
                    {props.watchlist?.includes(ticker.symbol) ? '移除' : '关注'}
                  </button>
                </div>
              </div>
            )}
          </For>
        </Show>
      </div>
    </div>
  );
}