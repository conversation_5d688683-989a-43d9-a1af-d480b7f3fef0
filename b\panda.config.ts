import { defineConfig } from '@pandacss/dev';

export default defineConfig({
  // 是否启用预设
  preflight: true,
  
  // 包含的文件
  include: ['./src/**/*.{js,jsx,ts,tsx}'],
  
  // 排除的文件
  exclude: [],
  
  // 主题配置
  theme: {
    extend: {
      colors: {
        // 金融主题色彩
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        danger: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        // 金融数据颜色
        bull: '#26a69a', // 牛市绿
        bear: '#ef5350', // 熊市红
        neutral: '#78909c', // 中性灰
      },
      fontFamily: {
        mono: ['JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', 'monospace'],
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-subtle': 'bounceSubtle 1s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        bounceSubtle: {
          '0%, 100%': { transform: 'translateY(-2px)' },
          '50%': { transform: 'translateY(0)' },
        },
      },
      boxShadow: {
        'card': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'card-hover': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'glow': '0 0 20px rgba(59, 130, 246, 0.3)',
      },
    },
  },
  
  // 全局CSS
  globalCss: {
    '*': {
      boxSizing: 'border-box',
    },
    'html, body': {
      margin: 0,
      padding: 0,
      fontFamily: 'Inter, system-ui, sans-serif',
      lineHeight: 1.6,
    },
    body: {
      backgroundColor: 'var(--colors-gray-50)',
      color: 'var(--colors-gray-900)',
      transition: 'background-color 0.2s ease, color 0.2s ease',
    },
    'body.dark': {
      backgroundColor: 'var(--colors-gray-900)',
      color: 'var(--colors-gray-100)',
    },
    // 滚动条样式
    '::-webkit-scrollbar': {
      width: '6px',
      height: '6px',
    },
    '::-webkit-scrollbar-track': {
      backgroundColor: 'var(--colors-gray-100)',
    },
    '::-webkit-scrollbar-thumb': {
      backgroundColor: 'var(--colors-gray-400)',
      borderRadius: '3px',
    },
    '::-webkit-scrollbar-thumb:hover': {
      backgroundColor: 'var(--colors-gray-500)',
    },
  },
  
  // 输出目录
  outdir: 'styled-system',

  // 生成 CSS 文件
  emitPackage: true,
  
  // 条件样式
  conditions: {
    dark: '.dark &',
    light: '.light &',
  },
});
