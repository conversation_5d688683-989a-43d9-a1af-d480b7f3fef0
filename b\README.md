# 量化交易前端平台 - 简化版本

这是一个基于 SolidJS 的轻量级量化交易前端平台的简化版本。

## 特性

- ✅ **轻量级架构**: 移除了复杂的CSS-in-JS和状态管理库
- ✅ **简洁的UI**: 使用内联样式实现简单的界面
- ✅ **基础路由**: 包含仪表盘、市场数据、策略编辑和回测分析页面
- ✅ **主题切换**: 支持明暗主题切换
- ✅ **响应式设计**: 适配不同屏幕尺寸

## 技术栈

- **前端框架**: SolidJS 1.8.0
- **路由**: @solidjs/router
- **构建工具**: Vite
- **类型检查**: TypeScript
- **样式**: 内联样式 (简化版本)

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

应用将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 项目结构

```
src/
├── components/          # 组件
│   ├── Header.tsx      # 顶部导航
│   ├── Footer.tsx      # 底部信息
│   └── LoadingSpinner.tsx # 加载动画
├── pages/              # 页面
│   ├── Dashboard.tsx   # 仪表盘
│   ├── MarketData.tsx  # 市场数据
│   ├── StrategyEditor.tsx # 策略编辑
│   └── BacktestAnalysis.tsx # 回测分析
├── context/            # 上下文
│   └── ThemeContext.tsx # 主题管理
├── types/              # 类型定义
│   └── index.ts
├── App.tsx             # 主应用组件
├── index.tsx           # 应用入口
└── styles.css          # 全局样式
```

## 页面说明

### 仪表盘 (/)
- 显示关键指标卡片
- 市场热点信息
- 策略表现概览

### 市场数据 (/market)
- 实时行情卡片
- 市场分析信息
- 热门资产列表

### 策略编辑 (/strategy)
- 策略列表管理
- AI策略助手界面
- 代码编辑器占位

### 回测分析 (/backtest)
- 回测配置表单
- 关键指标展示
- 历史回测记录

## 简化说明

这个版本相比完整版本进行了以下简化：

1. **移除了复杂依赖**:
   - 删除了 Panda CSS
   - 删除了 Jotai 状态管理
   - 删除了图标库

2. **简化了样式系统**:
   - 使用内联样式替代CSS-in-JS
   - 保留了基本的响应式设计

3. **移除了复杂组件**:
   - 图表组件 (占位显示)
   - AI编辑器组件 (占位显示)
   - 实时数据组件 (占位显示)

4. **简化了状态管理**:
   - 使用本地状态替代全局状态
   - 移除了复杂的数据流

## 开发说明

这个简化版本专注于展示应用的基本结构和页面布局，适合：

- 快速原型开发
- 学习 SolidJS 基础
- 作为更复杂版本的起点

如需完整功能，请参考原始的完整版本。

## 许可证

MIT License
