# 轻量级量化交易前端平台

基于 SolidJS + Jotai + Panda CSS 构建的现代化量化交易前端平台，专注于性能、美观和智能化。

## ✨ 特性

- 🚀 **极致性能** - 首屏加载 < 50KB，行情更新延迟 < 1ms
- 📊 **专业图表** - 基于 Lightweight Charts 的金融级K线图表
- 🤖 **AI辅助** - 集成 Transformers.js 实现浏览器内AI策略生成
- 🎨 **现代设计** - 自适应主题系统，专业金融界面
- ⚡ **响应式** - 支持桌面端和移动端，流畅交互体验

## 🛠️ 技术栈

- **核心框架**: SolidJS 1.8+ (极致性能的响应式框架)
- **状态管理**: Jotai 2.6+ (原子化状态管理)
- **样式系统**: Panda CSS 0.39+ (零运行时CSS-in-JS)
- **图表库**: Lightweight Charts 4.0+ (专业金融图表)
- **代码编辑**: CodeMirror 6.0+ (现代代码编辑器)
- **AI功能**: Transformers.js 2.0+ (浏览器内机器学习)
- **构建工具**: Vite 5.0+ (快速构建)
- **代码质量**: Biome 1.7+ (快速代码检查和格式化)

## 📦 核心模块

### 🏪 实时行情
- SSE/WebSocket 实时数据推送
- 多资产类型支持 (股票、期货、数字货币)
- 智能异动检测和提醒

### 📈 专业图表
- 高性能K线图表渲染
- 多种技术指标支持
- 自定义绘图工具

### 🧠 AI策略助手
- 自然语言策略描述
- 浏览器内AI模型推理
- 智能策略优化建议

### 🔄 回测分析
- WASM加速计算引擎
- 多维度性能指标
- 可视化回测报告

## 🚀 快速开始

### 环境要求

- Node.js 18.0+
- 现代浏览器 (支持ES2020+)

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

应用将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

## 📁 项目结构

```
src/
├── components/          # 可复用组件
│   ├── Header.tsx      # 顶部导航
│   ├── RealTimeTicker.tsx  # 实时行情
│   ├── FinancialChart.tsx  # 金融图表
│   └── AIStrategyEditor.tsx # AI策略编辑器
├── pages/              # 页面组件
│   ├── Dashboard.tsx   # 仪表盘
│   ├── StrategyEditor.tsx # 策略编辑
│   ├── BacktestAnalysis.tsx # 回测分析
│   └── MarketData.tsx  # 市场数据
├── stores/             # 状态管理
│   ├── market.ts       # 市场数据状态
│   └── strategy.ts     # 策略状态
├── context/            # 上下文
│   └── ThemeContext.tsx # 主题管理
├── utils/              # 工具函数
│   ├── formatters.ts   # 数据格式化
│   └── indicators.ts   # 技术指标
├── workers/            # Web Workers
│   └── backtest.worker.ts # 回测计算
├── types/              # 类型定义
├── App.tsx             # 应用根组件
└── index.tsx           # 应用入口
```

## 🎯 性能优化

### 代码分割
- 按页面和功能模块分割
- 重型模块懒加载
- AI模型按需加载

### 运行时优化
- SolidJS 细粒度响应式更新
- 虚拟滚动长列表
- 图表渲染优化

### 构建优化
- Tree-shaking 移除未使用代码
- 资源压缩和缓存
- 现代浏览器优化

## 🔧 开发指南

### 代码规范

```bash
# 代码检查
npm run lint

# 代码格式化
npm run format

# 类型检查
npm run type-check
```

### 主题系统

支持明暗主题切换，使用 Panda CSS 条件样式：

```tsx
<div class={css({
  bg: 'white',
  _dark: { bg: 'gray.900' }
})}>
  内容
</div>
```

### 状态管理

使用 Jotai 原子化状态管理：

```tsx
import { atom, useAtom } from 'jotai';

const countAtom = atom(0);

function Counter() {
  const [count, setCount] = useAtom(countAtom);
  return <button onClick={() => setCount(c => c + 1)}>{count}</button>;
}
```

## 📊 性能指标

- **首屏加载**: < 50KB (gzip)
- **行情延迟**: < 1ms
- **图表帧率**: 60 FPS
- **内存占用**: < 50MB
- **构建时间**: < 10s

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- [SolidJS](https://solidjs.com/) - 高性能响应式框架
- [Jotai](https://jotai.org/) - 原子化状态管理
- [Panda CSS](https://panda-css.com/) - 零运行时CSS-in-JS
- [Lightweight Charts](https://tradingview.github.io/lightweight-charts/) - 专业金融图表
- [Transformers.js](https://huggingface.co/docs/transformers.js/) - 浏览器内机器学习
