const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/StrategyEditor-DnZTH3yZ.js","assets/StrategyEditor-CXBm6MfC.css"])))=>i.map(i=>d[i]);
(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))n(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&n(o)}).observe(document,{childList:!0,subtree:!0});function t(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function n(s){if(s.ep)return;s.ep=!0;const i=t(s);fetch(s.href,i)}})();const v={context:void 0,registry:void 0,effects:void 0,done:!1,getContextId(){return Xt(this.context.count)},getNextContextId(){return Xt(this.context.count++)}};function Xt(r){const e=String(r),t=e.length-1;return v.context.id+(t?String.fromCharCode(96+t):"")+e}function se(r){v.context=r}const Sr=!1,yn=(r,e)=>r===e,Xe=Symbol("solid-proxy"),kr=typeof Proxy=="function",bn=Symbol("solid-track"),Je={equals:yn};let vr=xr;const Z=1,Oe=2,_r={owned:null,cleanups:null,context:null,owner:null},ut={};var A=null;let m=null,wn=null,T=null,W=null,F=null,rt=0;function Ee(r,e){const t=T,n=A,s=r.length===0,i=e===void 0?n:e,o=s?_r:{owned:null,cleanups:null,context:i?i.context:null,owner:i},a=s?r:()=>r(()=>V(()=>oe(o)));A=o,T=null;try{return j(a,!0)}finally{T=t,A=n}}function D(r,e){e=e?Object.assign({},Je,e):Je;const t={value:r,observers:null,observerSlots:null,comparator:e.equals||void 0},n=s=>(typeof s=="function"&&(m&&m.running&&m.sources.has(t)?s=s(t.tValue):s=s(t.value)),Ar(t,s));return[Er.bind(t),n]}function Jt(r,e,t){const n=nt(r,e,!0,Z);xe(n)}function M(r,e,t){const n=nt(r,e,!1,Z);xe(n)}function Ae(r,e,t){vr=Rn;const n=nt(r,e,!1,Z),s=de&&ge(de);s&&(n.suspense=s),n.user=!0,F?F.push(n):xe(n)}function P(r,e,t){t=t?Object.assign({},Je,t):Je;const n=nt(r,e,!0,0);return n.observers=null,n.observerSlots=null,n.comparator=t.equals||void 0,xe(n),Er.bind(n)}function Sn(r){return r&&typeof r=="object"&&"then"in r}function kn(r,e,t){let n,s,i;n=!0,s=r,i={};let o=null,a=ut,c=null,l=!1,u=!1,h="initialValue"in i,d=typeof n=="function"&&P(n);const g=new Set,[w,p]=(i.storage||D)(i.initialValue),[y,k]=D(void 0),[S,N]=D(void 0,{equals:!1}),[L,$]=D(h?"ready":"unresolved");v.context&&(c=v.getNextContextId(),i.ssrLoadFrom==="initial"?a=i.initialValue:v.load&&v.has(c)&&(a=v.load(c)));function q(O,I,_,E){return o===O&&(o=null,E!==void 0&&(h=!0),(O===a||I===a)&&i.onHydrated&&queueMicrotask(()=>i.onHydrated(E,{value:I})),a=ut,m&&O&&l?(m.promises.delete(O),l=!1,j(()=>{m.running=!0,Be(I,_)},!1)):Be(I,_)),I}function Be(O,I){j(()=>{I===void 0&&p(()=>O),$(I!==void 0?"errored":h?"ready":"unresolved"),k(I);for(const _ of g.keys())_.decrement();g.clear()},!1)}function be(){const O=de&&ge(de),I=w(),_=y();if(_!==void 0&&!o)throw _;return T&&!T.user&&O&&Jt(()=>{S(),o&&(O.resolved&&m&&l?m.promises.add(o):g.has(O)||(O.increment(),g.add(O)))}),I}function Re(O=!0){if(O!==!1&&u)return;u=!1;const I=d?d():n;if(l=m&&m.running,I==null||I===!1){q(o,V(w));return}m&&o&&m.promises.delete(o);let _;const E=a!==ut?a:V(()=>{try{return s(I,{value:w(),refetching:O})}catch(U){_=U}});if(_!==void 0){q(o,void 0,ze(_),I);return}else if(!Sn(E))return q(o,E,void 0,I),E;return o=E,"v"in E?(E.s===1?q(o,E.v,void 0,I):q(o,void 0,ze(E.v),I),E):(u=!0,queueMicrotask(()=>u=!1),j(()=>{$(h?"refreshing":"pending"),N()},!1),E.then(U=>q(E,U,void 0,I),U=>q(E,void 0,ze(U),I)))}Object.defineProperties(be,{state:{get:()=>L()},error:{get:()=>y()},loading:{get(){const O=L();return O==="pending"||O==="refreshing"}},latest:{get(){if(!h)return be();const O=y();if(O&&!o)throw O;return w()}}});let De=A;return d?Jt(()=>(De=A,Re(!1))):Re(!1),[be,{refetch:O=>Bt(De,()=>Re(O)),mutate:p}]}function vn(r){return j(r,!1)}function V(r){if(T===null)return r();const e=T;T=null;try{return r()}finally{T=e}}function Nt(r,e,t){const n=Array.isArray(r);let s,i=t&&t.defer;return o=>{let a;if(n){a=Array(r.length);for(let l=0;l<r.length;l++)a[l]=r[l]()}else a=r();if(i)return i=!1,o;const c=V(()=>e(a,s,o));return s=a,c}}function _n(r){Ae(()=>V(r))}function Ne(r){return A===null||(A.cleanups===null?A.cleanups=[r]:A.cleanups.push(r)),r}function Mt(){return A}function Bt(r,e){const t=A,n=T;A=r,T=null;try{return j(e,!0)}catch(s){$t(s)}finally{A=t,T=n}}function En(r){if(m&&m.running)return r(),m.done;const e=T,t=A;return Promise.resolve().then(()=>{T=e,A=t;let n;return de&&(n=m||(m={sources:new Set,effects:[],promises:new Set,disposed:new Set,queue:new Set,running:!0}),n.done||(n.done=new Promise(s=>n.resolve=s)),n.running=!0),j(r,!1),T=A=null,n?n.done:void 0})}const[Go,Zt]=D(!1);function An(r){F.push.apply(F,r),r.length=0}function fe(r,e){const t=Symbol("context");return{id:t,Provider:Cn(t),defaultValue:r}}function ge(r){let e;return A&&A.context&&(e=A.context[r.id])!==void 0?e:r.defaultValue}function Dt(r){const e=P(r),t=P(()=>kt(e()));return t.toArray=()=>{const n=t();return Array.isArray(n)?n:n!=null?[n]:[]},t}let de;function xn(){return de||(de=fe())}function Er(){const r=m&&m.running;if(this.sources&&(r?this.tState:this.state))if((r?this.tState:this.state)===Z)xe(this);else{const e=W;W=null,j(()=>et(this),!1),W=e}if(T){const e=this.observers?this.observers.length:0;T.sources?(T.sources.push(this),T.sourceSlots.push(e)):(T.sources=[this],T.sourceSlots=[e]),this.observers?(this.observers.push(T),this.observerSlots.push(T.sources.length-1)):(this.observers=[T],this.observerSlots=[T.sources.length-1])}return r&&m.sources.has(this)?this.tValue:this.value}function Ar(r,e,t){let n=m&&m.running&&m.sources.has(r)?r.tValue:r.value;if(!r.comparator||!r.comparator(n,e)){if(m){const s=m.running;(s||!t&&m.sources.has(r))&&(m.sources.add(r),r.tValue=e),s||(r.value=e)}else r.value=e;r.observers&&r.observers.length&&j(()=>{for(let s=0;s<r.observers.length;s+=1){const i=r.observers[s],o=m&&m.running;o&&m.disposed.has(i)||((o?!i.tState:!i.state)&&(i.pure?W.push(i):F.push(i),i.observers&&Tr(i)),o?i.tState=Z:i.state=Z)}if(W.length>1e6)throw W=[],new Error},!1)}return e}function xe(r){if(!r.fn)return;oe(r);const e=rt;er(r,m&&m.running&&m.sources.has(r)?r.tValue:r.value,e),m&&!m.running&&m.sources.has(r)&&queueMicrotask(()=>{j(()=>{m&&(m.running=!0),T=A=r,er(r,r.tValue,e),T=A=null},!1)})}function er(r,e,t){let n;const s=A,i=T;T=A=r;try{n=r.fn(e)}catch(o){return r.pure&&(m&&m.running?(r.tState=Z,r.tOwned&&r.tOwned.forEach(oe),r.tOwned=void 0):(r.state=Z,r.owned&&r.owned.forEach(oe),r.owned=null)),r.updatedAt=t+1,$t(o)}finally{T=i,A=s}(!r.updatedAt||r.updatedAt<=t)&&(r.updatedAt!=null&&"observers"in r?Ar(r,n,!0):m&&m.running&&r.pure?(m.sources.add(r),r.tValue=n):r.value=n,r.updatedAt=t)}function nt(r,e,t,n=Z,s){const i={fn:r,state:n,updatedAt:null,owned:null,sources:null,sourceSlots:null,cleanups:null,value:e,owner:A,context:A?A.context:null,pure:t};return m&&m.running&&(i.state=0,i.tState=n),A===null||A!==_r&&(m&&m.running&&A.pure?A.tOwned?A.tOwned.push(i):A.tOwned=[i]:A.owned?A.owned.push(i):A.owned=[i]),i}function Ze(r){const e=m&&m.running;if((e?r.tState:r.state)===0)return;if((e?r.tState:r.state)===Oe)return et(r);if(r.suspense&&V(r.suspense.inFallback))return r.suspense.effects.push(r);const t=[r];for(;(r=r.owner)&&(!r.updatedAt||r.updatedAt<rt);){if(e&&m.disposed.has(r))return;(e?r.tState:r.state)&&t.push(r)}for(let n=t.length-1;n>=0;n--){if(r=t[n],e){let s=r,i=t[n+1];for(;(s=s.owner)&&s!==i;)if(m.disposed.has(s))return}if((e?r.tState:r.state)===Z)xe(r);else if((e?r.tState:r.state)===Oe){const s=W;W=null,j(()=>et(r,t[0]),!1),W=s}}}function j(r,e){if(W)return r();let t=!1;e||(W=[]),F?t=!0:F=[],rt++;try{const n=r();return Tn(t),n}catch(n){t||(F=null),W=null,$t(n)}}function Tn(r){if(W&&(xr(W),W=null),r)return;let e;if(m){if(!m.promises.size&&!m.queue.size){const n=m.sources,s=m.disposed;F.push.apply(F,m.effects),e=m.resolve;for(const i of F)"tState"in i&&(i.state=i.tState),delete i.tState;m=null,j(()=>{for(const i of s)oe(i);for(const i of n){if(i.value=i.tValue,i.owned)for(let o=0,a=i.owned.length;o<a;o++)oe(i.owned[o]);i.tOwned&&(i.owned=i.tOwned),delete i.tValue,delete i.tOwned,i.tState=0}Zt(!1)},!1)}else if(m.running){m.running=!1,m.effects.push.apply(m.effects,F),F=null,Zt(!0);return}}const t=F;F=null,t.length&&j(()=>vr(t),!1),e&&e()}function xr(r){for(let e=0;e<r.length;e++)Ze(r[e])}function Rn(r){let e,t=0;for(e=0;e<r.length;e++){const n=r[e];n.user?r[t++]=n:Ze(n)}if(v.context){if(v.count){v.effects||(v.effects=[]),v.effects.push(...r.slice(0,t));return}se()}for(v.effects&&(v.done||!v.count)&&(r=[...v.effects,...r],t+=v.effects.length,delete v.effects),e=0;e<t;e++)Ze(r[e])}function et(r,e){const t=m&&m.running;t?r.tState=0:r.state=0;for(let n=0;n<r.sources.length;n+=1){const s=r.sources[n];if(s.sources){const i=t?s.tState:s.state;i===Z?s!==e&&(!s.updatedAt||s.updatedAt<rt)&&Ze(s):i===Oe&&et(s,e)}}}function Tr(r){const e=m&&m.running;for(let t=0;t<r.observers.length;t+=1){const n=r.observers[t];(e?!n.tState:!n.state)&&(e?n.tState=Oe:n.state=Oe,n.pure?W.push(n):F.push(n),n.observers&&Tr(n))}}function oe(r){let e;if(r.sources)for(;r.sources.length;){const t=r.sources.pop(),n=r.sourceSlots.pop(),s=t.observers;if(s&&s.length){const i=s.pop(),o=t.observerSlots.pop();n<s.length&&(i.sourceSlots[o]=n,s[n]=i,t.observerSlots[n]=o)}}if(r.tOwned){for(e=r.tOwned.length-1;e>=0;e--)oe(r.tOwned[e]);delete r.tOwned}if(m&&m.running&&r.pure)Rr(r,!0);else if(r.owned){for(e=r.owned.length-1;e>=0;e--)oe(r.owned[e]);r.owned=null}if(r.cleanups){for(e=r.cleanups.length-1;e>=0;e--)r.cleanups[e]();r.cleanups=null}m&&m.running?r.tState=0:r.state=0}function Rr(r,e){if(e||(r.tState=0,m.disposed.add(r)),r.owned)for(let t=0;t<r.owned.length;t++)Rr(r.owned[t])}function ze(r){return r instanceof Error?r:new Error(typeof r=="string"?r:"Unknown error",{cause:r})}function $t(r,e=A){throw ze(r)}function kt(r){if(typeof r=="function"&&!r.length)return kt(r());if(Array.isArray(r)){const e=[];for(let t=0;t<r.length;t++){const n=kt(r[t]);Array.isArray(n)?e.push.apply(e,n):e.push(n)}return e}return r}function Cn(r,e){return function(n){let s;return M(()=>s=V(()=>(A.context={...A.context,[r]:n.value},Dt(()=>n.children))),void 0),s}}const Pn=Symbol("fallback");function tr(r){for(let e=0;e<r.length;e++)r[e]()}function On(r,e,t={}){let n=[],s=[],i=[],o=0,a=e.length>1?[]:null;return Ne(()=>tr(i)),()=>{let c=r()||[],l=c.length,u,h;return c[bn],V(()=>{let g,w,p,y,k,S,N,L,$;if(l===0)o!==0&&(tr(i),i=[],n=[],s=[],o=0,a&&(a=[])),t.fallback&&(n=[Pn],s[0]=Ee(q=>(i[0]=q,t.fallback())),o=1);else if(o===0){for(s=new Array(l),h=0;h<l;h++)n[h]=c[h],s[h]=Ee(d);o=l}else{for(p=new Array(l),y=new Array(l),a&&(k=new Array(l)),S=0,N=Math.min(o,l);S<N&&n[S]===c[S];S++);for(N=o-1,L=l-1;N>=S&&L>=S&&n[N]===c[L];N--,L--)p[L]=s[N],y[L]=i[N],a&&(k[L]=a[N]);for(g=new Map,w=new Array(L+1),h=L;h>=S;h--)$=c[h],u=g.get($),w[h]=u===void 0?-1:u,g.set($,h);for(u=S;u<=N;u++)$=n[u],h=g.get($),h!==void 0&&h!==-1?(p[h]=s[u],y[h]=i[u],a&&(k[h]=a[u]),h=w[h],g.set($,h)):i[u]();for(h=S;h<l;h++)h in p?(s[h]=p[h],i[h]=y[h],a&&(a[h]=k[h],a[h](h))):s[h]=Ee(d);s=s.slice(0,o=l),n=c.slice(0)}return s});function d(g){if(i[h]=g,a){const[w,p]=D(h);return a[h]=p,e(c[h],w)}return e(c[h])}}}function b(r,e){return V(()=>r(e||{}))}function Ue(){return!0}const vt={get(r,e,t){return e===Xe?t:r.get(e)},has(r,e){return e===Xe?!0:r.has(e)},set:Ue,deleteProperty:Ue,getOwnPropertyDescriptor(r,e){return{configurable:!0,enumerable:!0,get(){return r.get(e)},set:Ue,deleteProperty:Ue}},ownKeys(r){return r.keys()}};function ht(r){return(r=typeof r=="function"?r():r)?r:{}}function Ln(){for(let r=0,e=this.length;r<e;++r){const t=this[r]();if(t!==void 0)return t}}function _t(...r){let e=!1;for(let o=0;o<r.length;o++){const a=r[o];e=e||!!a&&Xe in a,r[o]=typeof a=="function"?(e=!0,P(a)):a}if(kr&&e)return new Proxy({get(o){for(let a=r.length-1;a>=0;a--){const c=ht(r[a])[o];if(c!==void 0)return c}},has(o){for(let a=r.length-1;a>=0;a--)if(o in ht(r[a]))return!0;return!1},keys(){const o=[];for(let a=0;a<r.length;a++)o.push(...Object.keys(ht(r[a])));return[...new Set(o)]}},vt);const t={},n=Object.create(null);for(let o=r.length-1;o>=0;o--){const a=r[o];if(!a)continue;const c=Object.getOwnPropertyNames(a);for(let l=c.length-1;l>=0;l--){const u=c[l];if(u==="__proto__"||u==="constructor")continue;const h=Object.getOwnPropertyDescriptor(a,u);if(!n[u])n[u]=h.get?{enumerable:!0,configurable:!0,get:Ln.bind(t[u]=[h.get.bind(a)])}:h.value!==void 0?h:void 0;else{const d=t[u];d&&(h.get?d.push(h.get.bind(a)):h.value!==void 0&&d.push(()=>h.value))}}}const s={},i=Object.keys(n);for(let o=i.length-1;o>=0;o--){const a=i[o],c=n[a];c&&c.get?Object.defineProperty(s,a,c):s[a]=c?c.value:void 0}return s}function In(r,...e){if(kr&&Xe in r){const s=new Set(e.length>1?e.flat():e[0]),i=e.map(o=>new Proxy({get(a){return o.includes(a)?r[a]:void 0},has(a){return o.includes(a)&&a in r},keys(){return o.filter(a=>a in r)}},vt));return i.push(new Proxy({get(o){return s.has(o)?void 0:r[o]},has(o){return s.has(o)?!1:o in r},keys(){return Object.keys(r).filter(o=>!s.has(o))}},vt)),i}const t={},n=e.map(()=>({}));for(const s of Object.getOwnPropertyNames(r)){const i=Object.getOwnPropertyDescriptor(r,s),o=!i.get&&!i.set&&i.enumerable&&i.writable&&i.configurable;let a=!1,c=0;for(const l of e)l.includes(s)&&(a=!0,o?n[c][s]=i.value:Object.defineProperty(n[c],s,i)),++c;a||(o?t[s]=i.value:Object.defineProperty(t,s,i))}return[...n,t]}function pe(r){let e,t;const n=s=>{const i=v.context;if(i){const[a,c]=D();v.count||(v.count=0),v.count++,(t||(t=r())).then(l=>{!v.done&&se(i),v.count--,c(()=>l.default),se()}),e=a}else if(!e){const[a]=kn(()=>(t||(t=r())).then(c=>c.default));e=a}let o;return P(()=>(o=e())?V(()=>{if(!i||v.done)return o(s);const a=v.context;se(i);const c=o(s);return se(a),c}):"")};return n.preload=()=>t||((t=r()).then(s=>e=()=>s.default),t),n}const Nn=r=>`Stale read from <${r}>.`;function Qo(r){const e="fallback"in r&&{fallback:()=>r.fallback};return P(On(()=>r.each,r.children,e||void 0))}function st(r){const e=r.keyed,t=P(()=>r.when,void 0,void 0),n=e?t:P(t,void 0,{equals:(s,i)=>!s==!i});return P(()=>{const s=n();if(s){const i=r.children;return typeof i=="function"&&i.length>0?V(()=>i(e?s:()=>{if(!V(n))throw Nn("Show");return t()})):i}return r.fallback},void 0,void 0)}const Mn=fe();function Bn(r){let e=0,t,n,s,i,o;const[a,c]=D(!1),l=xn(),u={increment:()=>{++e===1&&c(!0)},decrement:()=>{--e===0&&c(!1)},inFallback:a,effects:[],resolved:!1},h=Mt();if(v.context&&v.load){const w=v.getContextId();let p=v.load(w);if(p&&(typeof p!="object"||p.s!==1?s=p:v.gather(w)),s&&s!=="$$f"){const[y,k]=D(void 0,{equals:!1});i=y,s.then(()=>{if(v.done)return k();v.gather(w),se(n),k(),se()},S=>{o=S,k()})}}const d=ge(Mn);d&&(t=d.register(u.inFallback));let g;return Ne(()=>g&&g()),b(l.Provider,{value:u,get children(){return P(()=>{if(o)throw o;if(n=v.context,i)return i(),i=void 0;n&&s==="$$f"&&se();const w=P(()=>r.children);return P(p=>{const y=u.inFallback(),{showContent:k=!0,showFallback:S=!0}=t?t():{};if((!y||s&&s!=="$$f")&&k)return u.resolved=!0,g&&g(),g=n=s=void 0,An(u.effects),w();if(S)return g?p:Ee(N=>(g=N,n&&(se({id:n.id+"F",count:0}),n=void 0),r.fallback),h)})})}})}const Dn=["allowfullscreen","async","alpha","autofocus","autoplay","checked","controls","default","disabled","formnovalidate","hidden","indeterminate","inert","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","seamless","selected","adauctionheaders","browsingtopics","credentialless","defaultchecked","defaultmuted","defaultselected","defer","disablepictureinpicture","disableremoteplayback","preservespitch","shadowrootclonable","shadowrootcustomelementregistry","shadowrootdelegatesfocus","shadowrootserializable","sharedstoragewritable"],$n=new Set(["className","value","readOnly","noValidate","formNoValidate","isMap","noModule","playsInline","adAuctionHeaders","allowFullscreen","browsingTopics","defaultChecked","defaultMuted","defaultSelected","disablePictureInPicture","disableRemotePlayback","preservesPitch","shadowRootClonable","shadowRootCustomElementRegistry","shadowRootDelegatesFocus","shadowRootSerializable","sharedStorageWritable",...Dn]),Un=new Set(["innerHTML","textContent","innerText","children"]),Fn=Object.assign(Object.create(null),{className:"class",htmlFor:"for"}),Wn=Object.assign(Object.create(null),{class:"className",novalidate:{$:"noValidate",FORM:1},formnovalidate:{$:"formNoValidate",BUTTON:1,INPUT:1},ismap:{$:"isMap",IMG:1},nomodule:{$:"noModule",SCRIPT:1},playsinline:{$:"playsInline",VIDEO:1},readonly:{$:"readOnly",INPUT:1,TEXTAREA:1},adauctionheaders:{$:"adAuctionHeaders",IFRAME:1},allowfullscreen:{$:"allowFullscreen",IFRAME:1},browsingtopics:{$:"browsingTopics",IMG:1},defaultchecked:{$:"defaultChecked",INPUT:1},defaultmuted:{$:"defaultMuted",AUDIO:1,VIDEO:1},defaultselected:{$:"defaultSelected",OPTION:1},disablepictureinpicture:{$:"disablePictureInPicture",VIDEO:1},disableremoteplayback:{$:"disableRemotePlayback",AUDIO:1,VIDEO:1},preservespitch:{$:"preservesPitch",AUDIO:1,VIDEO:1},shadowrootclonable:{$:"shadowRootClonable",TEMPLATE:1},shadowrootdelegatesfocus:{$:"shadowRootDelegatesFocus",TEMPLATE:1},shadowrootserializable:{$:"shadowRootSerializable",TEMPLATE:1},sharedstoragewritable:{$:"sharedStorageWritable",IFRAME:1,IMG:1}});function qn(r,e){const t=Wn[r];return typeof t=="object"?t[e]?t.$:void 0:t}const Vn=new Set(["beforeinput","click","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"]),Cr=r=>P(()=>r());function zn(r,e,t){let n=t.length,s=e.length,i=n,o=0,a=0,c=e[s-1].nextSibling,l=null;for(;o<s||a<i;){if(e[o]===t[a]){o++,a++;continue}for(;e[s-1]===t[i-1];)s--,i--;if(s===o){const u=i<n?a?t[a-1].nextSibling:t[i-a]:c;for(;a<i;)r.insertBefore(t[a++],u)}else if(i===a)for(;o<s;)(!l||!l.has(e[o]))&&e[o].remove(),o++;else if(e[o]===t[i-1]&&t[a]===e[s-1]){const u=e[--s].nextSibling;r.insertBefore(t[a++],e[o++].nextSibling),r.insertBefore(t[--i],u),e[s]=t[i]}else{if(!l){l=new Map;let h=a;for(;h<i;)l.set(t[h],h++)}const u=l.get(e[o]);if(u!=null)if(a<u&&u<i){let h=o,d=1,g;for(;++h<s&&h<i&&!((g=l.get(e[h]))==null||g!==u+d);)d++;if(d>u-a){const w=e[o];for(;a<u;)r.insertBefore(t[a++],w)}else r.replaceChild(t[a++],e[o++])}else o++;else e[o++].remove()}}}const rr="_$DX_DELEGATE";function Hn(r,e,t,n={}){let s;return Ee(i=>{s=i,e===document?r():H(e,r(),e.firstChild?null:void 0,t)},n.owner),()=>{s(),e.textContent=""}}function K(r,e,t,n){let s;const i=()=>{const a=document.createElement("template");return a.innerHTML=r,a.content.firstChild},o=()=>(s||(s=i())).cloneNode(!0);return o.cloneNode=o,o}function Ut(r,e=window.document){const t=e[rr]||(e[rr]=new Set);for(let n=0,s=r.length;n<s;n++){const i=r[n];t.has(i)||(t.add(i),e.addEventListener(i,Zn))}}function Le(r,e,t){Me(r)||(t==null?r.removeAttribute(e):r.setAttribute(e,t))}function jn(r,e,t){Me(r)||(t?r.setAttribute(e,""):r.removeAttribute(e))}function Kn(r,e){Me(r)||(e==null?r.removeAttribute("class"):r.className=e)}function Pr(r,e,t,n){if(n)Array.isArray(t)?(r[`$$${e}`]=t[0],r[`$$${e}Data`]=t[1]):r[`$$${e}`]=t;else if(Array.isArray(t)){const s=t[0];r.addEventListener(e,t[0]=i=>s.call(r,t[1],i))}else r.addEventListener(e,t,typeof t!="function"&&t)}function Yn(r,e,t={}){const n=Object.keys(e||{}),s=Object.keys(t);let i,o;for(i=0,o=s.length;i<o;i++){const a=s[i];!a||a==="undefined"||e[a]||(nr(r,a,!1),delete t[a])}for(i=0,o=n.length;i<o;i++){const a=n[i],c=!!e[a];!a||a==="undefined"||t[a]===c||!c||(nr(r,a,!0),t[a]=c)}return t}function Or(r,e,t){if(!e)return t?Le(r,"style"):e;const n=r.style;if(typeof e=="string")return n.cssText=e;typeof t=="string"&&(n.cssText=t=void 0),t||(t={}),e||(e={});let s,i;for(i in t)e[i]==null&&n.removeProperty(i),delete t[i];for(i in e)s=e[i],s!==t[i]&&(n.setProperty(i,s),t[i]=s);return t}function f(r,e,t){t!=null?r.style.setProperty(e,t):r.style.removeProperty(e)}function Gn(r,e={},t,n){const s={};return M(()=>s.children=Ie(r,e.children,s.children)),M(()=>typeof e.ref=="function"&&Qn(e.ref,r)),M(()=>Xn(r,e,t,!0,s,!0)),s}function Qn(r,e,t){return V(()=>r(e,t))}function H(r,e,t,n){if(t!==void 0&&!n&&(n=[]),typeof e!="function")return Ie(r,e,n,t);M(s=>Ie(r,e(),s,t),n)}function Xn(r,e,t,n,s={},i=!1){e||(e={});for(const o in s)if(!(o in e)){if(o==="children")continue;s[o]=sr(r,o,null,s[o],t,i,e)}for(const o in e){if(o==="children")continue;const a=e[o];s[o]=sr(r,o,a,s[o],t,i,e)}}function Me(r){return!!v.context&&!v.done&&(!r||r.isConnected)}function Jn(r){return r.toLowerCase().replace(/-([a-z])/g,(e,t)=>t.toUpperCase())}function nr(r,e,t){const n=e.trim().split(/\s+/);for(let s=0,i=n.length;s<i;s++)r.classList.toggle(n[s],t)}function sr(r,e,t,n,s,i,o){let a,c,l,u,h;if(e==="style")return Or(r,t,n);if(e==="classList")return Yn(r,t,n);if(t===n)return n;if(e==="ref")i||t(r);else if(e.slice(0,3)==="on:"){const d=e.slice(3);n&&r.removeEventListener(d,n,typeof n!="function"&&n),t&&r.addEventListener(d,t,typeof t!="function"&&t)}else if(e.slice(0,10)==="oncapture:"){const d=e.slice(10);n&&r.removeEventListener(d,n,!0),t&&r.addEventListener(d,t,!0)}else if(e.slice(0,2)==="on"){const d=e.slice(2).toLowerCase(),g=Vn.has(d);if(!g&&n){const w=Array.isArray(n)?n[0]:n;r.removeEventListener(d,w)}(g||t)&&(Pr(r,d,t,g),g&&Ut([d]))}else if(e.slice(0,5)==="attr:")Le(r,e.slice(5),t);else if(e.slice(0,5)==="bool:")jn(r,e.slice(5),t);else if((h=e.slice(0,5)==="prop:")||(l=Un.has(e))||(u=qn(e,r.tagName))||(c=$n.has(e))||(a=r.nodeName.includes("-")||"is"in o)){if(h)e=e.slice(5),c=!0;else if(Me(r))return t;e==="class"||e==="className"?Kn(r,t):a&&!c&&!l?r[Jn(e)]=t:r[u||e]=t}else Le(r,Fn[e]||e,t);return t}function Zn(r){if(v.registry&&v.events&&v.events.find(([c,l])=>l===r))return;let e=r.target;const t=`$$${r.type}`,n=r.target,s=r.currentTarget,i=c=>Object.defineProperty(r,"target",{configurable:!0,value:c}),o=()=>{const c=e[t];if(c&&!e.disabled){const l=e[`${t}Data`];if(l!==void 0?c.call(e,l,r):c.call(e,r),r.cancelBubble)return}return e.host&&typeof e.host!="string"&&!e.host._$host&&e.contains(r.target)&&i(e.host),!0},a=()=>{for(;o()&&(e=e._$host||e.parentNode||e.host););};if(Object.defineProperty(r,"currentTarget",{configurable:!0,get(){return e||document}}),v.registry&&!v.done&&(v.done=_$HY.done=!0),r.composedPath){const c=r.composedPath();i(c[0]);for(let l=0;l<c.length-2&&(e=c[l],!!o());l++){if(e._$host){e=e._$host,a();break}if(e.parentNode===s)break}}else a();i(n)}function Ie(r,e,t,n,s){const i=Me(r);if(i){!t&&(t=[...r.childNodes]);let c=[];for(let l=0;l<t.length;l++){const u=t[l];u.nodeType===8&&u.data.slice(0,2)==="!$"?u.remove():c.push(u)}t=c}for(;typeof t=="function";)t=t();if(e===t)return t;const o=typeof e,a=n!==void 0;if(r=a&&t[0]&&t[0].parentNode||r,o==="string"||o==="number"){if(i||o==="number"&&(e=e.toString(),e===t))return t;if(a){let c=t[0];c&&c.nodeType===3?c.data!==e&&(c.data=e):c=document.createTextNode(e),t=Se(r,t,n,c)}else t!==""&&typeof t=="string"?t=r.firstChild.data=e:t=r.textContent=e}else if(e==null||o==="boolean"){if(i)return t;t=Se(r,t,n)}else{if(o==="function")return M(()=>{let c=e();for(;typeof c=="function";)c=c();t=Ie(r,c,t,n)}),()=>t;if(Array.isArray(e)){const c=[],l=t&&Array.isArray(t);if(Et(c,e,t,s))return M(()=>t=Ie(r,c,t,n,!0)),()=>t;if(i){if(!c.length)return t;if(n===void 0)return t=[...r.childNodes];let u=c[0];if(u.parentNode!==r)return t;const h=[u];for(;(u=u.nextSibling)!==n;)h.push(u);return t=h}if(c.length===0){if(t=Se(r,t,n),a)return t}else l?t.length===0?ir(r,c,n):zn(r,t,c):(t&&Se(r),ir(r,c));t=c}else if(e.nodeType){if(i&&e.parentNode)return t=a?[e]:e;if(Array.isArray(t)){if(a)return t=Se(r,t,n,e);Se(r,t,null,e)}else t==null||t===""||!r.firstChild?r.appendChild(e):r.replaceChild(e,r.firstChild);t=e}}return t}function Et(r,e,t,n){let s=!1;for(let i=0,o=e.length;i<o;i++){let a=e[i],c=t&&t[r.length],l;if(!(a==null||a===!0||a===!1))if((l=typeof a)=="object"&&a.nodeType)r.push(a);else if(Array.isArray(a))s=Et(r,a,c)||s;else if(l==="function")if(n){for(;typeof a=="function";)a=a();s=Et(r,Array.isArray(a)?a:[a],Array.isArray(c)?c:[c])||s}else r.push(a),s=!0;else{const u=String(a);c&&c.nodeType===3&&c.data===u?r.push(c):r.push(document.createTextNode(u))}}return s}function ir(r,e,t=null){for(let n=0,s=e.length;n<s;n++)r.insertBefore(e[n],t)}function Se(r,e,t,n){if(t===void 0)return r.textContent="";const s=n||document.createTextNode("");if(e.length){let i=!1;for(let o=e.length-1;o>=0;o--){const a=e[o];if(s!==a){const c=a.parentNode===r;!i&&!o?c?r.replaceChild(s,a):r.insertBefore(s,t):c&&a.remove()}else i=!0}}else r.insertBefore(s,t);return[s]}const es=!1,ts="modulepreload",rs=function(r){return"/"+r},or={},me=function(e,t,n){let s=Promise.resolve();if(t&&t.length>0){document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),a=o?.nonce||o?.getAttribute("nonce");s=Promise.allSettled(t.map(c=>{if(c=rs(c),c in or)return;or[c]=!0;const l=c.endsWith(".css"),u=l?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${u}`))return;const h=document.createElement("link");if(h.rel=l?"stylesheet":ts,l||(h.as="script"),h.crossOrigin="",h.href=c,a&&h.setAttribute("nonce",a),document.head.appendChild(h),l)return new Promise((d,g)=>{h.addEventListener("load",d),h.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${c}`)))})}))}function i(o){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=o,window.dispatchEvent(a),!a.defaultPrevented)throw o}return s.then(o=>{for(const a of o||[])a.status==="rejected"&&i(a.reason);return e().catch(i)})};function Lr(){let r=new Set;function e(s){return r.add(s),()=>r.delete(s)}let t=!1;function n(s,i){if(t)return!(t=!1);const o={to:s,options:i,defaultPrevented:!1,preventDefault:()=>o.defaultPrevented=!0};for(const a of r)a.listener({...o,from:a.location,retry:c=>{c&&(t=!0),a.navigate(s,{...i,resolve:!1})}});return!o.defaultPrevented}return{subscribe:e,confirm:n}}let At;function Ft(){(!window.history.state||window.history.state._depth==null)&&window.history.replaceState({...window.history.state,_depth:window.history.length-1},""),At=window.history.state._depth}Ft();function ns(r){return{...r,_depth:window.history.state&&window.history.state._depth}}function ss(r,e){let t=!1;return()=>{const n=At;Ft();const s=n==null?null:At-n;if(t){t=!1;return}s&&e(s)?(t=!0,window.history.go(-s)):r()}}const is=/^(?:[a-z0-9]+:)?\/\//i,os=/^\/+|(\/)\/+$/g,Ir="http://sr";function ue(r,e=!1){const t=r.replace(os,"$1");return t?e||/^[?#]/.test(t)?t:"/"+t:""}function He(r,e,t){if(is.test(e))return;const n=ue(r),s=t&&ue(t);let i="";return!s||e.startsWith("/")?i=n:s.toLowerCase().indexOf(n.toLowerCase())!==0?i=n+s:i=s,(i||"/")+ue(e,!i)}function as(r,e){if(r==null)throw new Error(e);return r}function cs(r,e){return ue(r).replace(/\/*(\*.*)?$/g,"")+ue(e)}function Nr(r){const e={};return r.searchParams.forEach((t,n)=>{e[n]=t}),e}function ls(r,e,t){const[n,s]=r.split("/*",2),i=n.split("/").filter(Boolean),o=i.length;return a=>{const c=a.split("/").filter(Boolean),l=c.length-o;if(l<0||l>0&&s===void 0&&!e)return null;const u={path:o?"":"/",params:{}},h=d=>t===void 0?void 0:t[d];for(let d=0;d<o;d++){const g=i[d],w=c[d],p=g[0]===":",y=p?g.slice(1):g;if(p&&dt(w,h(y)))u.params[y]=w;else if(p||!dt(w,g))return null;u.path+=`/${w}`}if(s){const d=l?c.slice(-l).join("/"):"";if(dt(d,h(s)))u.params[s]=d;else return null}return u}}function dt(r,e){const t=n=>n.localeCompare(r,void 0,{sensitivity:"base"})===0;return e===void 0?!0:typeof e=="string"?t(e):typeof e=="function"?e(r):Array.isArray(e)?e.some(t):e instanceof RegExp?e.test(r):!1}function us(r){const[e,t]=r.pattern.split("/*",2),n=e.split("/").filter(Boolean);return n.reduce((s,i)=>s+(i.startsWith(":")?2:3),n.length-(t===void 0?0:1))}function Mr(r){const e=new Map,t=Mt();return new Proxy({},{get(n,s){return e.has(s)||Bt(t,()=>e.set(s,P(()=>r()[s]))),e.get(s)()},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}},ownKeys(){return Reflect.ownKeys(r())}})}function Br(r){let e=/(\/?\:[^\/]+)\?/.exec(r);if(!e)return[r];let t=r.slice(0,e.index),n=r.slice(e.index+e[0].length);const s=[t,t+=e[1]];for(;e=/^(\/\:[^\/]+)\?/.exec(n);)s.push(t+=e[1]),n=n.slice(e[0].length);return Br(n).reduce((i,o)=>[...i,...s.map(a=>a+o)],[])}const hs=100,Dr=fe(),Wt=fe(),it=()=>as(ge(Dr),"<A> and 'use' router primitives can be only used inside a Route."),ds=()=>ge(Wt)||it().base,fs=r=>{const e=ds();return P(()=>e.resolvePath(r()))},gs=r=>{const e=it();return P(()=>{const t=r();return t!==void 0?e.renderPath(t):t})},ps=()=>it().navigatorFactory(),$r=()=>it().location;function ms(r,e=""){const{component:t,load:n,children:s,info:i}=r,o=!s||Array.isArray(s)&&!s.length,a={key:r,component:t,load:n,info:i};return Ur(r.path).reduce((c,l)=>{for(const u of Br(l)){const h=cs(e,u);let d=o?h:h.split("/*",1)[0];d=d.split("/").map(g=>g.startsWith(":")||g.startsWith("*")?g:encodeURIComponent(g)).join("/"),c.push({...a,originalPath:l,pattern:d,matcher:ls(d,!o,r.matchFilters)})}return c},[])}function ys(r,e=0){return{routes:r,score:us(r[r.length-1])*1e4-e,matcher(t){const n=[];for(let s=r.length-1;s>=0;s--){const i=r[s],o=i.matcher(t);if(!o)return null;n.unshift({...o,route:i})}return n}}}function Ur(r){return Array.isArray(r)?r:[r]}function Fr(r,e="",t=[],n=[]){const s=Ur(r);for(let i=0,o=s.length;i<o;i++){const a=s[i];if(a&&typeof a=="object"){a.hasOwnProperty("path")||(a.path="");const c=ms(a,e);for(const l of c){t.push(l);const u=Array.isArray(a.children)&&a.children.length===0;if(a.children&&!u)Fr(a.children,l.pattern,t,n);else{const h=ys([...t],n.length);n.push(h)}t.pop()}}}return t.length?n:n.sort((i,o)=>o.score-i.score)}function ft(r,e){for(let t=0,n=r.length;t<n;t++){const s=r[t].matcher(e);if(s)return s}return[]}function bs(r,e){const t=new URL(Ir),n=P(c=>{const l=r();try{return new URL(l,t)}catch{return console.error(`Invalid path ${l}`),c}},t,{equals:(c,l)=>c.href===l.href}),s=P(()=>n().pathname),i=P(()=>n().search,!0),o=P(()=>n().hash),a=()=>"";return{get pathname(){return s()},get search(){return i()},get hash(){return o()},get state(){return e()},get key(){return a()},query:Mr(Nt(i,()=>Nr(n())))}}let ce;function ws(){return ce}function Ss(r,e,t,n={}){const{signal:[s,i],utils:o={}}=r,a=o.parsePath||(_=>_),c=o.renderPath||(_=>_),l=o.beforeLeave||Lr(),u=He("",n.base||"");if(u===void 0)throw new Error(`${u} is not a valid base path`);u&&!s().value&&i({value:u,replace:!0,scroll:!1});const[h,d]=D(!1);let g;const w=(_,E)=>{E.value===p()&&E.state===k()||(g===void 0&&d(!0),ce=_,g=E,En(()=>{g===E&&(y(g.value),S(g.state),$[1]([]))}).finally(()=>{g===E&&vn(()=>{ce=void 0,_==="navigate"&&O(g),d(!1),g=void 0})}))},[p,y]=D(s().value),[k,S]=D(s().state),N=bs(p,k),L=[],$=D([]),q=P(()=>typeof n.transformUrl=="function"?ft(e(),n.transformUrl(N.pathname)):ft(e(),N.pathname)),Be=Mr(()=>{const _=q(),E={};for(let U=0;U<_.length;U++)Object.assign(E,_[U].params);return E}),be={pattern:u,path:()=>u,outlet:()=>null,resolvePath(_){return He(u,_)}};return M(Nt(s,_=>w("native",_),{defer:!0})),{base:be,location:N,params:Be,isRouting:h,renderPath:c,parsePath:a,navigatorFactory:De,matches:q,beforeLeave:l,preloadRoute:I,singleFlight:n.singleFlight===void 0?!0:n.singleFlight,submissions:$};function Re(_,E,U){V(()=>{if(typeof E=="number"){E&&(o.go?o.go(E):console.warn("Router integration does not support relative routing"));return}const{replace:ct,resolve:lt,scroll:we,state:$e}={replace:!1,resolve:!0,scroll:!0,...U},ae=lt?_.resolvePath(E):He("",E);if(ae===void 0)throw new Error(`Path '${E}' is not a routable path`);if(L.length>=hs)throw new Error("Too many redirects");const Qt=p();(ae!==Qt||$e!==k())&&(es||l.confirm(ae,U)&&(L.push({value:Qt,replace:ct,scroll:we,state:k()}),w("navigate",{value:ae,state:$e})))})}function De(_){return _=_||ge(Wt)||be,(E,U)=>Re(_,E,U)}function O(_){const E=L[0];E&&(i({..._,replace:E.replace,scroll:E.scroll}),L.length=0)}function I(_,E={}){const U=ft(e(),_.pathname),ct=ce;ce="preload";for(let lt in U){const{route:we,params:$e}=U[lt];we.component&&we.component.preload&&we.component.preload();const{load:ae}=we;E.preloadData&&ae&&Bt(t(),()=>ae({params:$e,location:{pathname:_.pathname,search:_.search,hash:_.hash,query:Nr(_),state:null,key:""},intent:"preload"}))}ce=ct}}function ks(r,e,t,n){const{base:s,location:i,params:o}=r,{pattern:a,component:c,load:l}=n().route,u=P(()=>n().path);c&&c.preload&&c.preload();const h=l?l({params:o,location:i,intent:ce||"initial"}):void 0;return{parent:e,pattern:a,path:u,outlet:()=>c?b(c,{params:o,location:i,data:h,get children(){return t()}}):t(),resolvePath(g){return He(s.path(),g,u())}}}const vs=r=>e=>{const{base:t}=e,n=Dt(()=>e.children),s=P(()=>Fr(n(),e.base||""));let i;const o=Ss(r,s,()=>i,{base:t,singleFlight:e.singleFlight,transformUrl:e.transformUrl});return r.create&&r.create(o),b(Dr.Provider,{value:o,get children(){return b(_s,{routerState:o,get root(){return e.root},get load(){return e.rootLoad},get children(){return[Cr(()=>(i=Mt())&&null),b(Es,{routerState:o,get branches(){return s()}})]}})}})};function _s(r){const e=r.routerState.location,t=r.routerState.params,n=P(()=>r.load&&V(()=>{r.load({params:t,location:e,intent:ws()||"initial"})}));return b(st,{get when(){return r.root},keyed:!0,get fallback(){return r.children},children:s=>b(s,{params:t,location:e,get data(){return n()},get children(){return r.children}})})}function Es(r){const e=[];let t;const n=P(Nt(r.routerState.matches,(s,i,o)=>{let a=i&&s.length===i.length;const c=[];for(let l=0,u=s.length;l<u;l++){const h=i&&i[l],d=s[l];o&&h&&d.route.key===h.route.key?c[l]=o[l]:(a=!1,e[l]&&e[l](),Ee(g=>{e[l]=g,c[l]=ks(r.routerState,c[l-1]||r.routerState.base,ar(()=>n()[l+1]),()=>r.routerState.matches()[l])}))}return e.splice(s.length).forEach(l=>l()),o&&a?o:(t=c[0],c)}));return ar(()=>n()&&t)()}const ar=r=>()=>b(st,{get when(){return r()},keyed:!0,children:e=>b(Wt.Provider,{value:e,get children(){return e.outlet()}})}),re=r=>{const e=Dt(()=>r.children);return _t(r,{get children(){return e()}})};function As([r,e],t,n){return[r,n?s=>e(n(s)):e]}function xs(r){if(r==="#")return null;try{return document.querySelector(r)}catch{return null}}function Ts(r){let e=!1;const t=s=>typeof s=="string"?{value:s}:s,n=As(D(t(r.get()),{equals:(s,i)=>s.value===i.value&&s.state===i.state}),void 0,s=>(!e&&r.set(s),s));return r.init&&Ne(r.init((s=r.get())=>{e=!0,n[1](t(s)),e=!1})),vs({signal:n,create:r.create,utils:r.utils})}function Rs(r,e,t){return r.addEventListener(e,t),()=>r.removeEventListener(e,t)}function Cs(r,e){const t=xs(`#${r}`);t?t.scrollIntoView():e&&window.scrollTo(0,0)}const Ps=new Map;function Os(r=!0,e=!1,t="/_server",n){return s=>{const i=s.base.path(),o=s.navigatorFactory(s.base);let a={};function c(p){return p.namespaceURI==="http://www.w3.org/2000/svg"}function l(p){if(p.defaultPrevented||p.button!==0||p.metaKey||p.altKey||p.ctrlKey||p.shiftKey)return;const y=p.composedPath().find(q=>q instanceof Node&&q.nodeName.toUpperCase()==="A");if(!y||e&&!y.hasAttribute("link"))return;const k=c(y),S=k?y.href.baseVal:y.href;if((k?y.target.baseVal:y.target)||!S&&!y.hasAttribute("state"))return;const L=(y.getAttribute("rel")||"").split(/\s+/);if(y.hasAttribute("download")||L&&L.includes("external"))return;const $=k?new URL(S,document.baseURI):new URL(S);if(!($.origin!==window.location.origin||i&&$.pathname&&!$.pathname.toLowerCase().startsWith(i.toLowerCase())))return[y,$]}function u(p){const y=l(p);if(!y)return;const[k,S]=y,N=s.parsePath(S.pathname+S.search+S.hash),L=k.getAttribute("state");p.preventDefault(),o(N,{resolve:!1,replace:k.hasAttribute("replace"),scroll:!k.hasAttribute("noscroll"),state:L&&JSON.parse(L)})}function h(p){const y=l(p);if(!y)return;const[k,S]=y;typeof n=="function"&&(S.pathname=n(S.pathname)),a[S.pathname]||s.preloadRoute(S,{preloadData:k.getAttribute("preload")!=="false"})}function d(p){const y=l(p);if(!y)return;const[k,S]=y;typeof n=="function"&&(S.pathname=n(S.pathname)),!a[S.pathname]&&(a[S.pathname]=setTimeout(()=>{s.preloadRoute(S,{preloadData:k.getAttribute("preload")!=="false"}),delete a[S.pathname]},200))}function g(p){const y=l(p);if(!y)return;const[,k]=y;typeof n=="function"&&(k.pathname=n(k.pathname)),a[k.pathname]&&(clearTimeout(a[k.pathname]),delete a[k.pathname])}function w(p){if(p.defaultPrevented)return;let y=p.submitter&&p.submitter.hasAttribute("formaction")?p.submitter.getAttribute("formaction"):p.target.getAttribute("action");if(!y)return;if(!y.startsWith("https://action/")){const S=new URL(y,Ir);if(y=s.parsePath(S.pathname+S.search),!y.startsWith(t))return}if(p.target.method.toUpperCase()!=="POST")throw new Error("Only POST forms are supported for Actions");const k=Ps.get(y);if(k){p.preventDefault();const S=new FormData(p.target);p.submitter&&p.submitter.name&&S.append(p.submitter.name,p.submitter.value),k.call({r:s,f:p.target},S)}}Ut(["click","submit"]),document.addEventListener("click",u),r&&(document.addEventListener("mouseover",d),document.addEventListener("mouseout",g),document.addEventListener("focusin",h),document.addEventListener("touchstart",h)),document.addEventListener("submit",w),Ne(()=>{document.removeEventListener("click",u),r&&(document.removeEventListener("mouseover",d),document.removeEventListener("mouseout",g),document.removeEventListener("focusin",h),document.removeEventListener("touchstart",h)),document.removeEventListener("submit",w)})}}function Ls(r){const e=()=>{const n=window.location.pathname+window.location.search;return{value:r.transformUrl?r.transformUrl(n)+window.location.hash:n+window.location.hash,state:window.history.state}},t=Lr();return Ts({get:e,set({value:n,replace:s,scroll:i,state:o}){s?window.history.replaceState(ns(o),"",n):window.history.pushState(o,"",n),Cs(decodeURIComponent(window.location.hash.slice(1)),i),Ft()},init:n=>Rs(window,"popstate",ss(n,s=>{if(s&&s<0)return!t.confirm(s);{const i=e();return!t.confirm(i.value,{state:i.state})}})),create:Os(r.preload,r.explicitLinks,r.actionBase,r.transformUrl),utils:{go:n=>window.history.go(n),beforeLeave:t}})(r)}var Is=K("<a>");function gt(r){r=_t({inactiveClass:"inactive",activeClass:"active"},r);const[,e]=In(r,["href","state","class","activeClass","inactiveClass","end"]),t=fs(()=>r.href),n=gs(t),s=$r(),i=P(()=>{const o=t();if(o===void 0)return[!1,!1];const a=ue(o.split(/[?#]/,1)[0]).toLowerCase(),c=ue(s.pathname).toLowerCase();return[r.end?a===c:c.startsWith(a+"/")||c===a,a===c]});return(()=>{var o=Is();return Gn(o,_t(e,{get href(){return n()||r.href},get state(){return JSON.stringify(r.state)},get classList(){return{...r.class&&{[r.class]:!0},[r.inactiveClass]:!i()[0],[r.activeClass]:i()[0],...e.classList}},link:"",get"aria-current"(){return i()[1]?"page":void 0}}),!1),o})()}const Wr=fe();function Ns(r){const e=()=>{if(typeof window<"u"){const c=localStorage.getItem("theme");if(c)return c;if(window.matchMedia)return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}return"light"},[t,n]=D(e()),s=c=>{n(c),typeof window<"u"&&localStorage.setItem("theme",c),o(c)},i=()=>{s(t()==="light"?"dark":"light")},o=c=>{if(typeof document<"u"){const l=document.documentElement;c==="dark"?(l.classList.add("dark"),l.classList.remove("light")):(l.classList.add("light"),l.classList.remove("dark"));const u=document.querySelector('meta[name="theme-color"]');u&&u.setAttribute("content",c==="dark"?"#111827":"#3b82f6")}};_n(()=>{if(o(t()),typeof window<"u"&&window.matchMedia){const c=window.matchMedia("(prefers-color-scheme: dark)"),l=u=>{localStorage.getItem("theme")||s(u.matches?"dark":"light")};return c.addEventListener("change",l),()=>c.removeEventListener("change",l)}});const a={theme:t,setTheme:s,toggleTheme:i};return b(Wr.Provider,{value:a,get children(){return r.children}})}function Ms(){const r=ge(Wr);if(!r)throw new Error("useTheme must be used within a ThemeProvider");return r}const Bs={common:{loading:"加载中...",error:"错误",success:"成功",warning:"警告",info:"信息",confirm:"确认",cancel:"取消",save:"保存",delete:"删除",edit:"编辑",add:"添加",search:"搜索",refresh:"刷新",back:"返回",next:"下一步",previous:"上一步",submit:"提交",reset:"重置",close:"关闭",open:"打开",yes:"是",no:"否"},auth:{login:"登录",logout:"退出登录",register:"注册",username:"用户名",password:"密码",confirmPassword:"确认密码",email:"邮箱",forgotPassword:"忘记密码",rememberMe:"记住我",loginSuccess:"登录成功",loginFailed:"登录失败",logoutSuccess:"退出成功",invalidCredentials:"用户名或密码错误",slideToVerify:"向右滑动完成验证",verifySuccess:"验证成功",verifyFailed:"验证失败，请重试"},navigation:{dashboard:"仪表盘",market:"市场数据",strategy:"策略管理",backtest:"回测分析",trading:"交易",portfolio:"投资组合",risk:"风险管理",settings:"设置",profile:"个人资料"},market:{symbol:"代码",name:"名称",price:"价格",change:"涨跌",changePercent:"涨跌幅",volume:"成交量",high:"最高",low:"最低",open:"开盘",close:"收盘",marketCap:"市值",pe:"市盈率",watchlist:"自选股",addToWatchlist:"加入自选",removeFromWatchlist:"移出自选"},strategy:{strategies:"策略",createStrategy:"创建策略",editStrategy:"编辑策略",deleteStrategy:"删除策略",strategyName:"策略名称",strategyDescription:"策略描述",strategyType:"策略类型",strategyStatus:"策略状态",strategyCode:"策略代码",parameters:"参数",performance:"表现",totalReturn:"总收益",sharpeRatio:"夏普比率",maxDrawdown:"最大回撤",winRate:"胜率",startStrategy:"启动策略",stopStrategy:"停止策略",pauseStrategy:"暂停策略"},backtest:{backtest:"回测",createBacktest:"创建回测",backtestResults:"回测结果",startDate:"开始日期",endDate:"结束日期",initialCapital:"初始资金",finalCapital:"最终资金",trades:"交易次数",runBacktest:"运行回测",backtestRunning:"回测运行中",backtestCompleted:"回测完成",backtestFailed:"回测失败"},chart:{indicators:"技术指标",timeframe:"时间周期",candlestick:"K线图",line:"线图",area:"面积图",volume:"成交量",ma:"移动平均线",ema:"指数移动平均线",macd:"MACD",rsi:"RSI",bollinger:"布林带",kd:"KD指标"},error:{networkError:"网络错误",serverError:"服务器错误",unauthorized:"未授权访问",forbidden:"访问被拒绝",notFound:"页面未找到",validationError:"验证错误",unknownError:"未知错误"}},Ds={common:{loading:"Loading...",error:"Error",success:"Success",warning:"Warning",info:"Info",confirm:"Confirm",cancel:"Cancel",save:"Save",delete:"Delete",edit:"Edit",add:"Add",search:"Search",refresh:"Refresh",back:"Back",next:"Next",previous:"Previous",submit:"Submit",reset:"Reset",close:"Close",open:"Open",yes:"Yes",no:"No"},auth:{login:"Login",logout:"Logout",register:"Register",username:"Username",password:"Password",confirmPassword:"Confirm Password",email:"Email",forgotPassword:"Forgot Password",rememberMe:"Remember Me",loginSuccess:"Login Successful",loginFailed:"Login Failed",logoutSuccess:"Logout Successful",invalidCredentials:"Invalid username or password",slideToVerify:"Slide to verify",verifySuccess:"Verification successful",verifyFailed:"Verification failed, please try again"},navigation:{dashboard:"Dashboard",market:"Market Data",strategy:"Strategy",backtest:"Backtest",trading:"Trading",portfolio:"Portfolio",risk:"Risk Management",settings:"Settings",profile:"Profile"},market:{symbol:"Symbol",name:"Name",price:"Price",change:"Change",changePercent:"Change %",volume:"Volume",high:"High",low:"Low",open:"Open",close:"Close",marketCap:"Market Cap",pe:"P/E Ratio",watchlist:"Watchlist",addToWatchlist:"Add to Watchlist",removeFromWatchlist:"Remove from Watchlist"},strategy:{strategies:"Strategies",createStrategy:"Create Strategy",editStrategy:"Edit Strategy",deleteStrategy:"Delete Strategy",strategyName:"Strategy Name",strategyDescription:"Strategy Description",strategyType:"Strategy Type",strategyStatus:"Strategy Status",strategyCode:"Strategy Code",parameters:"Parameters",performance:"Performance",totalReturn:"Total Return",sharpeRatio:"Sharpe Ratio",maxDrawdown:"Max Drawdown",winRate:"Win Rate",startStrategy:"Start Strategy",stopStrategy:"Stop Strategy",pauseStrategy:"Pause Strategy"},backtest:{backtest:"Backtest",createBacktest:"Create Backtest",backtestResults:"Backtest Results",startDate:"Start Date",endDate:"End Date",initialCapital:"Initial Capital",finalCapital:"Final Capital",trades:"Trades",runBacktest:"Run Backtest",backtestRunning:"Backtest Running",backtestCompleted:"Backtest Completed",backtestFailed:"Backtest Failed"},chart:{indicators:"Indicators",timeframe:"Timeframe",candlestick:"Candlestick",line:"Line",area:"Area",volume:"Volume",ma:"Moving Average",ema:"Exponential Moving Average",macd:"MACD",rsi:"RSI",bollinger:"Bollinger Bands",kd:"KD Indicator"},error:{networkError:"Network Error",serverError:"Server Error",unauthorized:"Unauthorized",forbidden:"Forbidden",notFound:"Not Found",validationError:"Validation Error",unknownError:"Unknown Error"}},$s={"zh-CN":Bs,"en-US":Ds},Us=fe();function Fs(r){const e=()=>{if(typeof window<"u"){const c=localStorage.getItem("language");if(c&&["zh-CN","en-US"].includes(c))return c;if(navigator.language.startsWith("zh"))return"zh-CN"}return r.defaultLanguage||"zh-CN"},[t,n]=D(e()),a={language:t,setLanguage:c=>{n(c),typeof window<"u"&&localStorage.setItem("language",c)},t:(c,l)=>{const u=t(),h=$s[u],d=c.split(".");let g=h;for(const w of d)if(g&&typeof g=="object"&&w in g)g=g[w];else return console.warn(`Translation key not found: ${c} for language: ${u}`),c;return typeof g!="string"?(console.warn(`Translation value is not a string: ${c}`),c):l?g.replace(/\{\{(\w+)\}\}/g,(w,p)=>l[p]?.toString()||w):g},availableLanguages:["zh-CN","en-US"]};return b(Us.Provider,{value:a,get children(){return r.children}})}const Ws=fe();function qs(r){return b(Ws.Provider,{value:{},get children(){return r.children}})}class Vs{constructor(e={}){this.baseURL=e.baseURL||"https://api.yourdomain.com",this.timeout=e.timeout||1e4,this.defaultHeaders={"Content-Type":"application/json",...e.headers}}buildURL(e,t){const n=e.startsWith("http")?e:`${this.baseURL}${e}`;if(!t)return n;const s=new URLSearchParams;Object.entries(t).forEach(([o,a])=>{a!=null&&s.append(o,String(a))});const i=s.toString();return i?`${n}?${i}`:n}async handleResponse(e){const n=e.headers.get("content-type")?.includes("application/json");let s;try{s=n?await e.json():await e.text()}catch{throw new Error("响应解析失败")}if(!e.ok)throw{code:e.status,message:s.message||s.error||`HTTP ${e.status}`,details:s};return typeof s=="object"&&s!==null?{success:!0,data:s.data||s,message:s.message,code:e.status,timestamp:Date.now()}:{success:!0,data:s,code:e.status,timestamp:Date.now()}}async request(e){const{url:t,method:n="GET",params:s,data:i,headers:o={},timeout:a=this.timeout,retries:c=3,retryDelay:l=1e3}=e,u={...this.defaultHeaders,...o},h=typeof window<"u"?localStorage.getItem("access_token"):null;h&&(u.Authorization=`Bearer ${h}`);const d={method:n,headers:u,signal:AbortSignal.timeout(a)};i&&n!=="GET"&&(i instanceof FormData?(delete u["Content-Type"],d.body=i):d.body=JSON.stringify(i));const g=this.buildURL(t,n==="GET"?s:void 0);let w;for(let p=1;p<=c;p++)try{const y=await fetch(g,d);return await this.handleResponse(y)}catch(y){if(w=y,p===c)break;await new Promise(k=>setTimeout(k,l*p))}throw w}async get(e,t,n){return this.request({url:e,method:"GET",params:t,headers:n})}async post(e,t,n){return this.request({url:e,method:"POST",data:t,headers:n})}async put(e,t,n){return this.request({url:e,method:"PUT",data:t,headers:n})}async delete(e,t,n){return this.request({url:e,method:"DELETE",params:t,headers:n})}async patch(e,t,n){return this.request({url:e,method:"PATCH",data:t,headers:n})}}const R=new Vs,C={AUTH:{LOGIN:"/auth/login",LOGOUT:"/auth/logout",REFRESH:"/auth/refresh",REGISTER:"/auth/register",PROFILE:"/auth/me",CHANGE_PASSWORD:"/auth/change-password",RESET_PASSWORD:"/auth/reset-password",USERS:"/auth/users"},MARKET:{QUOTE:"/market/quote",QUOTES:"/market/quotes",KLINE:"/market/kline",HISTORY:"/market/history",SEARCH:"/market/search",OVERVIEW:"/market/overview",SECTORS:"/market/sectors",NEWS:"/market/news",RANKING:"/market/ranking",ORDERBOOK:"/market/orderbook",TICK:"/market/tick",STOCKS:"/market/stocks",WATCHLIST:"/market/watchlist",DEPTH:"/market/depth",SYMBOLS:"/market/symbols"},TRADING:{ACCOUNT:"/trading/account",POSITIONS:"/trading/positions",ORDERS:"/trading/orders",TRADES:"/trading/trades",SUBMIT:"/trading/submit",CANCEL:"/trading/cancel",MODIFY:"/trading/modify",HISTORY:"/trading/history"},STRATEGY:{LIST:"/strategy/list",DETAIL:"/strategy/detail",CREATE:"/strategy/create",UPDATE:"/strategy/update",DELETE:"/strategy/delete",START:"/strategy/start",STOP:"/strategy/stop",BACKTEST:"/strategy/backtest",PERFORMANCE:"/strategy/performance",SIGNALS:"/strategy/signals",TEMPLATES:"/strategy/templates",FILES:"/strategy-files"},BACKTEST:{RUN:"/backtest/run",RESULT:"/backtest/result",HISTORY:"/backtest/history",COMPARE:"/backtest/compare",START:"/backtest/start",STOP:"/backtest/stop",DELETE:"/backtest/delete",HEALTH:"/backtest/health"},USER:{PROFILE:"/user/profile",SETTINGS:"/user/settings",AVATAR:"/user/avatar",PREFERENCES:"/user/preferences"},SYSTEM:{HEALTH:"/health",STATUS:"/status",CONFIG:"/config"}},qt={MARKET:"/ws/market",TRADING:"/ws/trading",STRATEGY:"/ws/strategy"},ye={wsUrl:"wss://api.yourdomain.com/ws",enableMock:!1},pt={DEFAULTS:{INITIAL_CAPITAL:1e5,COMMISSION:3e-4,SLIPPAGE:1e-4,END_DATE:new Date().toISOString().split("T")[0]}},cr=r=>{const e=Math.random()*100+10,t=(Math.random()-.5)*10,n=t/e*100;return{symbol:r,name:`股票${r}`,currentPrice:Number((e+t).toFixed(2)),previousClose:Number(e.toFixed(2)),change:Number(t.toFixed(2)),changePercent:Number(n.toFixed(2)),high:Number((e+Math.abs(t)+Math.random()*5).toFixed(2)),low:Number((e-Math.abs(t)-Math.random()*5).toFixed(2)),openPrice:Number((e+(Math.random()-.5)*2).toFixed(2)),volume:Math.floor(Math.random()*1e6),turnover:Math.floor(Math.random()*1e8),timestamp:Date.now(),status:"trading",industry:"科技"}},lr=(r,e=100)=>{const t=[];let n=Math.random()*100+50;const s=Date.now();for(let i=e-1;i>=0;i--){const o=s-i*24*60*60*1e3,a=(Math.random()-.5)*10,c=n,l=n+a,u=Math.max(c,l)+Math.random()*5,h=Math.min(c,l)-Math.random()*5;t.push({timestamp:o,open:Number(c.toFixed(2)),high:Number(u.toFixed(2)),low:Number(h.toFixed(2)),close:Number(l.toFixed(2)),volume:Math.floor(Math.random()*1e6),turnover:Math.floor(Math.random()*1e8),symbol:r}),n=l}return t};class zs{constructor(){this.useMock=ye.enableMock}async getQuote(e){const t=Array.isArray(e)?e:[e];if(this.useMock)return t.map(n=>cr(n));try{return(await R.get(C.MARKET.QUOTE,{symbols:t.join(",")})).data||[]}catch(n){return console.warn("获取行情API调用失败，使用模拟数据:",n),t.map(s=>cr(s))}}async getKLineData(e){if(this.useMock)return lr(e.symbol,e.limit||100);try{return(await R.get(C.MARKET.KLINE,{symbol:e.symbol,period:e.period,limit:e.limit||1e3,start_time:e.startTime,end_time:e.endTime})).data||[]}catch(t){return console.warn("获取K线数据API调用失败，使用模拟数据:",t),lr(e.symbol,e.limit||100)}}async search(e){if(this.useMock)return[{symbol:"000001",name:"平安银行",exchange:"SZ",market:"深圳",sector:"金融",industry:"银行",listDate:"1991-04-03",totalShares:19405918198,floatShares:19405918198,marketCap:28e10,pe:5.2,pb:.8,eps:2.1,roe:12.5,status:"normal"}].filter(n=>n.name.includes(e.keyword)||n.symbol.includes(e.keyword)).slice(0,e.limit||10);try{return(await R.get(C.MARKET.SEARCH,{keyword:e.keyword,limit:e.limit||10,type:e.type})).data||[]}catch(t){return console.warn("搜索API调用失败，使用模拟数据:",t),[]}}async getOverview(){if(this.useMock)return{indices:[{name:"上证指数",value:3200.5,change:15.2,changePercent:.48},{name:"深证成指",value:12500.8,change:-8.5,changePercent:-.07},{name:"创业板指",value:2800.3,change:25.6,changePercent:.92}],stats:{totalStocks:4500,advancers:2100,decliners:1800,unchanged:600,totalVolume:45e7,totalTurnover:52e10},timestamp:Date.now()};try{return(await R.get(C.MARKET.OVERVIEW)).data}catch(e){return console.warn("获取市场概览API调用失败，使用模拟数据:",e),{indices:[],stats:{totalStocks:0,advancers:0,decliners:0,unchanged:0,totalVolume:0,totalTurnover:0},timestamp:Date.now()}}}}const xt=new zs,ke=r=>{const e=["trend_following","mean_reversion","momentum","arbitrage","market_making"],t=["inactive","active","paused","error"],n=["low","medium","high"];return{id:r,name:`策略${r.slice(-3)}`,description:`这是一个${e[Math.floor(Math.random()*e.length)]}策略的示例`,type:e[Math.floor(Math.random()*e.length)],status:t[Math.floor(Math.random()*t.length)],riskLevel:n[Math.floor(Math.random()*n.length)],frequency:"day",code:`# 策略代码示例
class Strategy:
    def __init__(self):
        self.name = "示例策略"
    
    def on_data(self, data):
        pass`,codeLanguage:"python",parameters:[{name:"period",type:"number",value:20,description:"移动平均周期",min:5,max:100}],config:{symbols:["000001","000002"],maxPositions:10,maxPositionSize:.1,frequency:"day",initialCapital:1e5,positionSizing:"percent",positionSizeValue:.05,commission:3e-4,slippage:1e-4,enableShortSelling:!1,enableLeverage:!1},tags:["技术分析","趋势跟踪"],version:"1.0.0",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),createdBy:"user-001",authorName:"量化研究员",isPublic:Math.random()>.5,totalRuns:Math.floor(Math.random()*100),successRuns:Math.floor(Math.random()*80),failureRuns:Math.floor(Math.random()*20),performance:{totalReturn:(Math.random()-.3)*50,annualizedReturn:(Math.random()-.3)*30,maxDrawdown:Math.random()*20,sharpeRatio:Math.random()*3,winRate:Math.random()*100,profitFactor:Math.random()*3+.5,totalTrades:Math.floor(Math.random()*500),avgTradeDuration:Math.random()*10,volatility:Math.random()*30,beta:Math.random()*2,alpha:(Math.random()-.5)*10,informationRatio:Math.random()*2}}};class Hs{constructor(){this.useMock=ye.enableMock}async getStrategies(e){if(this.useMock){let n=Array.from({length:20},(c,l)=>ke(`strategy-${String(l+1).padStart(3,"0")}`));e?.keyword&&(n=n.filter(c=>c.name.includes(e.keyword)||c.description.includes(e.keyword))),e?.type&&(n=n.filter(c=>c.type===e.type)),e?.status&&(n=n.filter(c=>c.status===e.status));const s=e?.page||1,i=e?.pageSize||10,o=(s-1)*i,a=o+i;return{items:n.slice(o,a),total:n.length,page:s,pageSize:i,totalPages:Math.ceil(n.length/i)}}try{return(await R.get(C.STRATEGY.LIST,e)).data}catch(t){return console.warn("获取策略列表API调用失败，使用模拟数据:",t),{items:[ke("strategy-001")],total:1,page:1,pageSize:10,totalPages:1}}}async getStrategy(e){if(this.useMock)return ke(e);try{return(await R.get(`${C.STRATEGY.DETAIL}/${e}`)).data}catch(t){return console.warn("获取策略详情API调用失败，使用模拟数据:",t),ke(e)}}async createStrategy(e){if(this.useMock)return{...ke(`strategy-${Date.now()}`),...e,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};try{return(await R.post(C.STRATEGY.CREATE,e)).data}catch(t){throw console.warn("创建策略API调用失败，使用模拟数据:",t),t}}async updateStrategy(e){if(this.useMock)return{...ke(e.id),...e,updatedAt:new Date().toISOString()};try{return(await R.put(`${C.STRATEGY.UPDATE}/${e.id}`,e)).data}catch(t){throw console.warn("更新策略API调用失败:",t),t}}async deleteStrategy(e){if(this.useMock)return Promise.resolve();try{await R.delete(`${C.STRATEGY.DELETE}/${e}`)}catch(t){throw console.warn("删除策略API调用失败:",t),t}}async startStrategy(e){if(this.useMock)return Promise.resolve();try{await R.post(`${C.STRATEGY.START}/${e}`)}catch(t){throw console.warn("启动策略API调用失败:",t),t}}async stopStrategy(e){if(this.useMock)return Promise.resolve();try{await R.post(`${C.STRATEGY.STOP}/${e}`)}catch(t){throw console.warn("停止策略API调用失败:",t),t}}async getTemplates(){if(this.useMock)return[{id:"template-001",name:"双均线策略",description:"基于短期和长期移动平均线的经典策略",type:"trend_following",code:"# 双均线策略代码",parameters:[],tags:["技术分析","趋势跟踪"],author:"量化团队",downloads:1250,rating:4.5,createdAt:new Date().toISOString()}];try{return(await R.get(C.STRATEGY.TEMPLATES)).data||[]}catch(e){return console.warn("获取策略模板API调用失败，使用模拟数据:",e),[]}}}const ne=new Hs,ur=r=>{const e=(Math.random()-.3)*50,t=Math.random()*30+5,n=e/t;return{id:r,strategyId:`strategy-${Math.floor(Math.random()*100)}`,strategyName:`策略${r.slice(-3)}`,symbol:"000001",status:"completed",config:{strategyId:`strategy-${Math.floor(Math.random()*100)}`,symbol:"000001",startDate:"2023-01-01",endDate:"2024-01-01",initialCapital:1e5,commission:3e-4,slippage:1e-4},totalReturn:Number(e.toFixed(2)),annualizedReturn:Number((e*1.2).toFixed(2)),maxDrawdown:Number((Math.random()*20).toFixed(2)),sharpeRatio:Number(n.toFixed(2)),sortinoRatio:Number((n*1.2).toFixed(2)),calmarRatio:Number((n*.8).toFixed(2)),winRate:Number((Math.random()*40+40).toFixed(1)),profitFactor:Number((Math.random()*2+.5).toFixed(2)),totalTrades:Math.floor(Math.random()*200+50),winningTrades:Math.floor(Math.random()*120+30),losingTrades:Math.floor(Math.random()*80+20),avgWinningTrade:Number((Math.random()*500+100).toFixed(2)),avgLosingTrade:Number((-Math.random()*300-50).toFixed(2)),maxWinningTrade:Number((Math.random()*2e3+500).toFixed(2)),maxLosingTrade:Number((-Math.random()*1e3-200).toFixed(2)),avgTradeDuration:Number((Math.random()*10+1).toFixed(1)),volatility:Number(t.toFixed(2)),beta:Number((Math.random()*1.5+.5).toFixed(2)),alpha:Number((Math.random()*10-5).toFixed(2)),informationRatio:Number((Math.random()*2).toFixed(2)),trackingError:Number((Math.random()*5).toFixed(2)),startTime:"2023-01-01T00:00:00Z",endTime:"2024-01-01T00:00:00Z",duration:365*24*60*60*1e3,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}},mt=r=>{const e=["pending","running","completed","failed"],t=e[Math.floor(Math.random()*e.length)];return{id:r,name:`回测任务${r.slice(-3)}`,strategyId:`strategy-${Math.floor(Math.random()*100)}`,strategyName:`策略${r.slice(-3)}`,symbol:"000001",status:t,progress:t==="completed"?100:Math.floor(Math.random()*100),config:{strategyId:`strategy-${Math.floor(Math.random()*100)}`,symbol:"000001",startDate:"2023-01-01",endDate:"2024-01-01",initialCapital:1e5,commission:3e-4,slippage:1e-4},createdAt:new Date().toISOString(),startTime:t!=="pending"?new Date().toISOString():void 0,endTime:t==="completed"?new Date().toISOString():void 0}};class js{constructor(){this.useMock=ye.enableMock}async getBacktests(e){if(this.useMock){let n=Array.from({length:15},(c,l)=>mt(`backtest-${String(l+1).padStart(3,"0")}`));e?.strategyId&&(n=n.filter(c=>c.strategyId===e.strategyId)),e?.status&&(n=n.filter(c=>c.status===e.status)),e?.symbol&&(n=n.filter(c=>c.symbol===e.symbol));const s=e?.page||1,i=e?.pageSize||10,o=(s-1)*i,a=o+i;return{items:n.slice(o,a),total:n.length,page:s,pageSize:i,totalPages:Math.ceil(n.length/i)}}try{return(await R.get(C.BACKTEST.HISTORY,e)).data}catch(t){return console.warn("获取回测列表API调用失败，使用模拟数据:",t),{items:[mt("backtest-001")],total:1,page:1,pageSize:10,totalPages:1}}}async getBacktestResult(e){if(this.useMock)return ur(e);try{return(await R.get(`${C.BACKTEST.RESULT}/${e}`)).data}catch(t){return console.warn("获取回测结果API调用失败，使用模拟数据:",t),ur(e)}}async createBacktest(e){if(this.useMock){const t=mt(`backtest-${Date.now()}`);return{...t,name:e.name,strategyId:e.strategyId,symbol:e.symbol,config:{...t.config,...e,initialCapital:e.initialCapital||pt.DEFAULTS.INITIAL_CAPITAL,commission:e.commission||pt.DEFAULTS.COMMISSION,slippage:e.slippage||pt.DEFAULTS.SLIPPAGE}}}try{return(await R.post(C.BACKTEST.RUN,e)).data}catch(t){throw console.warn("创建回测任务API调用失败:",t),t}}async startBacktest(e){if(this.useMock)return Promise.resolve();try{await R.post(`${C.BACKTEST.START}/${e}`)}catch(t){throw console.warn("启动回测API调用失败:",t),t}}async stopBacktest(e){if(this.useMock)return Promise.resolve();try{await R.post(`${C.BACKTEST.STOP}/${e}`)}catch(t){throw console.warn("停止回测API调用失败:",t),t}}async deleteBacktest(e){if(this.useMock)return Promise.resolve();try{await R.delete(`${C.BACKTEST.DELETE}/${e}`)}catch(t){throw console.warn("删除回测API调用失败:",t),t}}async getHealth(){if(this.useMock)return{status:"healthy",message:"回测服务运行正常"};try{return(await R.get(C.BACKTEST.HEALTH)).data}catch(e){return console.warn("获取回测健康状态API调用失败:",e),{status:"error",message:"回测服务连接失败"}}}}const Ks=new js,Fe=(r,e)=>{const t=r||"demo_user",n=e||"user",s={admin:{nickname:"系统管理员",realName:"管理员",avatar:"admin"},trader:{nickname:"专业交易员",realName:"交易员",avatar:"trader"},demo:{nickname:"演示用户",realName:"张三",avatar:"demo"},test:{nickname:"测试用户",realName:"李四",avatar:"test"}},i=s[t]||s.demo;return{id:`user-${t}-${Date.now()}`,username:t,email:`${t}@example.com`,nickname:i.nickname,avatar:`https://api.dicebear.com/7.x/avataaars/svg?seed=${i.avatar}`,phone:"138****8888",role:n==="trader"?"vip":n,status:"active",createdAt:"2024-01-01T00:00:00Z",updatedAt:new Date().toISOString(),lastLoginAt:new Date().toISOString(),preferences:{theme:"light",language:"zh-CN",timezone:"Asia/Shanghai",notifications:{email:!0,push:!0,sms:!1},trading:{confirmOrders:!0,showRiskWarnings:!0,defaultOrderType:"limit"}},profile:{realName:i.realName,gender:"male",investmentExperience:n==="admin"?"professional":"intermediate",riskTolerance:n==="trader"?"aggressive":"moderate"}}};class Ys{constructor(){this.useMock=ye.enableMock}async login(e){if(this.useMock){const n=[{username:"demo",password:"demo123",role:"user"},{username:"admin",password:"123456",role:"admin"},{username:"trader",password:"trader123",role:"trader"},{username:"test",password:"test123",role:"user"}].find(s=>s.username===e.username&&s.password===e.password);if(n){const s=Fe(n.username,n.role),i={user:s,accessToken:"mock-access-token-"+Date.now(),refreshToken:"mock-refresh-token-"+Date.now(),expiresIn:3600};return typeof window<"u"&&(localStorage.setItem("access_token",i.accessToken),localStorage.setItem("refresh_token",i.refreshToken),localStorage.setItem("user_info",JSON.stringify(s))),i}else throw new Error("用户名或密码错误")}try{const t=await R.post(C.AUTH.LOGIN,e);return t.data&&typeof window<"u"&&(localStorage.setItem("access_token",t.data.accessToken),localStorage.setItem("refresh_token",t.data.refreshToken),localStorage.setItem("user_info",JSON.stringify(t.data.user))),t.data}catch(t){throw console.warn("登录API调用失败:",t),t}}async register(e){if(this.useMock){if(e.password!==e.confirmPassword)throw new Error("两次输入的密码不一致");return{...Fe(),username:e.username,email:e.email,createdAt:new Date().toISOString()}}try{return(await R.post(C.AUTH.REGISTER,e)).data}catch(t){throw console.warn("注册API调用失败:",t),t}}async logout(){if(this.useMock)return typeof window<"u"&&(localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_info")),Promise.resolve();try{await R.post(C.AUTH.LOGOUT)}catch(e){console.warn("登出API调用失败:",e)}finally{typeof window<"u"&&(localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_info"))}}async refreshToken(){if(this.useMock)return{accessToken:"mock-new-access-token-"+Date.now(),refreshToken:"mock-new-refresh-token-"+Date.now(),expiresIn:3600};try{const e=typeof window<"u"?localStorage.getItem("refresh_token"):null,t=await R.post(C.AUTH.REFRESH,{refreshToken:e});return t.data&&typeof window<"u"&&(localStorage.setItem("access_token",t.data.accessToken),localStorage.setItem("refresh_token",t.data.refreshToken)),t.data}catch(e){throw console.warn("刷新token API调用失败:",e),e}}async getUserInfo(){if(this.useMock){if(typeof window<"u"){const e=localStorage.getItem("user_info");if(e)return JSON.parse(e)}return Fe()}try{return(await R.get(C.AUTH.PROFILE)).data}catch(e){return console.warn("获取用户信息API调用失败，使用模拟数据:",e),Fe()}}async updateProfile(e){if(this.useMock){const n={...await this.getUserInfo(),...e,updatedAt:new Date().toISOString()};return typeof window<"u"&&localStorage.setItem("user_info",JSON.stringify(n)),n}try{const t=await R.put(C.AUTH.PROFILE,e);return t.data&&typeof window<"u"&&localStorage.setItem("user_info",JSON.stringify(t.data)),t.data}catch(t){throw console.warn("更新用户资料API调用失败:",t),t}}async changePassword(e){if(this.useMock){if(e.newPassword!==e.confirmPassword)throw new Error("两次输入的新密码不一致");return Promise.resolve()}try{await R.post(C.AUTH.CHANGE_PASSWORD,e)}catch(t){throw console.warn("修改密码API调用失败:",t),t}}async resetPassword(e){if(this.useMock)return Promise.resolve();try{await R.post(C.AUTH.RESET_PASSWORD,e)}catch(t){throw console.warn("重置密码API调用失败:",t),t}}async uploadAvatar(e){if(this.useMock)return{url:"https://via.placeholder.com/100x100"};try{const t=new FormData;return t.append("avatar",e),(await R.post(C.USER.AVATAR,t,{"Content-Type":"multipart/form-data"})).data}catch(t){throw console.warn("上传头像API调用失败:",t),t}}async updatePreferences(e){if(this.useMock){const t=await this.getUserInfo(),n={...t.preferences,...e},s={...t,preferences:n,updatedAt:new Date().toISOString()};return typeof window<"u"&&localStorage.setItem("user_info",JSON.stringify(s)),n}try{return(await R.put(C.USER.PREFERENCES,e)).data}catch(t){throw console.warn("更新用户偏好设置API调用失败:",t),t}}}const Q=new Ys,te=Object.create(null);te.open="0";te.close="1";te.ping="2";te.pong="3";te.message="4";te.upgrade="5";te.noop="6";const je=Object.create(null);Object.keys(te).forEach(r=>{je[te[r]]=r});const Tt={type:"error",data:"parser error"},qr=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",Vr=typeof ArrayBuffer=="function",zr=r=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(r):r&&r.buffer instanceof ArrayBuffer,Vt=({type:r,data:e},t,n)=>qr&&e instanceof Blob?t?n(e):hr(e,n):Vr&&(e instanceof ArrayBuffer||zr(e))?t?n(e):hr(new Blob([e]),n):n(te[r]+(e||"")),hr=(r,e)=>{const t=new FileReader;return t.onload=function(){const n=t.result.split(",")[1];e("b"+(n||""))},t.readAsDataURL(r)};function dr(r){return r instanceof Uint8Array?r:r instanceof ArrayBuffer?new Uint8Array(r):new Uint8Array(r.buffer,r.byteOffset,r.byteLength)}let yt;function Gs(r,e){if(qr&&r.data instanceof Blob)return r.data.arrayBuffer().then(dr).then(e);if(Vr&&(r.data instanceof ArrayBuffer||zr(r.data)))return e(dr(r.data));Vt(r,!1,t=>{yt||(yt=new TextEncoder),e(yt.encode(t))})}const fr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Pe=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let r=0;r<fr.length;r++)Pe[fr.charCodeAt(r)]=r;const Qs=r=>{let e=r.length*.75,t=r.length,n,s=0,i,o,a,c;r[r.length-1]==="="&&(e--,r[r.length-2]==="="&&e--);const l=new ArrayBuffer(e),u=new Uint8Array(l);for(n=0;n<t;n+=4)i=Pe[r.charCodeAt(n)],o=Pe[r.charCodeAt(n+1)],a=Pe[r.charCodeAt(n+2)],c=Pe[r.charCodeAt(n+3)],u[s++]=i<<2|o>>4,u[s++]=(o&15)<<4|a>>2,u[s++]=(a&3)<<6|c&63;return l},Xs=typeof ArrayBuffer=="function",zt=(r,e)=>{if(typeof r!="string")return{type:"message",data:Hr(r,e)};const t=r.charAt(0);return t==="b"?{type:"message",data:Js(r.substring(1),e)}:je[t]?r.length>1?{type:je[t],data:r.substring(1)}:{type:je[t]}:Tt},Js=(r,e)=>{if(Xs){const t=Qs(r);return Hr(t,e)}else return{base64:!0,data:r}},Hr=(r,e)=>{switch(e){case"blob":return r instanceof Blob?r:new Blob([r]);case"arraybuffer":default:return r instanceof ArrayBuffer?r:r.buffer}},jr="",Zs=(r,e)=>{const t=r.length,n=new Array(t);let s=0;r.forEach((i,o)=>{Vt(i,!1,a=>{n[o]=a,++s===t&&e(n.join(jr))})})},ei=(r,e)=>{const t=r.split(jr),n=[];for(let s=0;s<t.length;s++){const i=zt(t[s],e);if(n.push(i),i.type==="error")break}return n};function ti(){return new TransformStream({transform(r,e){Gs(r,t=>{const n=t.length;let s;if(n<126)s=new Uint8Array(1),new DataView(s.buffer).setUint8(0,n);else if(n<65536){s=new Uint8Array(3);const i=new DataView(s.buffer);i.setUint8(0,126),i.setUint16(1,n)}else{s=new Uint8Array(9);const i=new DataView(s.buffer);i.setUint8(0,127),i.setBigUint64(1,BigInt(n))}r.data&&typeof r.data!="string"&&(s[0]|=128),e.enqueue(s),e.enqueue(t)})}})}let bt;function We(r){return r.reduce((e,t)=>e+t.length,0)}function qe(r,e){if(r[0].length===e)return r.shift();const t=new Uint8Array(e);let n=0;for(let s=0;s<e;s++)t[s]=r[0][n++],n===r[0].length&&(r.shift(),n=0);return r.length&&n<r[0].length&&(r[0]=r[0].slice(n)),t}function ri(r,e){bt||(bt=new TextDecoder);const t=[];let n=0,s=-1,i=!1;return new TransformStream({transform(o,a){for(t.push(o);;){if(n===0){if(We(t)<1)break;const c=qe(t,1);i=(c[0]&128)===128,s=c[0]&127,s<126?n=3:s===126?n=1:n=2}else if(n===1){if(We(t)<2)break;const c=qe(t,2);s=new DataView(c.buffer,c.byteOffset,c.length).getUint16(0),n=3}else if(n===2){if(We(t)<8)break;const c=qe(t,8),l=new DataView(c.buffer,c.byteOffset,c.length),u=l.getUint32(0);if(u>Math.pow(2,21)-1){a.enqueue(Tt);break}s=u*Math.pow(2,32)+l.getUint32(4),n=3}else{if(We(t)<s)break;const c=qe(t,s);a.enqueue(zt(i?c:bt.decode(c),e)),n=0}if(s===0||s>r){a.enqueue(Tt);break}}}})}const Kr=4;function B(r){if(r)return ni(r)}function ni(r){for(var e in B.prototype)r[e]=B.prototype[e];return r}B.prototype.on=B.prototype.addEventListener=function(r,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+r]=this._callbacks["$"+r]||[]).push(e),this};B.prototype.once=function(r,e){function t(){this.off(r,t),e.apply(this,arguments)}return t.fn=e,this.on(r,t),this};B.prototype.off=B.prototype.removeListener=B.prototype.removeAllListeners=B.prototype.removeEventListener=function(r,e){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var t=this._callbacks["$"+r];if(!t)return this;if(arguments.length==1)return delete this._callbacks["$"+r],this;for(var n,s=0;s<t.length;s++)if(n=t[s],n===e||n.fn===e){t.splice(s,1);break}return t.length===0&&delete this._callbacks["$"+r],this};B.prototype.emit=function(r){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),t=this._callbacks["$"+r],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(t){t=t.slice(0);for(var n=0,s=t.length;n<s;++n)t[n].apply(this,e)}return this};B.prototype.emitReserved=B.prototype.emit;B.prototype.listeners=function(r){return this._callbacks=this._callbacks||{},this._callbacks["$"+r]||[]};B.prototype.hasListeners=function(r){return!!this.listeners(r).length};const ot=typeof Promise=="function"&&typeof Promise.resolve=="function"?e=>Promise.resolve().then(e):(e,t)=>t(e,0),G=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),si="arraybuffer";function Yr(r,...e){return e.reduce((t,n)=>(r.hasOwnProperty(n)&&(t[n]=r[n]),t),{})}const ii=G.setTimeout,oi=G.clearTimeout;function at(r,e){e.useNativeTimers?(r.setTimeoutFn=ii.bind(G),r.clearTimeoutFn=oi.bind(G)):(r.setTimeoutFn=G.setTimeout.bind(G),r.clearTimeoutFn=G.clearTimeout.bind(G))}const ai=1.33;function ci(r){return typeof r=="string"?li(r):Math.ceil((r.byteLength||r.size)*ai)}function li(r){let e=0,t=0;for(let n=0,s=r.length;n<s;n++)e=r.charCodeAt(n),e<128?t+=1:e<2048?t+=2:e<55296||e>=57344?t+=3:(n++,t+=4);return t}function Gr(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function ui(r){let e="";for(let t in r)r.hasOwnProperty(t)&&(e.length&&(e+="&"),e+=encodeURIComponent(t)+"="+encodeURIComponent(r[t]));return e}function hi(r){let e={},t=r.split("&");for(let n=0,s=t.length;n<s;n++){let i=t[n].split("=");e[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return e}class di extends Error{constructor(e,t,n){super(e),this.description=t,this.context=n,this.type="TransportError"}}class Ht extends B{constructor(e){super(),this.writable=!1,at(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,n){return super.emitReserved("error",new di(e,t,n)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(e){this.readyState==="open"&&this.write(e)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){const t=zt(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){const e=this.opts.hostname;return e.indexOf(":")===-1?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(e){const t=ui(e);return t.length?"?"+t:""}}class fi extends Ht{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";const t=()=>{this.readyState="paused",e()};if(this._polling||!this.writable){let n=0;this._polling&&(n++,this.once("pollComplete",function(){--n||t()})),this.writable||(n++,this.once("drain",function(){--n||t()}))}else t()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){const t=n=>{if(this.readyState==="opening"&&n.type==="open"&&this.onOpen(),n.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(n)};ei(e,this.socket.binaryType).forEach(t),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const e=()=>{this.write([{type:"close"}])};this.readyState==="open"?e():this.once("open",e)}write(e){this.writable=!1,Zs(e,t=>{this.doWrite(t,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const e=this.opts.secure?"https":"http",t=this.query||{};return this.opts.timestampRequests!==!1&&(t[this.opts.timestampParam]=Gr()),!this.supportsBinary&&!t.sid&&(t.b64=1),this.createUri(e,t)}}let Qr=!1;try{Qr=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const gi=Qr;function pi(){}class mi extends fi{constructor(e){if(super(e),typeof location<"u"){const t=location.protocol==="https:";let n=location.port;n||(n=t?"443":"80"),this.xd=typeof location<"u"&&e.hostname!==location.hostname||n!==e.port}}doWrite(e,t){const n=this.request({method:"POST",data:e});n.on("success",t),n.on("error",(s,i)=>{this.onError("xhr post error",s,i)})}doPoll(){const e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(t,n)=>{this.onError("xhr poll error",t,n)}),this.pollXhr=e}}class ee extends B{constructor(e,t,n){super(),this.createRequest=e,at(this,n),this._opts=n,this._method=n.method||"GET",this._uri=t,this._data=n.data!==void 0?n.data:null,this._create()}_create(){var e;const t=Yr(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;const n=this._xhr=this.createRequest(t);try{n.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){n.setDisableHeaderCheck&&n.setDisableHeaderCheck(!0);for(let s in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(s)&&n.setRequestHeader(s,this._opts.extraHeaders[s])}}catch{}if(this._method==="POST")try{n.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{n.setRequestHeader("Accept","*/*")}catch{}(e=this._opts.cookieJar)===null||e===void 0||e.addCookies(n),"withCredentials"in n&&(n.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(n.timeout=this._opts.requestTimeout),n.onreadystatechange=()=>{var s;n.readyState===3&&((s=this._opts.cookieJar)===null||s===void 0||s.parseCookies(n.getResponseHeader("set-cookie"))),n.readyState===4&&(n.status===200||n.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof n.status=="number"?n.status:0)},0))},n.send(this._data)}catch(s){this.setTimeoutFn(()=>{this._onError(s)},0);return}typeof document<"u"&&(this._index=ee.requestsCount++,ee.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=pi,e)try{this._xhr.abort()}catch{}typeof document<"u"&&delete ee.requests[this._index],this._xhr=null}}_onLoad(){const e=this._xhr.responseText;e!==null&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}ee.requestsCount=0;ee.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",gr);else if(typeof addEventListener=="function"){const r="onpagehide"in G?"pagehide":"unload";addEventListener(r,gr,!1)}}function gr(){for(let r in ee.requests)ee.requests.hasOwnProperty(r)&&ee.requests[r].abort()}const yi=function(){const r=Xr({xdomain:!1});return r&&r.responseType!==null}();class bi extends mi{constructor(e){super(e);const t=e&&e.forceBase64;this.supportsBinary=yi&&!t}request(e={}){return Object.assign(e,{xd:this.xd},this.opts),new ee(Xr,this.uri(),e)}}function Xr(r){const e=r.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!e||gi))return new XMLHttpRequest}catch{}if(!e)try{return new G[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const Jr=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class wi extends Ht{get name(){return"websocket"}doOpen(){const e=this.uri(),t=this.opts.protocols,n=Jr?{}:Yr(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(n.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,n)}catch(s){return this.emitReserved("error",s)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],s=t===e.length-1;Vt(n,this.supportsBinary,i=>{try{this.doWrite(n,i)}catch{}s&&ot(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=Gr()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}const wt=G.WebSocket||G.MozWebSocket;class Si extends wi{createSocket(e,t,n){return Jr?new wt(e,t,n):t?new wt(e,t):new wt(e)}doWrite(e,t){this.ws.send(t)}}class ki extends Ht{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(e){return this.emitReserved("error",e)}this._transport.closed.then(()=>{this.onClose()}).catch(e=>{this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{const t=ri(Number.MAX_SAFE_INTEGER,this.socket.binaryType),n=e.readable.pipeThrough(t).getReader(),s=ti();s.readable.pipeTo(e.writable),this._writer=s.writable.getWriter();const i=()=>{n.read().then(({done:a,value:c})=>{a||(this.onPacket(c),i())}).catch(a=>{})};i();const o={type:"open"};this.query.sid&&(o.data=`{"sid":"${this.query.sid}"}`),this._writer.write(o).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],s=t===e.length-1;this._writer.write(n).then(()=>{s&&ot(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;(e=this._transport)===null||e===void 0||e.close()}}const vi={websocket:Si,webtransport:ki,polling:bi},_i=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Ei=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Rt(r){if(r.length>8e3)throw"URI too long";const e=r,t=r.indexOf("["),n=r.indexOf("]");t!=-1&&n!=-1&&(r=r.substring(0,t)+r.substring(t,n).replace(/:/g,";")+r.substring(n,r.length));let s=_i.exec(r||""),i={},o=14;for(;o--;)i[Ei[o]]=s[o]||"";return t!=-1&&n!=-1&&(i.source=e,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,":"),i.authority=i.authority.replace("[","").replace("]","").replace(/;/g,":"),i.ipv6uri=!0),i.pathNames=Ai(i,i.path),i.queryKey=xi(i,i.query),i}function Ai(r,e){const t=/\/{2,9}/g,n=e.replace(t,"/").split("/");return(e.slice(0,1)=="/"||e.length===0)&&n.splice(0,1),e.slice(-1)=="/"&&n.splice(n.length-1,1),n}function xi(r,e){const t={};return e.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(n,s,i){s&&(t[s]=i)}),t}const Ct=typeof addEventListener=="function"&&typeof removeEventListener=="function",Ke=[];Ct&&addEventListener("offline",()=>{Ke.forEach(r=>r())},!1);class ie extends B{constructor(e,t){if(super(),this.binaryType=si,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&typeof e=="object"&&(t=e,e=null),e){const n=Rt(e);t.hostname=n.host,t.secure=n.protocol==="https"||n.protocol==="wss",t.port=n.port,n.query&&(t.query=n.query)}else t.host&&(t.hostname=Rt(t.host).host);at(this,t),this.secure=t.secure!=null?t.secure:typeof location<"u"&&location.protocol==="https:",t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=t.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(n=>{const s=n.prototype.name;this.transports.push(s),this._transportsByName[s]=n}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=hi(this.opts.query)),Ct&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},Ke.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(e){const t=Object.assign({},this.opts.query);t.EIO=Kr,t.transport=e,this.id&&(t.sid=this.id);const n=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return new this._transportsByName[e](n)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const e=this.opts.rememberUpgrade&&ie.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",t=>this._onClose("transport close",t))}onOpen(){this.readyState="open",ie.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(e){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const t=new Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data);break}}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const e=this._getWritablePackets();this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let t=1;for(let n=0;n<this.writeBuffer.length;n++){const s=this.writeBuffer[n].data;if(s&&(t+=ci(s)),n>0&&t>this._maxPayload)return this.writeBuffer.slice(0,n);t+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const e=Date.now()>this._pingTimeoutTime;return e&&(this._pingTimeoutTime=0,ot(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,n){return this._sendPacket("message",e,t,n),this}send(e,t,n){return this._sendPacket("message",e,t,n),this}_sendPacket(e,t,n,s){if(typeof t=="function"&&(s=t,t=void 0),typeof n=="function"&&(s=n,n=null),this.readyState==="closing"||this.readyState==="closed")return;n=n||{},n.compress=n.compress!==!1;const i={type:e,data:t,options:n};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),s&&this.once("flush",s),this.flush()}close(){const e=()=>{this._onClose("forced close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},n=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?n():e()}):this.upgrading?n():e()),this}_onError(e){if(ie.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Ct&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const n=Ke.indexOf(this._offlineEventListener);n!==-1&&Ke.splice(n,1)}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}ie.protocol=Kr;class Ti extends ie{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}_probe(e){let t=this.createTransport(e),n=!1;ie.priorWebsocketSuccess=!1;const s=()=>{n||(t.send([{type:"ping",data:"probe"}]),t.once("packet",h=>{if(!n)if(h.type==="pong"&&h.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",t),!t)return;ie.priorWebsocketSuccess=t.name==="websocket",this.transport.pause(()=>{n||this.readyState!=="closed"&&(u(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())})}else{const d=new Error("probe error");d.transport=t.name,this.emitReserved("upgradeError",d)}}))};function i(){n||(n=!0,u(),t.close(),t=null)}const o=h=>{const d=new Error("probe error: "+h);d.transport=t.name,i(),this.emitReserved("upgradeError",d)};function a(){o("transport closed")}function c(){o("socket closed")}function l(h){t&&h.name!==t.name&&i()}const u=()=>{t.removeListener("open",s),t.removeListener("error",o),t.removeListener("close",a),this.off("close",c),this.off("upgrading",l)};t.once("open",s),t.once("error",o),t.once("close",a),this.once("close",c),this.once("upgrading",l),this._upgrades.indexOf("webtransport")!==-1&&e!=="webtransport"?this.setTimeoutFn(()=>{n||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){const t=[];for(let n=0;n<e.length;n++)~this.transports.indexOf(e[n])&&t.push(e[n]);return t}}let Ri=class extends Ti{constructor(e,t={}){const n=typeof e=="object"?e:t;(!n.transports||n.transports&&typeof n.transports[0]=="string")&&(n.transports=(n.transports||["polling","websocket","webtransport"]).map(s=>vi[s]).filter(s=>!!s)),super(e,n)}};function Ci(r,e="",t){let n=r;t=t||typeof location<"u"&&location,r==null&&(r=t.protocol+"//"+t.host),typeof r=="string"&&(r.charAt(0)==="/"&&(r.charAt(1)==="/"?r=t.protocol+r:r=t.host+r),/^(https?|wss?):\/\//.test(r)||(typeof t<"u"?r=t.protocol+"//"+r:r="https://"+r),n=Rt(r)),n.port||(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443")),n.path=n.path||"/";const i=n.host.indexOf(":")!==-1?"["+n.host+"]":n.host;return n.id=n.protocol+"://"+i+":"+n.port+e,n.href=n.protocol+"://"+i+(t&&t.port===n.port?"":":"+n.port),n}const Pi=typeof ArrayBuffer=="function",Oi=r=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(r):r.buffer instanceof ArrayBuffer,Zr=Object.prototype.toString,Li=typeof Blob=="function"||typeof Blob<"u"&&Zr.call(Blob)==="[object BlobConstructor]",Ii=typeof File=="function"||typeof File<"u"&&Zr.call(File)==="[object FileConstructor]";function jt(r){return Pi&&(r instanceof ArrayBuffer||Oi(r))||Li&&r instanceof Blob||Ii&&r instanceof File}function Ye(r,e){if(!r||typeof r!="object")return!1;if(Array.isArray(r)){for(let t=0,n=r.length;t<n;t++)if(Ye(r[t]))return!0;return!1}if(jt(r))return!0;if(r.toJSON&&typeof r.toJSON=="function"&&arguments.length===1)return Ye(r.toJSON(),!0);for(const t in r)if(Object.prototype.hasOwnProperty.call(r,t)&&Ye(r[t]))return!0;return!1}function Ni(r){const e=[],t=r.data,n=r;return n.data=Pt(t,e),n.attachments=e.length,{packet:n,buffers:e}}function Pt(r,e){if(!r)return r;if(jt(r)){const t={_placeholder:!0,num:e.length};return e.push(r),t}else if(Array.isArray(r)){const t=new Array(r.length);for(let n=0;n<r.length;n++)t[n]=Pt(r[n],e);return t}else if(typeof r=="object"&&!(r instanceof Date)){const t={};for(const n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=Pt(r[n],e));return t}return r}function Mi(r,e){return r.data=Ot(r.data,e),delete r.attachments,r}function Ot(r,e){if(!r)return r;if(r&&r._placeholder===!0){if(typeof r.num=="number"&&r.num>=0&&r.num<e.length)return e[r.num];throw new Error("illegal attachments")}else if(Array.isArray(r))for(let t=0;t<r.length;t++)r[t]=Ot(r[t],e);else if(typeof r=="object")for(const t in r)Object.prototype.hasOwnProperty.call(r,t)&&(r[t]=Ot(r[t],e));return r}const Bi=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],Di=5;var x;(function(r){r[r.CONNECT=0]="CONNECT",r[r.DISCONNECT=1]="DISCONNECT",r[r.EVENT=2]="EVENT",r[r.ACK=3]="ACK",r[r.CONNECT_ERROR=4]="CONNECT_ERROR",r[r.BINARY_EVENT=5]="BINARY_EVENT",r[r.BINARY_ACK=6]="BINARY_ACK"})(x||(x={}));class $i{constructor(e){this.replacer=e}encode(e){return(e.type===x.EVENT||e.type===x.ACK)&&Ye(e)?this.encodeAsBinary({type:e.type===x.EVENT?x.BINARY_EVENT:x.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let t=""+e.type;return(e.type===x.BINARY_EVENT||e.type===x.BINARY_ACK)&&(t+=e.attachments+"-"),e.nsp&&e.nsp!=="/"&&(t+=e.nsp+","),e.id!=null&&(t+=e.id),e.data!=null&&(t+=JSON.stringify(e.data,this.replacer)),t}encodeAsBinary(e){const t=Ni(e),n=this.encodeAsString(t.packet),s=t.buffers;return s.unshift(n),s}}function pr(r){return Object.prototype.toString.call(r)==="[object Object]"}class Kt extends B{constructor(e){super(),this.reviver=e}add(e){let t;if(typeof e=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");t=this.decodeString(e);const n=t.type===x.BINARY_EVENT;n||t.type===x.BINARY_ACK?(t.type=n?x.EVENT:x.ACK,this.reconstructor=new Ui(t),t.attachments===0&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else if(jt(e)||e.base64)if(this.reconstructor)t=this.reconstructor.takeBinaryData(e),t&&(this.reconstructor=null,super.emitReserved("decoded",t));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+e)}decodeString(e){let t=0;const n={type:Number(e.charAt(0))};if(x[n.type]===void 0)throw new Error("unknown packet type "+n.type);if(n.type===x.BINARY_EVENT||n.type===x.BINARY_ACK){const i=t+1;for(;e.charAt(++t)!=="-"&&t!=e.length;);const o=e.substring(i,t);if(o!=Number(o)||e.charAt(t)!=="-")throw new Error("Illegal attachments");n.attachments=Number(o)}if(e.charAt(t+1)==="/"){const i=t+1;for(;++t&&!(e.charAt(t)===","||t===e.length););n.nsp=e.substring(i,t)}else n.nsp="/";const s=e.charAt(t+1);if(s!==""&&Number(s)==s){const i=t+1;for(;++t;){const o=e.charAt(t);if(o==null||Number(o)!=o){--t;break}if(t===e.length)break}n.id=Number(e.substring(i,t+1))}if(e.charAt(++t)){const i=this.tryParse(e.substr(t));if(Kt.isPayloadValid(n.type,i))n.data=i;else throw new Error("invalid payload")}return n}tryParse(e){try{return JSON.parse(e,this.reviver)}catch{return!1}}static isPayloadValid(e,t){switch(e){case x.CONNECT:return pr(t);case x.DISCONNECT:return t===void 0;case x.CONNECT_ERROR:return typeof t=="string"||pr(t);case x.EVENT:case x.BINARY_EVENT:return Array.isArray(t)&&(typeof t[0]=="number"||typeof t[0]=="string"&&Bi.indexOf(t[0])===-1);case x.ACK:case x.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class Ui{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){const t=Mi(this.reconPack,this.buffers);return this.finishedReconstruction(),t}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const Fi=Object.freeze(Object.defineProperty({__proto__:null,Decoder:Kt,Encoder:$i,get PacketType(){return x},protocol:Di},Symbol.toStringTag,{value:"Module"}));function J(r,e,t){return r.on(e,t),function(){r.off(e,t)}}const Wi=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class en extends B{constructor(e,t,n){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,n&&n.auth&&(this.auth=n.auth),this._opts=Object.assign({},n),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const e=this.io;this.subs=[J(e,"open",this.onopen.bind(this)),J(e,"packet",this.onpacket.bind(this)),J(e,"error",this.onerror.bind(this)),J(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){var n,s,i;if(Wi.hasOwnProperty(e))throw new Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;const o={type:x.EVENT,data:t};if(o.options={},o.options.compress=this.flags.compress!==!1,typeof t[t.length-1]=="function"){const u=this.ids++,h=t.pop();this._registerAckCallback(u,h),o.id=u}const a=(s=(n=this.io.engine)===null||n===void 0?void 0:n.transport)===null||s===void 0?void 0:s.writable,c=this.connected&&!(!((i=this.io.engine)===null||i===void 0)&&i._hasPingExpired());return this.flags.volatile&&!a||(c?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o)),this.flags={},this}_registerAckCallback(e,t){var n;const s=(n=this.flags.timeout)!==null&&n!==void 0?n:this._opts.ackTimeout;if(s===void 0){this.acks[e]=t;return}const i=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let a=0;a<this.sendBuffer.length;a++)this.sendBuffer[a].id===e&&this.sendBuffer.splice(a,1);t.call(this,new Error("operation has timed out"))},s),o=(...a)=>{this.io.clearTimeoutFn(i),t.apply(this,a)};o.withError=!0,this.acks[e]=o}emitWithAck(e,...t){return new Promise((n,s)=>{const i=(o,a)=>o?s(o):n(a);i.withError=!0,t.push(i),this.emit(e,...t)})}_addToQueue(e){let t;typeof e[e.length-1]=="function"&&(t=e.pop());const n={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((s,...i)=>n!==this._queue[0]?void 0:(s!==null?n.tryCount>this._opts.retries&&(this._queue.shift(),t&&t(s)):(this._queue.shift(),t&&t(null,...i)),n.pending=!1,this._drainQueue())),this._queue.push(n),this._drainQueue()}_drainQueue(e=!1){if(!this.connected||this._queue.length===0)return;const t=this._queue[0];t.pending&&!e||(t.pending=!0,t.tryCount++,this.flags=t.flags,this.emit.apply(this,t.args))}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){typeof this.auth=="function"?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:x.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(n=>String(n.id)===e)){const n=this.acks[e];delete this.acks[e],n.withError&&n.call(this,new Error("socket has been disconnected"))}})}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case x.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case x.EVENT:case x.BINARY_EVENT:this.onevent(e);break;case x.ACK:case x.BINARY_ACK:this.onack(e);break;case x.DISCONNECT:this.ondisconnect();break;case x.CONNECT_ERROR:this.destroy();const n=new Error(e.data.message);n.data=e.data.data,this.emitReserved("connect_error",n);break}}onevent(e){const t=e.data||[];e.id!=null&&t.push(this.ack(e.id)),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length){const t=this._anyListeners.slice();for(const n of t)n.apply(this,e)}super.emit.apply(this,e),this._pid&&e.length&&typeof e[e.length-1]=="string"&&(this._lastOffset=e[e.length-1])}ack(e){const t=this;let n=!1;return function(...s){n||(n=!0,t.packet({type:x.ACK,id:e,data:s}))}}onack(e){const t=this.acks[e.id];typeof t=="function"&&(delete this.acks[e.id],t.withError&&e.data.unshift(null),t.apply(this,e.data))}onconnect(e,t){this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:x.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){const t=this._anyListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){const t=this._anyOutgoingListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const t=this._anyOutgoingListeners.slice();for(const n of t)n.apply(this,e.data)}}}function Te(r){r=r||{},this.ms=r.min||100,this.max=r.max||1e4,this.factor=r.factor||2,this.jitter=r.jitter>0&&r.jitter<=1?r.jitter:0,this.attempts=0}Te.prototype.duration=function(){var r=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),t=Math.floor(e*this.jitter*r);r=Math.floor(e*10)&1?r+t:r-t}return Math.min(r,this.max)|0};Te.prototype.reset=function(){this.attempts=0};Te.prototype.setMin=function(r){this.ms=r};Te.prototype.setMax=function(r){this.max=r};Te.prototype.setJitter=function(r){this.jitter=r};class Lt extends B{constructor(e,t){var n;super(),this.nsps={},this.subs=[],e&&typeof e=="object"&&(t=e,e=void 0),t=t||{},t.path=t.path||"/socket.io",this.opts=t,at(this,t),this.reconnection(t.reconnection!==!1),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor((n=t.randomizationFactor)!==null&&n!==void 0?n:.5),this.backoff=new Te({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(t.timeout==null?2e4:t.timeout),this._readyState="closed",this.uri=e;const s=t.parser||Fi;this.encoder=new s.Encoder,this.decoder=new s.Decoder,this._autoConnect=t.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return e===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return e===void 0?this._reconnectionDelay:(this._reconnectionDelay=e,(t=this.backoff)===null||t===void 0||t.setMin(e),this)}randomizationFactor(e){var t;return e===void 0?this._randomizationFactor:(this._randomizationFactor=e,(t=this.backoff)===null||t===void 0||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return e===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,(t=this.backoff)===null||t===void 0||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(e){if(~this._readyState.indexOf("open"))return this;this.engine=new Ri(this.uri,this.opts);const t=this.engine,n=this;this._readyState="opening",this.skipReconnect=!1;const s=J(t,"open",function(){n.onopen(),e&&e()}),i=a=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",a),e?e(a):this.maybeReconnectOnOpen()},o=J(t,"error",i);if(this._timeout!==!1){const a=this._timeout,c=this.setTimeoutFn(()=>{s(),i(new Error("timeout")),t.close()},a);this.opts.autoUnref&&c.unref(),this.subs.push(()=>{this.clearTimeoutFn(c)})}return this.subs.push(s),this.subs.push(o),this}connect(e){return this.open(e)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const e=this.engine;this.subs.push(J(e,"ping",this.onping.bind(this)),J(e,"data",this.ondata.bind(this)),J(e,"error",this.onerror.bind(this)),J(e,"close",this.onclose.bind(this)),J(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(t){this.onclose("parse error",t)}}ondecoded(e){ot(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){this.emitReserved("error",e)}socket(e,t){let n=this.nsps[e];return n?this._autoConnect&&!n.active&&n.connect():(n=new en(this,e,t),this.nsps[e]=n),n}_destroy(e){const t=Object.keys(this.nsps);for(const n of t)if(this.nsps[n].active)return;this._close()}_packet(e){const t=this.encoder.encode(e);for(let n=0;n<t.length;n++)this.engine.write(t[n],e.options)}cleanup(){this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var n;this.cleanup(),(n=this.engine)===null||n===void 0||n.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const t=this.backoff.duration();this._reconnecting=!0;const n=this.setTimeoutFn(()=>{e.skipReconnect||(this.emitReserved("reconnect_attempt",e.backoff.attempts),!e.skipReconnect&&e.open(s=>{s?(e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",s)):e.onreconnect()}))},t);this.opts.autoUnref&&n.unref(),this.subs.push(()=>{this.clearTimeoutFn(n)})}}onreconnect(){const e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}const Ce={};function Ge(r,e){typeof r=="object"&&(e=r,r=void 0),e=e||{};const t=Ci(r,e.path||"/socket.io"),n=t.source,s=t.id,i=t.path,o=Ce[s]&&i in Ce[s].nsps,a=e.forceNew||e["force new connection"]||e.multiplex===!1||o;let c;return a?c=new Lt(n,e):(Ce[s]||(Ce[s]=new Lt(n,e)),c=Ce[s]),t.query&&!e.query&&(e.query=t.queryKey),c.socket(t.path,e)}Object.assign(Ge,{Manager:Lt,Socket:en,io:Ge,connect:Ge});class Yt{constructor(e){this.ws=null,this.socket=null,this.listeners=new Map,this.reconnectTimer=null,this.heartbeatTimer=null,this.reconnectAttempts=0,this.state="disconnected",this.messageQueue=[],this.subscriptions=new Set,this.lastActivity=Date.now(),this.config={reconnect:!0,reconnectInterval:3e3,maxReconnectAttempts:5,heartbeatInterval:3e4,heartbeatMessage:JSON.stringify({type:"ping"}),useSocketIO:!1,socketOptions:{transports:["websocket"],upgrade:!0,timeout:1e4},...e},["connecting","open","close","error","message","reconnect"].forEach(n=>{this.listeners.set(n,new Set)})}connect(){if(!this.isConnected()){this.state="connecting",this.emit("connecting",{state:this.state});try{this.config.useSocketIO?this.connectSocketIO():this.connectWebSocket()}catch(e){this.state="error",this.emit("error",e),this.handleReconnect()}}}connectWebSocket(){this.ws=new WebSocket(this.config.url,this.config.protocols),this.setupWebSocketHandlers()}connectSocketIO(){this.socket=Ge(this.config.url,{autoConnect:!1,...this.config.socketOptions}),this.setupSocketIOHandlers(),this.socket.connect()}isConnected(){return this.config.useSocketIO?this.socket?.connected||!1:this.ws?.readyState===WebSocket.OPEN||!1}disconnect(){this.config.reconnect=!1,this.clearTimers(),this.config.useSocketIO&&this.socket?(this.socket.disconnect(),this.socket=null):this.ws&&(this.ws.close(),this.ws=null),this.state="disconnected",this.subscriptions.clear(),this.emit("close",{code:1e3,reason:"Manual disconnect"})}send(e,t){if(this.lastActivity=Date.now(),this.isConnected()){if(this.config.useSocketIO&&this.socket)this.socket.emit(e,t);else if(this.ws){const n=typeof t=="string"?t:JSON.stringify({type:e,data:t,timestamp:Date.now()});this.ws.send(n)}}else this.messageQueue.push({type:e,data:t,timestamp:Date.now()}),this.state==="disconnected"&&this.connect()}subscribe(e,t){const n=`${e}:${JSON.stringify(t||{})}`;this.subscriptions.has(n)||(this.subscriptions.add(n),this.send("subscribe",{channel:e,params:t}),this.config.useSocketIO&&this.socket&&this.socket.on(e,s=>{this.emit("message",{type:e,data:s,timestamp:Date.now()})}))}unsubscribe(e,t){const n=`${e}:${JSON.stringify(t||{})}`;this.subscriptions.has(n)&&(this.subscriptions.delete(n),this.send("unsubscribe",{channel:e,params:t}),this.config.useSocketIO&&this.socket&&this.socket.off(e))}on(e,t){const n=this.listeners.get(e);n&&n.add(t)}off(e,t){const n=this.listeners.get(e);n&&n.delete(t)}getState(){return this.state}getWebSocket(){return this.ws}setupWebSocketHandlers(){this.ws&&(this.ws.onopen=e=>{this.state="connected",this.reconnectAttempts=0,this.emit("open",e),this.flushMessageQueue(),this.startHeartbeat(),this.resubscribeAll()},this.ws.onclose=e=>{this.state="disconnected",this.clearTimers(),this.emit("close",e),this.config.reconnect&&e.code!==1e3&&this.handleReconnect()},this.ws.onerror=e=>{this.state="error",this.emit("error",e)},this.ws.onmessage=e=>{this.lastActivity=Date.now();try{const t=JSON.parse(e.data);this.emit("message",t)}catch{this.emit("message",{type:"raw",data:e.data})}})}setupSocketIOHandlers(){this.socket&&(this.socket.on("connect",()=>{this.state="connected",this.reconnectAttempts=0,this.emit("open",{socket:this.socket}),this.flushMessageQueue(),this.startHeartbeat(),this.resubscribeAll()}),this.socket.on("disconnect",e=>{this.state="disconnected",this.clearTimers(),this.emit("close",{reason:e}),this.config.reconnect&&e!=="io client disconnect"&&this.handleReconnect()}),this.socket.on("connect_error",e=>{this.state="error",this.emit("error",e),this.config.reconnect&&this.handleReconnect()}),this.socket.on("reconnect_attempt",e=>{this.state="reconnecting",this.emit("reconnect",{attempt:e,maxAttempts:this.config.maxReconnectAttempts})}),this.socket.onAny((e,...t)=>{["connect","disconnect","connect_error","reconnect_attempt"].includes(e)||(this.lastActivity=Date.now(),this.emit("message",{type:e,data:t.length===1?t[0]:t,timestamp:Date.now()}))}))}resubscribeAll(){const e=Array.from(this.subscriptions);this.subscriptions.clear(),e.forEach(t=>{const[n,s]=t.split(":"),i=s?JSON.parse(s):void 0;this.subscribe(n,i)})}emit(e,t){const n=this.listeners.get(e);n&&n.forEach(s=>{try{s(t)}catch(i){console.error(`WebSocket事件监听器错误 (${e}):`,i)}})}handleReconnect(){!this.config.reconnect||this.reconnectAttempts>=(this.config.maxReconnectAttempts||5)||(this.state="reconnecting",this.reconnectAttempts++,this.emit("reconnect",{attempt:this.reconnectAttempts,maxAttempts:this.config.maxReconnectAttempts}),this.reconnectTimer=window.setTimeout(()=>{this.connect()},this.config.reconnectInterval))}startHeartbeat(){!this.config.heartbeatInterval||!this.config.heartbeatMessage||(this.heartbeatTimer=window.setInterval(()=>{this.ws&&this.ws.readyState===WebSocket.OPEN&&this.ws.send(this.config.heartbeatMessage)},this.config.heartbeatInterval))}clearTimers(){this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.heartbeatTimer&&(clearInterval(this.heartbeatTimer),this.heartbeatTimer=null)}flushMessageQueue(){for(;this.messageQueue.length>0;){const e=this.messageQueue.shift();e&&this.send(e.type,e.data)}}getStats(){return{state:this.state,reconnectAttempts:this.reconnectAttempts,queuedMessages:this.messageQueue.length,subscriptions:this.subscriptions.size,lastActivity:this.lastActivity,url:this.config.url,useSocketIO:this.config.useSocketIO,connected:this.isConnected()}}isActive(){return Date.now()-this.lastActivity<6e4}getSubscriptions(){return Array.from(this.subscriptions)}}class tn extends Yt{constructor(e=!1){const t=ye.wsUrl+qt.MARKET;super({url:t,reconnect:!0,reconnectInterval:3e3,maxReconnectAttempts:10,heartbeatInterval:3e4,useSocketIO:e,socketOptions:{transports:["websocket"],upgrade:!0,timeout:1e4,forceNew:!0}})}subscribeQuote(e){this.subscribe("quote",{symbols:e})}unsubscribeQuote(e){this.unsubscribe("quote",{symbols:e})}subscribeKLine(e,t){this.subscribe("kline",{symbol:e,period:t})}subscribeDepth(e){this.subscribe("depth",{symbol:e})}batchSubscribe(e){e.quotes&&this.subscribeQuote(e.quotes),e.klines&&e.klines.forEach(({symbol:t,period:n})=>{this.subscribeKLine(t,n)}),e.depths&&e.depths.forEach(t=>{this.subscribeDepth(t)})}}class rn extends Yt{constructor(e=!1){const t=ye.wsUrl+qt.TRADING;super({url:t,reconnect:!0,reconnectInterval:3e3,maxReconnectAttempts:5,heartbeatInterval:3e4,useSocketIO:e,socketOptions:{transports:["websocket"],upgrade:!0,timeout:1e4}})}subscribeAccount(){this.subscribe("account")}subscribeOrders(){this.subscribe("orders")}subscribePositions(){this.subscribe("positions")}placeOrder(e){this.send("place_order",e)}cancelOrder(e){this.send("cancel_order",{orderId:e})}}class nn extends Yt{constructor(e=!1){const t=ye.wsUrl+qt.STRATEGY;super({url:t,reconnect:!0,reconnectInterval:3e3,maxReconnectAttempts:5,heartbeatInterval:3e4,useSocketIO:e,socketOptions:{transports:["websocket"],upgrade:!0,timeout:1e4}})}subscribeStrategyStatus(e){this.subscribe("strategy_status",{strategyId:e})}subscribeStrategySignals(e){this.subscribe("strategy_signals",{strategyId:e})}subscribeBacktestProgress(e){this.subscribe("backtest_progress",{backtestId:e})}startStrategy(e,t){this.send("start_strategy",{strategyId:e,params:t})}stopStrategy(e){this.send("stop_strategy",{strategyId:e})}}const Y=new tn(!0),qi=new rn(!0),Vi=new nn(!0);new tn(!1);new rn(!1);new nn(!1);class zi{constructor(){this.market=xt,this.strategy=ne,this.backtest=Ks,this.user=Q,this.marketWS=Y,this.tradingWS=qi,this.strategyWS=Vi}async initialize(){try{const e=typeof window<"u"?localStorage.getItem("access_token"):null;if(e)try{await this.user.getUserInfo()}catch{typeof window<"u"&&(localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_info"))}e&&this.connectWebSockets(),console.log("API管理器初始化完成")}catch(e){console.error("API管理器初始化失败:",e)}}connectWebSockets(){try{this.marketWS.connect(),this.marketWS.on("error",e=>{console.error("市场数据WebSocket连接错误:",e)}),this.marketWS.on("reconnect",e=>{console.log(`市场数据WebSocket重连中... (${e.attempt}/${e.maxAttempts})`)}),console.log("WebSocket连接已建立")}catch(e){console.error("WebSocket连接失败:",e)}}disconnectWebSockets(){try{this.marketWS.disconnect(),this.tradingWS.disconnect(),this.strategyWS.disconnect(),console.log("WebSocket连接已断开")}catch(e){console.error("断开WebSocket连接失败:",e)}}async onUserLogin(){try{this.connectWebSockets(),console.log("用户登录后初始化完成")}catch(e){console.error("用户登录后初始化失败:",e)}}async onUserLogout(){try{this.disconnectWebSockets(),typeof window<"u"&&(localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_info")),console.log("用户登出后清理完成")}catch(e){console.error("用户登出后清理失败:",e)}}async getHealthStatus(){const e={api:!1,websocket:!1,services:{market:!1,strategy:!1,backtest:!1,user:!1}};try{const t=[this.market.getOverview().then(()=>{e.services.market=!0}).catch(()=>{}),this.strategy.getTemplates().then(()=>{e.services.strategy=!0}).catch(()=>{}),this.backtest.getHealth().then(()=>{e.services.backtest=!0}).catch(()=>{}),this.user.getUserInfo().then(()=>{e.services.user=!0}).catch(()=>{})];return await Promise.allSettled(t),e.api=Object.values(e.services).some(n=>n),e.websocket=this.marketWS.getState()==="connected",e}catch(t){return console.error("获取API健康状态失败:",t),e}}}const Qe=new zi,[Hi,ve]=D({quotes:{},overview:null,watchlist:["000001","000002","399001","399006"],isLoading:!1,error:null,lastUpdate:0});class ji{get state(){return Hi()}setLoading(e){ve(t=>({...t,isLoading:e}))}setError(e){ve(t=>({...t,error:e}))}updateQuotes(e){const t={};e.forEach(n=>{t[n.symbol]=n}),ve(n=>({...n,quotes:{...n.quotes,...t},lastUpdate:Date.now(),error:null}))}updateOverview(e){ve(t=>({...t,overview:e,lastUpdate:Date.now(),error:null}))}addToWatchlist(e){ve(t=>{if(!t.watchlist.includes(e)){const n=[...t.watchlist,e];return this.subscribeQuotes([e]),{...t,watchlist:n}}return t})}removeFromWatchlist(e){ve(t=>{const n=t.watchlist.filter(s=>s!==e);return Y.unsubscribeQuote([e]),{...t,watchlist:n}})}async fetchQuotes(e){const t=e||this.state.watchlist;if(t.length!==0)try{this.setLoading(!0);const n=await xt.getQuote(t);this.updateQuotes(n)}catch(n){console.error("获取行情失败:",n),this.setError(n instanceof Error?n.message:"获取行情失败")}finally{this.setLoading(!1)}}async fetchOverview(){try{this.setLoading(!0);const e=await xt.getOverview();this.updateOverview(e)}catch(e){console.error("获取市场概览失败:",e),this.setError(e instanceof Error?e.message:"获取市场概览失败")}finally{this.setLoading(!1)}}subscribeQuotes(e){Y.getState()==="connected"&&Y.subscribeQuote(e)}unsubscribeQuotes(e){Y.getState()==="connected"&&Y.unsubscribeQuote(e)}initializeWebSocket(){Y.on("open",()=>{console.log("市场数据WebSocket已连接"),this.state.watchlist.length>0&&this.subscribeQuotes(this.state.watchlist)}),Y.on("message",e=>{e.type==="quote"?this.updateQuotes([e.data]):e.type==="overview"&&this.updateOverview(e.data)}),Y.on("error",e=>{console.error("市场数据WebSocket错误:",e),this.setError("实时数据连接异常")}),Y.on("close",()=>{console.log("市场数据WebSocket连接已关闭")})}cleanup(){Y.disconnect()}}const le=new ji;Ae(()=>{le.initializeWebSocket();const r=setInterval(()=>{le.fetchOverview()},3e4),e=setInterval(()=>{Y.getState()!=="connected"&&le.fetchQuotes()},5e3);Ne(()=>{clearInterval(r),clearInterval(e),le.cleanup()})});le.fetchOverview();le.fetchQuotes();const[Ki,z]=D({strategies:[],templates:[],currentStrategy:null,isLoading:!1,error:null,pagination:{page:1,pageSize:10,total:0,totalPages:0},filters:{keyword:"",type:"",status:"",riskLevel:""}});class Yi{get state(){return Ki()}setLoading(e){z(t=>({...t,isLoading:e}))}setError(e){z(t=>({...t,error:e}))}updateStrategies(e,t){z(n=>({...n,strategies:e,pagination:t?{...n.pagination,...t}:n.pagination,error:null}))}updateTemplates(e){z(t=>({...t,templates:e,error:null}))}setCurrentStrategy(e){z(t=>({...t,currentStrategy:e}))}updateFilters(e){z(t=>({...t,filters:{...t.filters,...e},pagination:{...t.pagination,page:1}}))}updatePagination(e){z(t=>({...t,pagination:{...t.pagination,...e}}))}async fetchStrategies(e){try{this.setLoading(!0);const t={...this.state.pagination,...this.state.filters,...e};Object.keys(t).forEach(s=>{const i=t[s];(i===""||i===null||i===void 0)&&delete t[s]});const n=await ne.getStrategies(t);this.updateStrategies(n.items,{page:n.page,pageSize:n.pageSize,total:n.total,totalPages:n.totalPages})}catch(t){console.error("获取策略列表失败:",t),this.setError(t instanceof Error?t.message:"获取策略列表失败")}finally{this.setLoading(!1)}}async fetchStrategy(e){try{this.setLoading(!0);const t=await ne.getStrategy(e);this.setCurrentStrategy(t);const n=this.state.strategies.findIndex(s=>s.id===e);z(n===-1?s=>({...s,strategies:[t,...s.strategies]}):s=>({...s,strategies:s.strategies.map(i=>i.id===e?t:i)}))}catch(t){console.error("获取策略详情失败:",t),this.setError(t instanceof Error?t.message:"获取策略详情失败")}finally{this.setLoading(!1)}}async fetchTemplates(){try{this.setLoading(!0);const e=await ne.getTemplates();this.updateTemplates(e)}catch(e){console.error("获取策略模板失败:",e),this.setError(e instanceof Error?e.message:"获取策略模板失败")}finally{this.setLoading(!1)}}async createStrategy(e){try{this.setLoading(!0);const t=await ne.createStrategy(e);return z(n=>({...n,strategies:[t,...n.strategies],currentStrategy:t,pagination:{...n.pagination,total:n.pagination.total+1}})),t}catch(t){throw console.error("创建策略失败:",t),this.setError(t instanceof Error?t.message:"创建策略失败"),t}finally{this.setLoading(!1)}}async updateStrategy(e){try{this.setLoading(!0);const t=await ne.updateStrategy(e);return z(n=>({...n,strategies:n.strategies.map(s=>s.id===e.id?t:s),currentStrategy:n.currentStrategy?.id===e.id?t:n.currentStrategy})),t}catch(t){throw console.error("更新策略失败:",t),this.setError(t instanceof Error?t.message:"更新策略失败"),t}finally{this.setLoading(!1)}}async deleteStrategy(e){try{this.setLoading(!0),await ne.deleteStrategy(e),z(t=>({...t,strategies:t.strategies.filter(n=>n.id!==e),currentStrategy:t.currentStrategy?.id===e?null:t.currentStrategy,pagination:{...t.pagination,total:Math.max(0,t.pagination.total-1)}}))}catch(t){throw console.error("删除策略失败:",t),this.setError(t instanceof Error?t.message:"删除策略失败"),t}finally{this.setLoading(!1)}}async startStrategy(e){try{await ne.startStrategy(e),z(t=>({...t,strategies:t.strategies.map(n=>n.id===e?{...n,status:"active"}:n),currentStrategy:t.currentStrategy?.id===e?{...t.currentStrategy,status:"active"}:t.currentStrategy}))}catch(t){throw console.error("启动策略失败:",t),this.setError(t instanceof Error?t.message:"启动策略失败"),t}}async stopStrategy(e){try{await ne.stopStrategy(e),z(t=>({...t,strategies:t.strategies.map(n=>n.id===e?{...n,status:"inactive"}:n),currentStrategy:t.currentStrategy?.id===e?{...t.currentStrategy,status:"inactive"}:t.currentStrategy}))}catch(t){throw console.error("停止策略失败:",t),this.setError(t instanceof Error?t.message:"停止策略失败"),t}}async searchStrategies(e){this.updateFilters({keyword:e}),await this.fetchStrategies()}async filterByType(e){this.updateFilters({type:e}),await this.fetchStrategies()}async filterByStatus(e){this.updateFilters({status:e}),await this.fetchStrategies()}async clearFilters(){this.updateFilters({keyword:"",type:"",status:"",riskLevel:""}),await this.fetchStrategies()}async refresh(){await Promise.all([this.fetchStrategies(),this.fetchTemplates()])}}const It=new Yi;Ae(()=>{It.fetchStrategies(),It.fetchTemplates()});const[Gi,_e]=D({user:null,isAuthenticated:!1,isLoading:!1,error:null,loginAttempts:0,lastLoginTime:null});class Qi{get state(){return Gi()}setLoading(e){_e(t=>({...t,isLoading:e}))}setError(e){_e(t=>({...t,error:e}))}setUser(e){_e(t=>({...t,user:e,isAuthenticated:!!e,error:null}))}incrementLoginAttempts(){_e(e=>({...e,loginAttempts:e.loginAttempts+1}))}resetLoginAttempts(){_e(e=>({...e,loginAttempts:0}))}setLastLoginTime(e){_e(t=>({...t,lastLoginTime:e}))}async login(e){try{this.setLoading(!0),this.setError(null);const t=await Q.login(e);return this.setUser(t.user),this.resetLoginAttempts(),this.setLastLoginTime(Date.now()),await Qe.onUserLogin(),t}catch(t){console.error("登录失败:",t),this.incrementLoginAttempts();const n=t instanceof Error?t.message:"登录失败";throw this.setError(n),t}finally{this.setLoading(!1)}}async register(e){try{return this.setLoading(!0),this.setError(null),await Q.register(e)}catch(t){console.error("注册失败:",t);const n=t instanceof Error?t.message:"注册失败";throw this.setError(n),t}finally{this.setLoading(!1)}}async logout(){try{this.setLoading(!0),await Q.logout(),await Qe.onUserLogout(),this.setUser(null),this.resetLoginAttempts(),this.setLastLoginTime(null)}catch(e){console.error("登出失败:",e),this.setUser(null),this.resetLoginAttempts(),this.setLastLoginTime(null)}finally{this.setLoading(!1)}}async fetchUserInfo(){try{this.setLoading(!0);const e=await Q.getUserInfo();return this.setUser(e),e}catch(e){throw console.error("获取用户信息失败:",e),e instanceof Error&&e.message.includes("401")&&(this.setUser(null),await Qe.onUserLogout()),this.setError(e instanceof Error?e.message:"获取用户信息失败"),e}finally{this.setLoading(!1)}}async updateProfile(e){try{this.setLoading(!0);const t=await Q.updateProfile(e);return this.setUser(t),t}catch(t){throw console.error("更新用户资料失败:",t),this.setError(t instanceof Error?t.message:"更新用户资料失败"),t}finally{this.setLoading(!1)}}async changePassword(e){try{this.setLoading(!0),await Q.changePassword(e),this.setError(null)}catch(t){throw console.error("修改密码失败:",t),this.setError(t instanceof Error?t.message:"修改密码失败"),t}finally{this.setLoading(!1)}}async resetPassword(e){try{this.setLoading(!0),await Q.resetPassword({email:e}),this.setError(null)}catch(t){throw console.error("重置密码失败:",t),this.setError(t instanceof Error?t.message:"重置密码失败"),t}finally{this.setLoading(!1)}}async uploadAvatar(e){try{this.setLoading(!0);const t=await Q.uploadAvatar(e);if(this.state.user){const n={...this.state.user,avatar:t.url};this.setUser(n)}return t}catch(t){throw console.error("上传头像失败:",t),this.setError(t instanceof Error?t.message:"上传头像失败"),t}finally{this.setLoading(!1)}}async updatePreferences(e){try{this.setLoading(!0);const t=await Q.updatePreferences(e);if(this.state.user){const n={...this.state.user,preferences:t};this.setUser(n)}return t}catch(t){throw console.error("更新用户偏好失败:",t),this.setError(t instanceof Error?t.message:"更新用户偏好失败"),t}finally{this.setLoading(!1)}}async checkAuthStatus(){if(!(typeof window<"u"?localStorage.getItem("access_token"):null))return this.setUser(null),!1;try{return await this.fetchUserInfo(),!0}catch{return typeof window<"u"&&(localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_info")),this.setUser(null),!1}}async refreshToken(){try{return await Q.refreshToken()}catch(e){throw console.error("刷新token失败:",e),await this.logout(),e}}getUserPermissions(){const e=this.state.user;if(!e)return[];const t=[];switch(t.push("read:profile","update:profile"),e.role){case"admin":t.push("admin:*");break;case"vip":t.push("vip:*","create:strategy","run:backtest");break;case"user":t.push("create:strategy","run:backtest");break}return t}hasPermission(e){const t=this.getUserPermissions();return t.includes(e)||t.includes("admin:*")}}const he=new Qi;Ae(()=>{he.checkAuthStatus()});class Xi{constructor(){this.market=le,this.strategy=It,this.user=he}async initialize(){try{console.log("初始化全局状态管理器..."),await this.user.checkAuthStatus()&&await Promise.allSettled([this.market.fetchOverview(),this.strategy.fetchStrategies(),this.strategy.fetchTemplates()]),console.log("全局状态管理器初始化完成")}catch(e){console.error("全局状态管理器初始化失败:",e)}}cleanup(){try{this.market.cleanup(),console.log("全局状态管理器清理完成")}catch(e){console.error("全局状态管理器清理失败:",e)}}reset(){console.log("重置全局状态管理器")}getAppState(){return{market:this.market.state,strategy:this.strategy.state,user:this.user.state,timestamp:Date.now()}}getLoadingState(){return{market:this.market.state.isLoading,strategy:this.strategy.state.isLoading,user:this.user.state.isLoading,isAnyLoading:this.market.state.isLoading||this.strategy.state.isLoading||this.user.state.isLoading}}getErrorState(){return{market:this.market.state.error,strategy:this.strategy.state.error,user:this.user.state.error,hasAnyError:!!(this.market.state.error||this.strategy.state.error||this.user.state.error)}}async refreshAll(){try{this.user.state.isAuthenticated&&await Promise.allSettled([this.market.fetchOverview(),this.market.fetchQuotes(),this.strategy.refresh(),this.user.fetchUserInfo()])}catch(e){console.error("刷新数据失败:",e)}}}const Ji=new Xi;var Zi=K("<div style=border-radius:8px;align-items:center;justify-content:center;font-size:14px;font-weight:bold>Q"),eo=K("<div style=align-items:center;border-radius:8px><img alt=用户头像 style=border-radius:50%><span style=font-size:14px;font-weight:500></span><button title=退出登录 style=border-radius:4px;font-size:12px>🚪"),to=K('<header style="border-bottom:1px solid #e5e7eb;box-shadow:0 1px 3px 0 rgba(0, 0, 0, 0.1);z-index:50"><div style=max-width:1280px;align-items:center;justify-content:space-between><nav style=align-items:center></nav><div style=align-items:center><div style=align-items:center;border-radius:9999px;font-size:12px;font-weight:500><div style=border-radius:50%></div>已连接</div><button type=button style=border-radius:6px>');function ro(){const{theme:r,toggleTheme:e}=Ms(),t=()=>he.state.isAuthenticated,n=()=>he.state.user,s=[{path:"/",label:"仪表盘"},{path:"/market",label:"市场数据"},{path:"/strategy",label:"策略编辑"},{path:"/backtest",label:"回测分析"},{path:"/login",label:"登录演示"}];return(()=>{var i=to(),o=i.firstChild,a=o.firstChild,c=a.nextSibling,l=c.firstChild,u=l.firstChild,h=l.nextSibling;return f(i,"background","white"),f(i,"position","sticky"),f(i,"top","0"),f(o,"margin","0 auto"),f(o,"padding","12px 16px"),f(o,"display","flex"),H(o,b(gt,{href:"/",style:{display:"flex","align-items":"center",gap:"12px","font-size":"1.25rem","font-weight":"bold",color:"#3b82f6","text-decoration":"none"},get children(){return[(()=>{var d=Zi();return f(d,"width","32px"),f(d,"height","32px"),f(d,"background","linear-gradient(to bottom right, #3b82f6, #1d4ed8)"),f(d,"display","flex"),f(d,"color","white"),d})(),"量化交易平台"]}}),a),f(a,"display","flex"),f(a,"gap","4px"),H(a,()=>s.map(d=>b(gt,{get href(){return d.path},style:{display:"flex","align-items":"center",gap:"8px",padding:"8px 12px","border-radius":"6px","font-size":"14px","font-weight":"500",color:"#6b7280","text-decoration":"none",transition:"all 0.2s ease"},get children(){return d.label}}))),f(c,"display","flex"),f(c,"gap","12px"),f(l,"display","flex"),f(l,"gap","8px"),f(l,"padding","4px 8px"),f(l,"background","#dcfce7"),f(l,"color","#15803d"),f(u,"width","8px"),f(u,"height","8px"),f(u,"background","#22c55e"),H(c,b(st,{get when(){return t()},get fallback(){return b(gt,{href:"/login",style:{padding:"8px 16px","border-radius":"6px",background:"#3b82f6",color:"white","text-decoration":"none","font-size":"14px","font-weight":"500",transition:"all 0.2s ease"},children:"登录"})},get children(){var d=eo(),g=d.firstChild,w=g.nextSibling,p=w.nextSibling;return f(d,"display","flex"),f(d,"gap","8px"),f(d,"padding","4px 8px"),f(d,"background","#f3f4f6"),f(d,"border","1px solid #e5e7eb"),f(g,"width","24px"),f(g,"height","24px"),f(w,"color","#374151"),H(w,()=>n()?.nickname),p.$$click=async()=>{confirm("确定要退出登录吗？")&&(await he.logout(),window.location.href="/login")},f(p,"background","none"),f(p,"border","none"),f(p,"color","#6b7280"),f(p,"cursor","pointer"),f(p,"padding","4px"),M(()=>Le(g,"src",n()?.avatar)),d}}),h),Pr(h,"click",e,!0),f(h,"padding","8px"),f(h,"color","#6b7280"),f(h,"background","transparent"),f(h,"border","none"),f(h,"cursor","pointer"),f(h,"transition","all 0.2s ease"),H(h,()=>r()==="light"?"🌙":"☀️"),M(()=>Le(h,"title",r()==="light"?"切换到深色模式":"切换到浅色模式")),i})()}Ut(["click"]);var no=K('<footer style="border-top:1px solid #e5e7eb"><div style=max-width:1280px;text-align:center><div style=flex-direction:column;align-items:center;justify-content:space-between><div style=font-size:14px>© 2024 量化交易前端平台. 基于 SolidJS 构建，专注性能与体验.</div><div style=align-items:center;font-size:12px><span style=align-items:center><div style=border-radius:50%></div>SolidJS</span><span style=align-items:center><div style=border-radius:50%></div>TypeScript</span><span style=align-items:center><div style=border-radius:50%></div>Vite');function so(){return(()=>{var r=no(),e=r.firstChild,t=e.firstChild,n=t.firstChild,s=n.nextSibling,i=s.firstChild,o=i.firstChild,a=i.nextSibling,c=a.firstChild,l=a.nextSibling,u=l.firstChild;return f(r,"background","white"),f(r,"padding","24px 0"),f(e,"margin","0 auto"),f(e,"padding","0 16px"),f(t,"display","flex"),f(t,"gap","16px"),f(n,"color","#6b7280"),f(s,"display","flex"),f(s,"gap","16px"),f(s,"color","#9ca3af"),f(i,"display","flex"),f(i,"gap","4px"),f(o,"width","8px"),f(o,"height","8px"),f(o,"background","#3b82f6"),f(a,"display","flex"),f(a,"gap","4px"),f(c,"width","8px"),f(c,"height","8px"),f(c,"background","#22c55e"),f(l,"display","flex"),f(l,"gap","4px"),f(u,"width","8px"),f(u,"height","8px"),f(u,"background","#f59e0b"),r})()}var St=K("<div style=border-radius:50%>"),mr=K("<div style=align-items:center>"),io=K("<div>"),oo=K("<div style=font-size:14px;text-align:center;max-width:300px>"),ao=K(`<div><style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        @keyframes bounce {
          0%, 80%, 100% {
            transform: scale(0);
            opacity: 0.5;
          }
          40% {
            transform: scale(1);
            opacity: 1;
          }
        }
        
        @keyframes pulse {
          0% {
            transform: scale(0);
            opacity: 1;
          }
          100% {
            transform: scale(1);
            opacity: 0;
          }
        }
        
        @keyframes bars {
          0%, 40%, 100% {
            transform: scaleY(0.4);
            opacity: 0.5;
          }
          20% {
            transform: scaleY(1);
            opacity: 1;
          }
        }
      `);function co(r){const e=()=>{switch(r.size||"medium"){case"small":return"24px";case"large":return"64px";default:return"40px"}},t=()=>({display:"flex","align-items":"center","justify-content":"center","flex-direction":"column",padding:r.overlay?"0":"20px",gap:"16px",...r.overlay&&{position:"fixed",top:"0",left:"0",right:"0",bottom:"0",background:r.theme==="dark"?"rgba(0, 0, 0, 0.7)":"rgba(255, 255, 255, 0.8)","z-index":"9999","backdrop-filter":"blur(4px)"}}),n=r.color||"#3b82f6",s=r.theme==="dark"?"#374151":"#f3f4f6",i=()=>(()=>{var u=St();return f(u,"border",`4px solid ${s}`),f(u,"border-top",`4px solid ${n}`),f(u,"animation","spin 1s linear infinite"),M(h=>{var d=e(),g=e();return d!==h.e&&f(u,"width",h.e=d),g!==h.t&&f(u,"height",h.t=g),h},{e:void 0,t:void 0}),u})(),o=()=>(()=>{var u=mr();return f(u,"display","flex"),f(u,"gap","4px"),H(u,()=>[0,1,2].map(h=>(()=>{var d=St();return f(d,"background-color",n),f(d,"animation","bounce 1.4s ease-in-out both infinite"),f(d,"animation-delay",`${h*.16}s`),M(g=>{var w=`calc(${e()} / 4)`,p=`calc(${e()} / 4)`;return w!==g.e&&f(d,"width",g.e=w),p!==g.t&&f(d,"height",g.t=p),g},{e:void 0,t:void 0}),d})())),u})(),a=()=>(()=>{var u=St();return f(u,"background-color",n),f(u,"animation","pulse 1.5s ease-in-out infinite"),M(h=>{var d=e(),g=e();return d!==h.e&&f(u,"width",h.e=d),g!==h.t&&f(u,"height",h.t=g),h},{e:void 0,t:void 0}),u})(),c=()=>(()=>{var u=mr();return f(u,"display","flex"),f(u,"gap","4px"),H(u,()=>[0,1,2,3,4].map(h=>(()=>{var d=io();return f(d,"height","100%"),f(d,"background-color",n),f(d,"animation","bars 1.2s ease-in-out infinite"),f(d,"animation-delay",`${h*.1}s`),M(g=>f(d,"width",`calc(${e()} / 8)`)),d})())),M(h=>f(u,"height",e())),u})(),l=()=>{switch(r.type||"spinner"){case"dots":return b(o,{});case"pulse":return b(a,{});case"bars":return b(c,{});default:return b(i,{})}};return(()=>{var u=ao(),h=u.firstChild;return H(u,l,h),H(u,b(st,{get when(){return r.message},get children(){var d=oo();return H(d,()=>r.message),M(g=>f(d,"color",r.theme==="dark"?"#d1d5db":"#374151")),d}}),h),M(d=>Or(u,t(),d)),u})()}function sn(r){const e=ps(),t=$r(),{requireAuth:n=!1,requireRoles:s=[],redirectTo:i="/login"}=r;return Ae(()=>{const o=he.state.isAuthenticated,a=he.state.user;if(n&&!o){console.log("Route guard: User not authenticated, redirecting to login"),e(i+`?redirect=${encodeURIComponent(t.pathname)}`);return}if(s.length>0&&a&&!s.includes(a.role)){console.log("Route guard: User does not have required role, redirecting"),e("/unauthorized");return}}),Cr(()=>r.children)}const lo=r=>b(sn,{requireAuth:!0,requireRoles:["admin"],get children(){return r.children}}),Ve=r=>b(sn,{requireAuth:!0,get children(){return r.children}});function tt(r){return typeof r=="object"&&r!=null&&!Array.isArray(r)}function uo(r){return Object.fromEntries(Object.entries(r??{}).filter(([e,t])=>t!==void 0))}var ho=r=>r==="base";function fo(r){return r.slice().filter(e=>!ho(e))}function yr(r){return String.fromCharCode(r+(r>25?39:97))}function go(r){let e="",t;for(t=Math.abs(r);t>52;t=t/52|0)e=yr(t%52)+e;return yr(t%52)+e}function po(r,e){let t=e.length;for(;t;)r=r*33^e.charCodeAt(--t);return r}function mo(r){return go(po(5381,r)>>>0)}var on=/\s*!(important)?/i;function yo(r){return typeof r=="string"?on.test(r):!1}function bo(r){return typeof r=="string"?r.replace(on,"").trim():r}function an(r){return typeof r=="string"?r.replaceAll(" ","_"):r}var Gt=r=>{const e=new Map;return(...n)=>{const s=JSON.stringify(n);if(e.has(s))return e.get(s);const i=r(...n);return e.set(s,i),i}};function cn(...r){return r.filter(Boolean).reduce((t,n)=>(Object.keys(n).forEach(s=>{const i=t[s],o=n[s];tt(i)&&tt(o)?t[s]=cn(i,o):t[s]=o}),t),{})}var wo=r=>r!=null;function ln(r,e,t={}){const{stop:n,getKey:s}=t;function i(o,a=[]){if(tt(o)||Array.isArray(o)){const c={};for(const[l,u]of Object.entries(o)){const h=s?.(l,u)??l,d=[...a,h];if(n?.(o,d))return e(o,a);const g=i(u,d);wo(g)&&(c[h]=g)}return c}return e(o,a)}return i(r)}function So(r,e){return r.reduce((t,n,s)=>{const i=e[s];return n!=null&&(t[i]=n),t},{})}function un(r,e,t=!0){const{utility:n,conditions:s}=e,{hasShorthand:i,resolveShorthand:o}=n;return ln(r,a=>Array.isArray(a)?So(a,s.breakpoints.keys):a,{stop:a=>Array.isArray(a),getKey:t?a=>i?o(a):a:void 0})}var ko={shift:r=>r,finalize:r=>r,breakpoints:{keys:[]}},vo=r=>typeof r=="string"?r.replaceAll(/[\n\s]+/g," "):r;function _o(r){const{utility:e,hash:t,conditions:n=ko}=r,s=o=>[e.prefix,o].filter(Boolean).join("-"),i=(o,a)=>{let c;if(t){const l=[...n.finalize(o),a];c=s(e.toHash(l,mo))}else c=[...n.finalize(o),s(a)].join(":");return c};return Gt(({base:o,...a}={})=>{const c=Object.assign(a,o),l=un(c,r),u=new Set;return ln(l,(h,d)=>{const g=yo(h);if(h==null)return;const[w,...p]=n.shift(d),y=fo(p),k=e.transform(w,bo(vo(h)));let S=i(y,k.className);g&&(S=`${S}!`),u.add(S)}),Array.from(u).join(" ")})}function Eo(...r){return r.flat().filter(e=>tt(e)&&Object.keys(uo(e)).length>0)}function Ao(r){function e(s){const i=Eo(...s);return i.length===1?i:i.map(o=>un(o,r))}function t(...s){return cn(...e(s))}function n(...s){return Object.assign({},...e(s))}return{mergeCss:Gt(t),assignCss:n}}var xo=/([A-Z])/g,To=/^ms-/,Ro=Gt(r=>r.startsWith("--")?r:r.replace(xo,"-$1").replace(To,"-ms-").toLowerCase()),Co="cm,mm,Q,in,pc,pt,px,em,ex,ch,rem,lh,rlh,vw,vh,vmin,vmax,vb,vi,svw,svh,lvw,lvh,dvw,dvh,cqw,cqh,cqi,cqb,cqmin,cqmax,%";`${Co.split(",").join("|")}`;const Po="_dark,_light,_hover,_focus,_focusWithin,_focusVisible,_disabled,_active,_visited,_target,_readOnly,_readWrite,_empty,_checked,_enabled,_expanded,_highlighted,_before,_after,_firstLetter,_firstLine,_marker,_selection,_file,_backdrop,_first,_last,_only,_even,_odd,_firstOfType,_lastOfType,_onlyOfType,_peerFocus,_peerHover,_peerActive,_peerFocusWithin,_peerFocusVisible,_peerDisabled,_peerChecked,_peerInvalid,_peerExpanded,_peerPlaceholderShown,_groupFocus,_groupHover,_groupActive,_groupFocusWithin,_groupFocusVisible,_groupDisabled,_groupChecked,_groupExpanded,_groupInvalid,_indeterminate,_required,_valid,_invalid,_autofill,_inRange,_outOfRange,_placeholder,_placeholderShown,_pressed,_selected,_default,_optional,_open,_closed,_fullscreen,_loading,_currentPage,_currentStep,_motionReduce,_motionSafe,_print,_landscape,_portrait,_osDark,_osLight,_highContrast,_lessContrast,_moreContrast,_ltr,_rtl,_scrollbar,_scrollbarThumb,_scrollbarTrack,_horizontal,_vertical,_starting,sm,smOnly,smDown,md,mdOnly,mdDown,lg,lgOnly,lgDown,xl,xlOnly,xlDown,2xl,2xlOnly,2xlDown,smToMd,smToLg,smToXl,smTo2xl,mdToLg,mdToXl,mdTo2xl,lgToXl,lgTo2xl,xlTo2xl,@/xs,@/sm,@/md,@/lg,@/xl,@/2xl,@/3xl,@/4xl,@/5xl,@/6xl,@/7xl,@/8xl,base",hn=new Set(Po.split(","));function br(r){return hn.has(r)||/^@|&|&$/.test(r)}const Oo=/^_/,Lo=/&|@/;function Io(r){return r.map(e=>hn.has(e)?e.replace(Oo,""):Lo.test(e)?`[${an(e.trim())}]`:e)}function No(r){return r.sort((e,t)=>{const n=br(e),s=br(t);return n&&!s?1:!n&&s?-1:0})}const Mo="aspectRatio:aspect,boxDecorationBreak:decoration,zIndex:z,boxSizing:box,objectPosition:obj-pos,objectFit:obj-fit,overscrollBehavior:overscroll,overscrollBehaviorX:overscroll-x,overscrollBehaviorY:overscroll-y,position:pos/1,top:top,left:left,insetInline:inset-x/insetX,insetBlock:inset-y/insetY,inset:inset,insetBlockEnd:inset-b,insetBlockStart:inset-t,insetInlineEnd:end/insetEnd/1,insetInlineStart:start/insetStart/1,right:right,bottom:bottom,float:float,visibility:vis,display:d,hideFrom:hide,hideBelow:show,flexBasis:basis,flex:flex,flexDirection:flex/flexDir,flexGrow:grow,flexShrink:shrink,gridTemplateColumns:grid-cols,gridTemplateRows:grid-rows,gridColumn:col-span,gridRow:row-span,gridColumnStart:col-start,gridColumnEnd:col-end,gridAutoFlow:grid-flow,gridAutoColumns:auto-cols,gridAutoRows:auto-rows,gap:gap,gridGap:gap,gridRowGap:gap-x,gridColumnGap:gap-y,rowGap:gap-x,columnGap:gap-y,justifyContent:justify,alignContent:content,alignItems:items,alignSelf:self,padding:p/1,paddingLeft:pl/1,paddingRight:pr/1,paddingTop:pt/1,paddingBottom:pb/1,paddingBlock:py/1/paddingY,paddingBlockEnd:pb,paddingBlockStart:pt,paddingInline:px/paddingX/1,paddingInlineEnd:pe/1/paddingEnd,paddingInlineStart:ps/1/paddingStart,marginLeft:ml/1,marginRight:mr/1,marginTop:mt/1,marginBottom:mb/1,margin:m/1,marginBlock:my/1/marginY,marginBlockEnd:mb,marginBlockStart:mt,marginInline:mx/1/marginX,marginInlineEnd:me/1/marginEnd,marginInlineStart:ms/1/marginStart,spaceX:space-x,spaceY:space-y,outlineWidth:ring-width/ringWidth,outlineColor:ring-color/ringColor,outline:ring/1,outlineOffset:ring-offset/ringOffset,divideX:divide-x,divideY:divide-y,divideColor:divide-color,divideStyle:divide-style,width:w/1,inlineSize:w,minWidth:min-w/minW,minInlineSize:min-w,maxWidth:max-w/maxW,maxInlineSize:max-w,height:h/1,blockSize:h,minHeight:min-h/minH,minBlockSize:min-h,maxHeight:max-h/maxH,maxBlockSize:max-b,color:text,fontFamily:font,fontSize:fs,fontWeight:fw,fontSmoothing:smoothing,fontVariantNumeric:numeric,letterSpacing:tracking,lineHeight:leading,textAlign:text-align,textDecoration:text-decor,textDecorationColor:text-decor-color,textEmphasisColor:text-emphasis-color,textDecorationStyle:decoration-style,textDecorationThickness:decoration-thickness,textUnderlineOffset:underline-offset,textTransform:text-transform,textIndent:indent,textShadow:text-shadow,textShadowColor:text-shadow/textShadowColor,textOverflow:text-overflow,verticalAlign:v-align,wordBreak:break,textWrap:text-wrap,truncate:truncate,lineClamp:clamp,listStyleType:list-type,listStylePosition:list-pos,listStyleImage:list-img,backgroundPosition:bg-pos/bgPosition,backgroundPositionX:bg-pos-x/bgPositionX,backgroundPositionY:bg-pos-y/bgPositionY,backgroundAttachment:bg-attach/bgAttachment,backgroundClip:bg-clip/bgClip,background:bg/1,backgroundColor:bg/bgColor,backgroundOrigin:bg-origin/bgOrigin,backgroundImage:bg-img/bgImage,backgroundRepeat:bg-repeat/bgRepeat,backgroundBlendMode:bg-blend/bgBlendMode,backgroundSize:bg-size/bgSize,backgroundGradient:bg-gradient/bgGradient,textGradient:text-gradient,gradientFromPosition:gradient-from-pos,gradientToPosition:gradient-to-pos,gradientFrom:gradient-from,gradientTo:gradient-to,gradientVia:gradient-via,gradientViaPosition:gradient-via-pos,borderRadius:rounded/1,borderTopLeftRadius:rounded-tl/roundedTopLeft,borderTopRightRadius:rounded-tr/roundedTopRight,borderBottomRightRadius:rounded-br/roundedBottomRight,borderBottomLeftRadius:rounded-bl/roundedBottomLeft,borderTopRadius:rounded-t/roundedTop,borderRightRadius:rounded-r/roundedRight,borderBottomRadius:rounded-b/roundedBottom,borderLeftRadius:rounded-l/roundedLeft,borderStartStartRadius:rounded-ss/roundedStartStart,borderStartEndRadius:rounded-se/roundedStartEnd,borderStartRadius:rounded-s/roundedStart,borderEndStartRadius:rounded-es/roundedEndStart,borderEndEndRadius:rounded-ee/roundedEndEnd,borderEndRadius:rounded-e/roundedEnd,border:border,borderWidth:border-w,borderTopWidth:border-tw,borderLeftWidth:border-lw,borderRightWidth:border-rw,borderBottomWidth:border-bw,borderColor:border,borderInline:border-x/borderX,borderInlineWidth:border-x/borderXWidth,borderInlineColor:border-x/borderXColor,borderBlock:border-y/borderY,borderBlockWidth:border-y/borderYWidth,borderBlockColor:border-y/borderYColor,borderLeft:border-l,borderLeftColor:border-l,borderInlineStart:border-s/borderStart,borderInlineStartWidth:border-s/borderStartWidth,borderInlineStartColor:border-s/borderStartColor,borderRight:border-r,borderRightColor:border-r,borderInlineEnd:border-e/borderEnd,borderInlineEndWidth:border-e/borderEndWidth,borderInlineEndColor:border-e/borderEndColor,borderTop:border-t,borderTopColor:border-t,borderBottom:border-b,borderBottomColor:border-b,borderBlockEnd:border-be,borderBlockEndColor:border-be,borderBlockStart:border-bs,borderBlockStartColor:border-bs,boxShadow:shadow/1,boxShadowColor:shadow-color/shadowColor,mixBlendMode:mix-blend,filter:filter,brightness:brightness,contrast:contrast,grayscale:grayscale,hueRotate:hue-rotate,invert:invert,saturate:saturate,sepia:sepia,dropShadow:drop-shadow,blur:blur,backdropFilter:backdrop,backdropBlur:backdrop-blur,backdropBrightness:backdrop-brightness,backdropContrast:backdrop-contrast,backdropGrayscale:backdrop-grayscale,backdropHueRotate:backdrop-hue-rotate,backdropInvert:backdrop-invert,backdropOpacity:backdrop-opacity,backdropSaturate:backdrop-saturate,backdropSepia:backdrop-sepia,borderCollapse:border,borderSpacing:border-spacing,borderSpacingX:border-spacing-x,borderSpacingY:border-spacing-y,tableLayout:table,transitionTimingFunction:ease,transitionDelay:delay,transitionDuration:duration,transitionProperty:transition-prop,transition:transition,animation:animation,animationName:animation-name,animationTimingFunction:animation-ease,animationDuration:animation-duration,animationDelay:animation-delay,transformOrigin:origin,rotate:rotate,rotateX:rotate-x,rotateY:rotate-y,rotateZ:rotate-z,scale:scale,scaleX:scale-x,scaleY:scale-y,translate:translate,translateX:translate-x/x,translateY:translate-y/y,translateZ:translate-z/z,accentColor:accent,caretColor:caret,scrollBehavior:scroll,scrollbar:scrollbar,scrollMargin:scroll-m,scrollMarginLeft:scroll-ml,scrollMarginRight:scroll-mr,scrollMarginTop:scroll-mt,scrollMarginBottom:scroll-mb,scrollMarginBlock:scroll-my/scrollMarginY,scrollMarginBlockEnd:scroll-mb,scrollMarginBlockStart:scroll-mt,scrollMarginInline:scroll-mx/scrollMarginX,scrollMarginInlineEnd:scroll-me,scrollMarginInlineStart:scroll-ms,scrollPadding:scroll-p,scrollPaddingBlock:scroll-pb/scrollPaddingY,scrollPaddingBlockStart:scroll-pt,scrollPaddingBlockEnd:scroll-pb,scrollPaddingInline:scroll-px/scrollPaddingX,scrollPaddingInlineEnd:scroll-pe,scrollPaddingInlineStart:scroll-ps,scrollPaddingLeft:scroll-pl,scrollPaddingRight:scroll-pr,scrollPaddingTop:scroll-pt,scrollPaddingBottom:scroll-pb,scrollSnapAlign:snap-align,scrollSnapStop:snap-stop,scrollSnapType:snap-type,scrollSnapStrictness:snap-strictness,scrollSnapMargin:snap-m,scrollSnapMarginTop:snap-mt,scrollSnapMarginBottom:snap-mb,scrollSnapMarginLeft:snap-ml,scrollSnapMarginRight:snap-mr,touchAction:touch,userSelect:select,fill:fill,stroke:stroke,strokeWidth:stroke-w,srOnly:sr,debug:debug,appearance:appearance,backfaceVisibility:backface,clipPath:clip-path,hyphens:hyphens,mask:mask,maskImage:mask-image,maskSize:mask-size,textSizeAdjust:text-adjust,container:cq,containerName:cq-name,containerType:cq-type,textStyle:textStyle",dn=new Map,fn=new Map;Mo.split(",").forEach(r=>{const[e,t]=r.split(":"),[n,...s]=t.split("/");dn.set(e,n),s.length&&s.forEach(i=>{fn.set(i==="1"?n:i,e)})});const wr=r=>fn.get(r)||r,gn={conditions:{shift:No,finalize:Io,breakpoints:{keys:["base","sm","md","lg","xl","2xl"]}},utility:{transform:(r,e)=>{const t=wr(r);return{className:`${dn.get(t)||Ro(t)}_${an(e)}`}},hasShorthand:!0,toHash:(r,e)=>e(r.join(":")),resolveShorthand:wr}},Bo=_o(gn),X=(...r)=>Bo(pn(...r));X.raw=(...r)=>pn(...r);const{mergeCss:pn}=Ao(gn);var Do=K("<div><main>"),$o=K("<div><h1>管理员面板</h1><p>管理员功能正在开发中..."),Uo=K("<div><div>🔍</div><h1>404 - 页面未找到</h1><p>您访问的页面不存在</p><a href=/>返回首页");const Fo=pe(()=>me(()=>import("./Dashboard-BhOBfWI_.js"),[])),Wo=pe(()=>me(()=>import("./StrategyEditor-DnZTH3yZ.js").then(r=>r.S),__vite__mapDeps([0,1]))),qo=pe(()=>me(()=>import("./EnhancedStrategyEditor--JCchGM1.js"),[])),Vo=pe(()=>me(()=>import("./BacktestAnalysis-BqYcURrp.js"),[])),zo=pe(()=>me(()=>import("./MarketData-Bw9jWHsL.js"),[])),Ho=pe(()=>me(()=>import("./Login-yh0bC3lq.js"),[])),jo=pe(()=>me(()=>import("./Unauthorized-C1vJVksH.js"),[]));function Ko(r){return Ae(async()=>{console.log("应用开始初始化...");try{await Qe.initialize(),await Ji.initialize(),console.log("应用初始化完成")}catch(e){console.error("应用初始化失败:",e)}}),b(qs,{get children(){return b(Fs,{defaultLanguage:"zh-CN",get children(){return b(Ns,{get children(){var e=Do(),t=e.firstChild;return H(e,b(ro,{}),t),H(t,b(Bn,{get fallback(){return b(co,{})},get children(){return r.children}})),H(e,b(so,{}),null),M(n=>{var s=X({minHeight:"100vh",display:"flex",flexDirection:"column",backgroundColor:"gray.50",color:"gray.900",transition:"all 0.2s ease"}),i=X({flex:1,maxWidth:"1400px",margin:"0 auto",padding:"24px 16px",width:"100%"});return s!==n.e&&(e.className=n.e=s),i!==n.t&&(t.className=n.t=i),n},{e:void 0,t:void 0}),e}})}})}})}function Yo(){return b(Ls,{root:Ko,get children(){return[b(re,{path:"/",component:Fo}),b(re,{path:"/login",component:Ho}),b(re,{path:"/unauthorized",component:jo}),b(re,{path:"/market",component:()=>b(Ve,{get children(){return b(zo,{})}})}),b(re,{path:"/strategy",component:()=>b(Ve,{get children(){return b(Wo,{})}})}),b(re,{path:"/strategy-enhanced",component:()=>b(Ve,{get children(){return b(qo,{})}})}),b(re,{path:"/backtest",component:()=>b(Ve,{get children(){return b(Vo,{})}})}),b(re,{path:"/admin/*",component:()=>b(lo,{get children(){var r=$o(),e=r.firstChild,t=e.nextSibling;return M(n=>{var s=X({textAlign:"center",padding:"80px 0"}),i=X({fontSize:"2xl",fontWeight:"bold",marginBottom:"16px"}),o=X({color:"gray.600"});return s!==n.e&&(r.className=n.e=s),i!==n.t&&(e.className=n.t=i),o!==n.a&&(t.className=n.a=o),n},{e:void 0,t:void 0,a:void 0}),r}})}),b(re,{path:"*",component:()=>(()=>{var r=Uo(),e=r.firstChild,t=e.nextSibling,n=t.nextSibling,s=n.nextSibling;return M(i=>{var o=X({textAlign:"center",padding:"80px 0"}),a=X({fontSize:"6xl",marginBottom:"20px"}),c=X({fontSize:"2xl",fontWeight:"bold",marginBottom:"16px",color:"gray.900"}),l=X({color:"gray.600",marginBottom:"24px"}),u=X({display:"inline-block",padding:"12px 24px",backgroundColor:"blue.600",color:"white",textDecoration:"none",borderRadius:"8px",fontWeight:"500",transition:"all 0.2s",_hover:{backgroundColor:"blue.700"}});return o!==i.e&&(r.className=i.e=o),a!==i.t&&(e.className=i.t=a),c!==i.a&&(t.className=i.a=c),l!==i.o&&(n.className=i.o=l),u!==i.i&&(s.className=i.i=u),i},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),r})()})]}})}const mn=document.getElementById("root");if(!mn)throw new Error("Root element not found");Hn(()=>b(Yo,{}),mn);export{gt as A,Qo as F,st as S,me as _,b as a,M as b,D as c,Ae as d,Ne as e,Le as f,Qn as g,Ut as h,H as i,It as j,Ks as k,Pr as l,Cr as m,Ms as n,_n as o,ne as p,le as q,ps as r,f as s,K as t,he as u};
