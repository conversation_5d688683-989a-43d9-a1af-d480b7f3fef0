{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/mysql/mysql.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/mysql/mysql.ts\nvar conf = {\n  comments: {\n    lineComment: \"--\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".sql\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \"ACCESSIBLE\",\n    \"ADD\",\n    \"ALL\",\n    \"ALTER\",\n    \"ANALYZE\",\n    \"AND\",\n    \"AS\",\n    \"ASC\",\n    \"ASENSITIVE\",\n    \"BEFORE\",\n    \"BETWEEN\",\n    \"BIGINT\",\n    \"BINARY\",\n    \"BLOB\",\n    \"BOTH\",\n    \"BY\",\n    \"CALL\",\n    \"CASCADE\",\n    \"CASE\",\n    \"CHANGE\",\n    \"CHAR\",\n    \"CHARACTER\",\n    \"CHECK\",\n    \"COLLATE\",\n    \"COLUMN\",\n    \"CONDITION\",\n    \"CONSTRAINT\",\n    \"CONTINUE\",\n    \"CONVERT\",\n    \"CREATE\",\n    \"CROSS\",\n    \"CUBE\",\n    \"CUME_DIST\",\n    \"CURRENT_DATE\",\n    \"CURRENT_TIME\",\n    \"CURRENT_TIMESTAMP\",\n    \"CURRENT_USER\",\n    \"CURSOR\",\n    \"DATABASE\",\n    \"DATABASES\",\n    \"DAY_HOUR\",\n    \"DAY_MICROSECOND\",\n    \"DAY_MINUTE\",\n    \"DAY_SECOND\",\n    \"DEC\",\n    \"DECIMAL\",\n    \"DECLARE\",\n    \"DEFAULT\",\n    \"DELAYED\",\n    \"DELETE\",\n    \"DENSE_RANK\",\n    \"DESC\",\n    \"DESCRIBE\",\n    \"DETERMINISTIC\",\n    \"DISTINCT\",\n    \"DISTINCTROW\",\n    \"DIV\",\n    \"DOUBLE\",\n    \"DROP\",\n    \"DUAL\",\n    \"EACH\",\n    \"ELSE\",\n    \"ELSEIF\",\n    \"EMPTY\",\n    \"ENCLOSED\",\n    \"ESCAPED\",\n    \"EXCEPT\",\n    \"EXISTS\",\n    \"EXIT\",\n    \"EXPLAIN\",\n    \"FALSE\",\n    \"FETCH\",\n    \"FIRST_VALUE\",\n    \"FLOAT\",\n    \"FLOAT4\",\n    \"FLOAT8\",\n    \"FOR\",\n    \"FORCE\",\n    \"FOREIGN\",\n    \"FROM\",\n    \"FULLTEXT\",\n    \"FUNCTION\",\n    \"GENERATED\",\n    \"GET\",\n    \"GRANT\",\n    \"GROUP\",\n    \"GROUPING\",\n    \"GROUPS\",\n    \"HAVING\",\n    \"HIGH_PRIORITY\",\n    \"HOUR_MICROSECOND\",\n    \"HOUR_MINUTE\",\n    \"HOUR_SECOND\",\n    \"IF\",\n    \"IGNORE\",\n    \"IN\",\n    \"INDEX\",\n    \"INFILE\",\n    \"INNER\",\n    \"INOUT\",\n    \"INSENSITIVE\",\n    \"INSERT\",\n    \"INT\",\n    \"INT1\",\n    \"INT2\",\n    \"INT3\",\n    \"INT4\",\n    \"INT8\",\n    \"INTEGER\",\n    \"INTERVAL\",\n    \"INTO\",\n    \"IO_AFTER_GTIDS\",\n    \"IO_BEFORE_GTIDS\",\n    \"IS\",\n    \"ITERATE\",\n    \"JOIN\",\n    \"JSON_TABLE\",\n    \"KEY\",\n    \"KEYS\",\n    \"KILL\",\n    \"LAG\",\n    \"LAST_VALUE\",\n    \"LATERAL\",\n    \"LEAD\",\n    \"LEADING\",\n    \"LEAVE\",\n    \"LEFT\",\n    \"LIKE\",\n    \"LIMIT\",\n    \"LINEAR\",\n    \"LINES\",\n    \"LOAD\",\n    \"LOCALTIME\",\n    \"LOCALTIMESTAMP\",\n    \"LOCK\",\n    \"LONG\",\n    \"LONGBLOB\",\n    \"LONGTEXT\",\n    \"LOOP\",\n    \"LOW_PRIORITY\",\n    \"MASTER_BIND\",\n    \"MASTER_SSL_VERIFY_SERVER_CERT\",\n    \"MATCH\",\n    \"MAXVALUE\",\n    \"MEDIUMBLOB\",\n    \"MEDIUMINT\",\n    \"MEDIUMTEXT\",\n    \"MIDDLEINT\",\n    \"MINUTE_MICROSECOND\",\n    \"MINUTE_SECOND\",\n    \"MOD\",\n    \"MODIFIES\",\n    \"NATURAL\",\n    \"NOT\",\n    \"NO_WRITE_TO_BINLOG\",\n    \"NTH_VALUE\",\n    \"NTILE\",\n    \"NULL\",\n    \"NUMERIC\",\n    \"OF\",\n    \"ON\",\n    \"OPTIMIZE\",\n    \"OPTIMIZER_COSTS\",\n    \"OPTION\",\n    \"OPTIONALLY\",\n    \"OR\",\n    \"ORDER\",\n    \"OUT\",\n    \"OUTER\",\n    \"OUTFILE\",\n    \"OVER\",\n    \"PARTITION\",\n    \"PERCENT_RANK\",\n    \"PRECISION\",\n    \"PRIMARY\",\n    \"PROCEDURE\",\n    \"PURGE\",\n    \"RANGE\",\n    \"RANK\",\n    \"READ\",\n    \"READS\",\n    \"READ_WRITE\",\n    \"REAL\",\n    \"RECURSIVE\",\n    \"REFERENCES\",\n    \"REGEXP\",\n    \"RELEASE\",\n    \"RENAME\",\n    \"REPEAT\",\n    \"REPLACE\",\n    \"REQUIRE\",\n    \"RESIGNAL\",\n    \"RESTRICT\",\n    \"RETURN\",\n    \"REVOKE\",\n    \"RIGHT\",\n    \"RLIKE\",\n    \"ROW\",\n    \"ROWS\",\n    \"ROW_NUMBER\",\n    \"SCHEMA\",\n    \"SCHEMAS\",\n    \"SECOND_MICROSECOND\",\n    \"SELECT\",\n    \"SENSITIVE\",\n    \"SEPARATOR\",\n    \"SET\",\n    \"SHOW\",\n    \"SIGNAL\",\n    \"SMALLINT\",\n    \"SPATIAL\",\n    \"SPECIFIC\",\n    \"SQL\",\n    \"SQLEXCEPTION\",\n    \"SQLSTATE\",\n    \"SQLWARNING\",\n    \"SQL_BIG_RESULT\",\n    \"SQL_CALC_FOUND_ROWS\",\n    \"SQL_SMALL_RESULT\",\n    \"SSL\",\n    \"STARTING\",\n    \"STORED\",\n    \"STRAIGHT_JOIN\",\n    \"SYSTEM\",\n    \"TABLE\",\n    \"TERMINATED\",\n    \"THEN\",\n    \"TINYBLOB\",\n    \"TINYINT\",\n    \"TINYTEXT\",\n    \"TO\",\n    \"TRAILING\",\n    \"TRIGGER\",\n    \"TRUE\",\n    \"UNDO\",\n    \"UNION\",\n    \"UNIQUE\",\n    \"UNLOCK\",\n    \"UNSIGNED\",\n    \"UPDATE\",\n    \"USAGE\",\n    \"USE\",\n    \"USING\",\n    \"UTC_DATE\",\n    \"UTC_TIME\",\n    \"UTC_TIMESTAMP\",\n    \"VALUES\",\n    \"VARBINARY\",\n    \"VARCHAR\",\n    \"VARCHARACTER\",\n    \"VARYING\",\n    \"VIRTUAL\",\n    \"WHEN\",\n    \"WHERE\",\n    \"WHILE\",\n    \"WINDOW\",\n    \"WITH\",\n    \"WRITE\",\n    \"XOR\",\n    \"YEAR_MONTH\",\n    \"ZEROFILL\"\n  ],\n  operators: [\n    \"AND\",\n    \"BETWEEN\",\n    \"IN\",\n    \"LIKE\",\n    \"NOT\",\n    \"OR\",\n    \"IS\",\n    \"NULL\",\n    \"INTERSECT\",\n    \"UNION\",\n    \"INNER\",\n    \"JOIN\",\n    \"LEFT\",\n    \"OUTER\",\n    \"RIGHT\"\n  ],\n  builtinFunctions: [\n    \"ABS\",\n    \"ACOS\",\n    \"ADDDATE\",\n    \"ADDTIME\",\n    \"AES_DECRYPT\",\n    \"AES_ENCRYPT\",\n    \"ANY_VALUE\",\n    \"Area\",\n    \"AsBinary\",\n    \"AsWKB\",\n    \"ASCII\",\n    \"ASIN\",\n    \"AsText\",\n    \"AsWKT\",\n    \"ASYMMETRIC_DECRYPT\",\n    \"ASYMMETRIC_DERIVE\",\n    \"ASYMMETRIC_ENCRYPT\",\n    \"ASYMMETRIC_SIGN\",\n    \"ASYMMETRIC_VERIFY\",\n    \"ATAN\",\n    \"ATAN2\",\n    \"ATAN\",\n    \"AVG\",\n    \"BENCHMARK\",\n    \"BIN\",\n    \"BIT_AND\",\n    \"BIT_COUNT\",\n    \"BIT_LENGTH\",\n    \"BIT_OR\",\n    \"BIT_XOR\",\n    \"Buffer\",\n    \"CAST\",\n    \"CEIL\",\n    \"CEILING\",\n    \"Centroid\",\n    \"CHAR\",\n    \"CHAR_LENGTH\",\n    \"CHARACTER_LENGTH\",\n    \"CHARSET\",\n    \"COALESCE\",\n    \"COERCIBILITY\",\n    \"COLLATION\",\n    \"COMPRESS\",\n    \"CONCAT\",\n    \"CONCAT_WS\",\n    \"CONNECTION_ID\",\n    \"Contains\",\n    \"CONV\",\n    \"CONVERT\",\n    \"CONVERT_TZ\",\n    \"ConvexHull\",\n    \"COS\",\n    \"COT\",\n    \"COUNT\",\n    \"CRC32\",\n    \"CREATE_ASYMMETRIC_PRIV_KEY\",\n    \"CREATE_ASYMMETRIC_PUB_KEY\",\n    \"CREATE_DH_PARAMETERS\",\n    \"CREATE_DIGEST\",\n    \"Crosses\",\n    \"CUME_DIST\",\n    \"CURDATE\",\n    \"CURRENT_DATE\",\n    \"CURRENT_ROLE\",\n    \"CURRENT_TIME\",\n    \"CURRENT_TIMESTAMP\",\n    \"CURRENT_USER\",\n    \"CURTIME\",\n    \"DATABASE\",\n    \"DATE\",\n    \"DATE_ADD\",\n    \"DATE_FORMAT\",\n    \"DATE_SUB\",\n    \"DATEDIFF\",\n    \"DAY\",\n    \"DAYNAME\",\n    \"DAYOFMONTH\",\n    \"DAYOFWEEK\",\n    \"DAYOFYEAR\",\n    \"DECODE\",\n    \"DEFAULT\",\n    \"DEGREES\",\n    \"DES_DECRYPT\",\n    \"DES_ENCRYPT\",\n    \"DENSE_RANK\",\n    \"Dimension\",\n    \"Disjoint\",\n    \"Distance\",\n    \"ELT\",\n    \"ENCODE\",\n    \"ENCRYPT\",\n    \"EndPoint\",\n    \"Envelope\",\n    \"Equals\",\n    \"EXP\",\n    \"EXPORT_SET\",\n    \"ExteriorRing\",\n    \"EXTRACT\",\n    \"ExtractValue\",\n    \"FIELD\",\n    \"FIND_IN_SET\",\n    \"FIRST_VALUE\",\n    \"FLOOR\",\n    \"FORMAT\",\n    \"FORMAT_BYTES\",\n    \"FORMAT_PICO_TIME\",\n    \"FOUND_ROWS\",\n    \"FROM_BASE64\",\n    \"FROM_DAYS\",\n    \"FROM_UNIXTIME\",\n    \"GEN_RANGE\",\n    \"GEN_RND_EMAIL\",\n    \"GEN_RND_PAN\",\n    \"GEN_RND_SSN\",\n    \"GEN_RND_US_PHONE\",\n    \"GeomCollection\",\n    \"GeomCollFromText\",\n    \"GeometryCollectionFromText\",\n    \"GeomCollFromWKB\",\n    \"GeometryCollectionFromWKB\",\n    \"GeometryCollection\",\n    \"GeometryN\",\n    \"GeometryType\",\n    \"GeomFromText\",\n    \"GeometryFromText\",\n    \"GeomFromWKB\",\n    \"GeometryFromWKB\",\n    \"GET_FORMAT\",\n    \"GET_LOCK\",\n    \"GLength\",\n    \"GREATEST\",\n    \"GROUP_CONCAT\",\n    \"GROUPING\",\n    \"GTID_SUBSET\",\n    \"GTID_SUBTRACT\",\n    \"HEX\",\n    \"HOUR\",\n    \"ICU_VERSION\",\n    \"IF\",\n    \"IFNULL\",\n    \"INET_ATON\",\n    \"INET_NTOA\",\n    \"INET6_ATON\",\n    \"INET6_NTOA\",\n    \"INSERT\",\n    \"INSTR\",\n    \"InteriorRingN\",\n    \"Intersects\",\n    \"INTERVAL\",\n    \"IS_FREE_LOCK\",\n    \"IS_IPV4\",\n    \"IS_IPV4_COMPAT\",\n    \"IS_IPV4_MAPPED\",\n    \"IS_IPV6\",\n    \"IS_USED_LOCK\",\n    \"IS_UUID\",\n    \"IsClosed\",\n    \"IsEmpty\",\n    \"ISNULL\",\n    \"IsSimple\",\n    \"JSON_APPEND\",\n    \"JSON_ARRAY\",\n    \"JSON_ARRAY_APPEND\",\n    \"JSON_ARRAY_INSERT\",\n    \"JSON_ARRAYAGG\",\n    \"JSON_CONTAINS\",\n    \"JSON_CONTAINS_PATH\",\n    \"JSON_DEPTH\",\n    \"JSON_EXTRACT\",\n    \"JSON_INSERT\",\n    \"JSON_KEYS\",\n    \"JSON_LENGTH\",\n    \"JSON_MERGE\",\n    \"JSON_MERGE_PATCH\",\n    \"JSON_MERGE_PRESERVE\",\n    \"JSON_OBJECT\",\n    \"JSON_OBJECTAGG\",\n    \"JSON_OVERLAPS\",\n    \"JSON_PRETTY\",\n    \"JSON_QUOTE\",\n    \"JSON_REMOVE\",\n    \"JSON_REPLACE\",\n    \"JSON_SCHEMA_VALID\",\n    \"JSON_SCHEMA_VALIDATION_REPORT\",\n    \"JSON_SEARCH\",\n    \"JSON_SET\",\n    \"JSON_STORAGE_FREE\",\n    \"JSON_STORAGE_SIZE\",\n    \"JSON_TABLE\",\n    \"JSON_TYPE\",\n    \"JSON_UNQUOTE\",\n    \"JSON_VALID\",\n    \"LAG\",\n    \"LAST_DAY\",\n    \"LAST_INSERT_ID\",\n    \"LAST_VALUE\",\n    \"LCASE\",\n    \"LEAD\",\n    \"LEAST\",\n    \"LEFT\",\n    \"LENGTH\",\n    \"LineFromText\",\n    \"LineStringFromText\",\n    \"LineFromWKB\",\n    \"LineStringFromWKB\",\n    \"LineString\",\n    \"LN\",\n    \"LOAD_FILE\",\n    \"LOCALTIME\",\n    \"LOCALTIMESTAMP\",\n    \"LOCATE\",\n    \"LOG\",\n    \"LOG10\",\n    \"LOG2\",\n    \"LOWER\",\n    \"LPAD\",\n    \"LTRIM\",\n    \"MAKE_SET\",\n    \"MAKEDATE\",\n    \"MAKETIME\",\n    \"MASK_INNER\",\n    \"MASK_OUTER\",\n    \"MASK_PAN\",\n    \"MASK_PAN_RELAXED\",\n    \"MASK_SSN\",\n    \"MASTER_POS_WAIT\",\n    \"MAX\",\n    \"MBRContains\",\n    \"MBRCoveredBy\",\n    \"MBRCovers\",\n    \"MBRDisjoint\",\n    \"MBREqual\",\n    \"MBREquals\",\n    \"MBRIntersects\",\n    \"MBROverlaps\",\n    \"MBRTouches\",\n    \"MBRWithin\",\n    \"MD5\",\n    \"MEMBER OF\",\n    \"MICROSECOND\",\n    \"MID\",\n    \"MIN\",\n    \"MINUTE\",\n    \"MLineFromText\",\n    \"MultiLineStringFromText\",\n    \"MLineFromWKB\",\n    \"MultiLineStringFromWKB\",\n    \"MOD\",\n    \"MONTH\",\n    \"MONTHNAME\",\n    \"MPointFromText\",\n    \"MultiPointFromText\",\n    \"MPointFromWKB\",\n    \"MultiPointFromWKB\",\n    \"MPolyFromText\",\n    \"MultiPolygonFromText\",\n    \"MPolyFromWKB\",\n    \"MultiPolygonFromWKB\",\n    \"MultiLineString\",\n    \"MultiPoint\",\n    \"MultiPolygon\",\n    \"NAME_CONST\",\n    \"NOT IN\",\n    \"NOW\",\n    \"NTH_VALUE\",\n    \"NTILE\",\n    \"NULLIF\",\n    \"NumGeometries\",\n    \"NumInteriorRings\",\n    \"NumPoints\",\n    \"OCT\",\n    \"OCTET_LENGTH\",\n    \"OLD_PASSWORD\",\n    \"ORD\",\n    \"Overlaps\",\n    \"PASSWORD\",\n    \"PERCENT_RANK\",\n    \"PERIOD_ADD\",\n    \"PERIOD_DIFF\",\n    \"PI\",\n    \"Point\",\n    \"PointFromText\",\n    \"PointFromWKB\",\n    \"PointN\",\n    \"PolyFromText\",\n    \"PolygonFromText\",\n    \"PolyFromWKB\",\n    \"PolygonFromWKB\",\n    \"Polygon\",\n    \"POSITION\",\n    \"POW\",\n    \"POWER\",\n    \"PS_CURRENT_THREAD_ID\",\n    \"PS_THREAD_ID\",\n    \"PROCEDURE ANALYSE\",\n    \"QUARTER\",\n    \"QUOTE\",\n    \"RADIANS\",\n    \"RAND\",\n    \"RANDOM_BYTES\",\n    \"RANK\",\n    \"REGEXP_INSTR\",\n    \"REGEXP_LIKE\",\n    \"REGEXP_REPLACE\",\n    \"REGEXP_REPLACE\",\n    \"RELEASE_ALL_LOCKS\",\n    \"RELEASE_LOCK\",\n    \"REPEAT\",\n    \"REPLACE\",\n    \"REVERSE\",\n    \"RIGHT\",\n    \"ROLES_GRAPHML\",\n    \"ROUND\",\n    \"ROW_COUNT\",\n    \"ROW_NUMBER\",\n    \"RPAD\",\n    \"RTRIM\",\n    \"SCHEMA\",\n    \"SEC_TO_TIME\",\n    \"SECOND\",\n    \"SESSION_USER\",\n    \"SHA1\",\n    \"SHA\",\n    \"SHA2\",\n    \"SIGN\",\n    \"SIN\",\n    \"SLEEP\",\n    \"SOUNDEX\",\n    \"SOURCE_POS_WAIT\",\n    \"SPACE\",\n    \"SQRT\",\n    \"SRID\",\n    \"ST_Area\",\n    \"ST_AsBinary\",\n    \"ST_AsWKB\",\n    \"ST_AsGeoJSON\",\n    \"ST_AsText\",\n    \"ST_AsWKT\",\n    \"ST_Buffer\",\n    \"ST_Buffer_Strategy\",\n    \"ST_Centroid\",\n    \"ST_Collect\",\n    \"ST_Contains\",\n    \"ST_ConvexHull\",\n    \"ST_Crosses\",\n    \"ST_Difference\",\n    \"ST_Dimension\",\n    \"ST_Disjoint\",\n    \"ST_Distance\",\n    \"ST_Distance_Sphere\",\n    \"ST_EndPoint\",\n    \"ST_Envelope\",\n    \"ST_Equals\",\n    \"ST_ExteriorRing\",\n    \"ST_FrechetDistance\",\n    \"ST_GeoHash\",\n    \"ST_GeomCollFromText\",\n    \"ST_GeometryCollectionFromText\",\n    \"ST_GeomCollFromTxt\",\n    \"ST_GeomCollFromWKB\",\n    \"ST_GeometryCollectionFromWKB\",\n    \"ST_GeometryN\",\n    \"ST_GeometryType\",\n    \"ST_GeomFromGeoJSON\",\n    \"ST_GeomFromText\",\n    \"ST_GeometryFromText\",\n    \"ST_GeomFromWKB\",\n    \"ST_GeometryFromWKB\",\n    \"ST_HausdorffDistance\",\n    \"ST_InteriorRingN\",\n    \"ST_Intersection\",\n    \"ST_Intersects\",\n    \"ST_IsClosed\",\n    \"ST_IsEmpty\",\n    \"ST_IsSimple\",\n    \"ST_IsValid\",\n    \"ST_LatFromGeoHash\",\n    \"ST_Length\",\n    \"ST_LineFromText\",\n    \"ST_LineStringFromText\",\n    \"ST_LineFromWKB\",\n    \"ST_LineStringFromWKB\",\n    \"ST_LineInterpolatePoint\",\n    \"ST_LineInterpolatePoints\",\n    \"ST_LongFromGeoHash\",\n    \"ST_Longitude\",\n    \"ST_MakeEnvelope\",\n    \"ST_MLineFromText\",\n    \"ST_MultiLineStringFromText\",\n    \"ST_MLineFromWKB\",\n    \"ST_MultiLineStringFromWKB\",\n    \"ST_MPointFromText\",\n    \"ST_MultiPointFromText\",\n    \"ST_MPointFromWKB\",\n    \"ST_MultiPointFromWKB\",\n    \"ST_MPolyFromText\",\n    \"ST_MultiPolygonFromText\",\n    \"ST_MPolyFromWKB\",\n    \"ST_MultiPolygonFromWKB\",\n    \"ST_NumGeometries\",\n    \"ST_NumInteriorRing\",\n    \"ST_NumInteriorRings\",\n    \"ST_NumPoints\",\n    \"ST_Overlaps\",\n    \"ST_PointAtDistance\",\n    \"ST_PointFromGeoHash\",\n    \"ST_PointFromText\",\n    \"ST_PointFromWKB\",\n    \"ST_PointN\",\n    \"ST_PolyFromText\",\n    \"ST_PolygonFromText\",\n    \"ST_PolyFromWKB\",\n    \"ST_PolygonFromWKB\",\n    \"ST_Simplify\",\n    \"ST_SRID\",\n    \"ST_StartPoint\",\n    \"ST_SwapXY\",\n    \"ST_SymDifference\",\n    \"ST_Touches\",\n    \"ST_Transform\",\n    \"ST_Union\",\n    \"ST_Validate\",\n    \"ST_Within\",\n    \"ST_X\",\n    \"ST_Y\",\n    \"StartPoint\",\n    \"STATEMENT_DIGEST\",\n    \"STATEMENT_DIGEST_TEXT\",\n    \"STD\",\n    \"STDDEV\",\n    \"STDDEV_POP\",\n    \"STDDEV_SAMP\",\n    \"STR_TO_DATE\",\n    \"STRCMP\",\n    \"SUBDATE\",\n    \"SUBSTR\",\n    \"SUBSTRING\",\n    \"SUBSTRING_INDEX\",\n    \"SUBTIME\",\n    \"SUM\",\n    \"SYSDATE\",\n    \"SYSTEM_USER\",\n    \"TAN\",\n    \"TIME\",\n    \"TIME_FORMAT\",\n    \"TIME_TO_SEC\",\n    \"TIMEDIFF\",\n    \"TIMESTAMP\",\n    \"TIMESTAMPADD\",\n    \"TIMESTAMPDIFF\",\n    \"TO_BASE64\",\n    \"TO_DAYS\",\n    \"TO_SECONDS\",\n    \"Touches\",\n    \"TRIM\",\n    \"TRUNCATE\",\n    \"UCASE\",\n    \"UNCOMPRESS\",\n    \"UNCOMPRESSED_LENGTH\",\n    \"UNHEX\",\n    \"UNIX_TIMESTAMP\",\n    \"UpdateXML\",\n    \"UPPER\",\n    \"USER\",\n    \"UTC_DATE\",\n    \"UTC_TIME\",\n    \"UTC_TIMESTAMP\",\n    \"UUID\",\n    \"UUID_SHORT\",\n    \"UUID_TO_BIN\",\n    \"VALIDATE_PASSWORD_STRENGTH\",\n    \"VALUES\",\n    \"VAR_POP\",\n    \"VAR_SAMP\",\n    \"VARIANCE\",\n    \"VERSION\",\n    \"WAIT_FOR_EXECUTED_GTID_SET\",\n    \"WAIT_UNTIL_SQL_THREAD_AFTER_GTIDS\",\n    \"WEEK\",\n    \"WEEKDAY\",\n    \"WEEKOFYEAR\",\n    \"WEIGHT_STRING\",\n    \"Within\",\n    \"X\",\n    \"Y\",\n    \"YEAR\",\n    \"YEARWEEK\"\n  ],\n  builtinVariables: [],\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@complexIdentifiers\" },\n      { include: \"@scopes\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [\n        /[\\w@]+/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@builtinVariables\": \"predefined\",\n            \"@builtinFunctions\": \"predefined\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [/--+.*/, \"comment\"],\n      [/#+.*/, \"comment\"],\n      [/\\/\\*/, { token: \"comment.quote\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^*/]+/, \"comment\"],\n      [/\\*\\//, { token: \"comment.quote\", next: \"@pop\" }],\n      [/./, \"comment\"]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [\n      [/'/, { token: \"string\", next: \"@string\" }],\n      [/\"/, { token: \"string.double\", next: \"@stringDouble\" }]\n    ],\n    string: [\n      [/\\\\'/, \"string\"],\n      [/[^']+/, \"string\"],\n      [/''/, \"string\"],\n      [/'/, { token: \"string\", next: \"@pop\" }]\n    ],\n    stringDouble: [\n      [/[^\"]+/, \"string.double\"],\n      [/\"\"/, \"string.double\"],\n      [/\"/, { token: \"string.double\", next: \"@pop\" }]\n    ],\n    complexIdentifiers: [[/`/, { token: \"identifier.quote\", next: \"@quotedIdentifier\" }]],\n    quotedIdentifier: [\n      [/[^`]+/, \"identifier\"],\n      [/``/, \"identifier\"],\n      [/`/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    scopes: []\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,EAC1D;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB,CAAC;AAAA,EACnB,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,YAAY;AAAA,MACvB,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,sBAAsB;AAAA,MACjC,EAAE,SAAS,UAAU;AAAA,MACrB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,QAAQ,WAAW;AAAA,MACpB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,qBAAqB;AAAA,YACrB,qBAAqB;AAAA,YACrB,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,oBAAoB,UAAU;AAAA,IACjC;AAAA,IACA,YAAY,CAAC,CAAC,OAAO,OAAO,CAAC;AAAA,IAC7B,UAAU;AAAA,MACR,CAAC,SAAS,SAAS;AAAA,MACnB,CAAC,QAAQ,SAAS;AAAA,MAClB,CAAC,QAAQ,EAAE,OAAO,iBAAiB,MAAM,WAAW,CAAC;AAAA,IACvD;AAAA,IACA,SAAS;AAAA,MACP,CAAC,UAAU,SAAS;AAAA,MACpB,CAAC,QAAQ,EAAE,OAAO,iBAAiB,MAAM,OAAO,CAAC;AAAA,MACjD,CAAC,KAAK,SAAS;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,qBAAqB,QAAQ;AAAA,MAC9B,CAAC,uBAAuB,QAAQ;AAAA,MAChC,CAAC,2CAA2C,QAAQ;AAAA,IACtD;AAAA,IACA,SAAS;AAAA,MACP,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,UAAU,CAAC;AAAA,MAC1C,CAAC,KAAK,EAAE,OAAO,iBAAiB,MAAM,gBAAgB,CAAC;AAAA,IACzD;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,MAAM,QAAQ;AAAA,MACf,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,IACzC;AAAA,IACA,cAAc;AAAA,MACZ,CAAC,SAAS,eAAe;AAAA,MACzB,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,iBAAiB,MAAM,OAAO,CAAC;AAAA,IAChD;AAAA,IACA,oBAAoB,CAAC,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,oBAAoB,CAAC,CAAC;AAAA,IACpF,kBAAkB;AAAA,MAChB,CAAC,SAAS,YAAY;AAAA,MACtB,CAAC,MAAM,YAAY;AAAA,MACnB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,IACnD;AAAA,IACA,QAAQ,CAAC;AAAA,EACX;AACF;", "names": []}