/**
 * 回测功能路由
 */
import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { authenticateToken } from './auth.js';

const router = express.Router();

// 模拟回测数据存储
const backtests = new Map();
const backtestResults = new Map();

// 初始化一些示例回测数据
const sampleBacktests = [
  {
    id: 'bt_001',
    userId: 1,
    name: '双均线策略回测-平安银行',
    description: '使用5日和20日移动平均线的交叉策略',
    symbol: '000001',
    strategy: 'ma_cross',
    params: {
      shortPeriod: 5,
      longPeriod: 20,
      initialCapital: 1000000,
      startDate: '2024-01-01',
      endDate: '2024-06-30'
    },
    status: 'completed',
    progress: 100,
    createTime: '2024-01-15T09:00:00Z',
    startTime: '2024-01-15T09:00:05Z',
    endTime: '2024-01-15T09:02:30Z',
    duration: 145,
    results: {
      totalReturn: 0.082,
      annualizedReturn: 0.164,
      maxDrawdown: 0.035,
      sharpeRatio: 1.85,
      winRate: 0.64,
      profitLossRatio: 1.45,
      totalTrades: 156,
      finalValue: 1082000
    }
  },
  {
    id: 'bt_002',
    userId: 1,
    name: 'RSI逆向策略-招商银行',
    description: 'RSI超买超卖反转策略',
    symbol: '600036',
    strategy: 'rsi_reversal',
    params: {
      rsiPeriod: 14,
      overbought: 70,
      oversold: 30,
      initialCapital: 500000,
      startDate: '2024-01-01',
      endDate: '2024-06-30'
    },
    status: 'completed',
    progress: 100,
    createTime: '2024-01-20T14:00:00Z',
    startTime: '2024-01-20T14:00:10Z',
    endTime: '2024-01-20T14:03:45Z',
    duration: 215,
    results: {
      totalReturn: -0.025,
      annualizedReturn: -0.05,
      maxDrawdown: 0.068,
      sharpeRatio: -0.32,
      winRate: 0.45,
      profitLossRatio: 0.89,
      totalTrades: 89,
      finalValue: 487500
    }
  },
  {
    id: 'bt_003',
    userId: 1,
    name: 'MACD金叉死叉策略',
    description: 'MACD指标金叉买入死叉卖出',
    symbol: '600000',
    strategy: 'macd_cross',
    params: {
      fastPeriod: 12,
      slowPeriod: 26,
      signalPeriod: 9,
      initialCapital: 800000,
      startDate: '2024-01-01',
      endDate: '2024-06-30'
    },
    status: 'running',
    progress: 75,
    createTime: '2024-01-25T10:30:00Z',
    startTime: '2024-01-25T10:30:15Z',
    endTime: null,
    duration: null,
    results: null
  }
];

// 初始化示例数据
for (const backtest of sampleBacktests) {
  backtests.set(backtest.id, backtest);
}

export default function(dataLoader) {
  
  /**
   * 获取回测列表
   */
  router.get('/', authenticateToken, async (req, res) => {
    try {
      const userId = req.user.id;
      const { status, strategy, page = 1, limit = 20 } = req.query;
      
      let userBacktests = Array.from(backtests.values())
        .filter(bt => bt.userId === userId);
      
      // 状态筛选
      if (status) {
        userBacktests = userBacktests.filter(bt => bt.status === status);
      }
      
      // 策略筛选
      if (strategy) {
        userBacktests = userBacktests.filter(bt => bt.strategy === strategy);
      }
      
      // 排序（最新的在前）
      userBacktests.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
      
      // 分页
      const offset = (parseInt(page) - 1) * parseInt(limit);
      const paginatedBacktests = userBacktests.slice(offset, offset + parseInt(limit));
      
      res.json({
        success: true,
        data: {
          backtests: paginatedBacktests,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: userBacktests.length,
            pages: Math.ceil(userBacktests.length / parseInt(limit))
          }
        }
      });
      
    } catch (error) {
      console.error('获取回测列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取回测列表失败',
        code: 'BACKTEST_LIST_ERROR'
      });
    }
  });

  /**
   * 创建回测任务
   */
  router.post('/', authenticateToken, async (req, res) => {
    try {
      const userId = req.user.id;
      const { 
        name, 
        description, 
        symbol, 
        strategy, 
        params 
      } = req.body;
      
      // 验证必要参数
      if (!name || !symbol || !strategy || !params) {
        return res.status(400).json({
          success: false,
          message: '回测参数不完整',
          code: 'INVALID_BACKTEST_PARAMS'
        });
      }
      
      // 验证策略参数
      const validationResult = validateStrategyParams(strategy, params);
      if (!validationResult.valid) {
        return res.status(400).json({
          success: false,
          message: validationResult.message,
          code: 'INVALID_STRATEGY_PARAMS'
        });
      }
      
      // 创建回测任务
      const backtestId = `bt_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
      const backtest = {
        id: backtestId,
        userId,
        name,
        description: description || '',
        symbol,
        strategy,
        params,
        status: 'pending',
        progress: 0,
        createTime: new Date().toISOString(),
        startTime: null,
        endTime: null,
        duration: null,
        results: null
      };
      
      backtests.set(backtestId, backtest);
      
      // 异步执行回测
      setTimeout(() => {
        executeBacktest(backtestId, dataLoader);
      }, 1000);
      
      res.status(201).json({
        success: true,
        message: '回测任务创建成功',
        data: {
          backtest
        }
      });
      
    } catch (error) {
      console.error('创建回测错误:', error);
      res.status(500).json({
        success: false,
        message: '创建回测任务失败',
        code: 'CREATE_BACKTEST_ERROR'
      });
    }
  });

  /**
   * 获取回测详情
   */
  router.get('/:backtestId', authenticateToken, async (req, res) => {
    try {
      const { backtestId } = req.params;
      const userId = req.user.id;
      
      const backtest = backtests.get(backtestId);
      
      if (!backtest) {
        return res.status(404).json({
          success: false,
          message: '回测任务不存在',
          code: 'BACKTEST_NOT_FOUND'
        });
      }
      
      if (backtest.userId !== userId) {
        return res.status(403).json({
          success: false,
          message: '无权访问此回测任务',
          code: 'INSUFFICIENT_PERMISSION'
        });
      }
      
      res.json({
        success: true,
        data: {
          backtest
        }
      });
      
    } catch (error) {
      console.error('获取回测详情错误:', error);
      res.status(500).json({
        success: false,
        message: '获取回测详情失败',
        code: 'BACKTEST_DETAIL_ERROR'
      });
    }
  });

  /**
   * 获取回测结果
   */
  router.get('/:backtestId/results', authenticateToken, async (req, res) => {
    try {
      const { backtestId } = req.params;
      const userId = req.user.id;
      
      const backtest = backtests.get(backtestId);
      
      if (!backtest) {
        return res.status(404).json({
          success: false,
          message: '回测任务不存在',
          code: 'BACKTEST_NOT_FOUND'
        });
      }
      
      if (backtest.userId !== userId) {
        return res.status(403).json({
          success: false,
          message: '无权访问此回测任务',
          code: 'INSUFFICIENT_PERMISSION'
        });
      }
      
      if (backtest.status !== 'completed') {
        return res.status(400).json({
          success: false,
          message: '回测尚未完成',
          code: 'BACKTEST_NOT_COMPLETED'
        });
      }
      
      // 获取详细结果数据
      const detailedResults = backtestResults.get(backtestId) || generateDetailedResults(backtest);
      
      res.json({
        success: true,
        data: {
          backtest: {
            id: backtest.id,
            name: backtest.name,
            symbol: backtest.symbol,
            strategy: backtest.strategy,
            params: backtest.params,
            duration: backtest.duration
          },
          results: detailedResults
        }
      });
      
    } catch (error) {
      console.error('获取回测结果错误:', error);
      res.status(500).json({
        success: false,
        message: '获取回测结果失败',
        code: 'BACKTEST_RESULTS_ERROR'
      });
    }
  });

  /**
   * 删除回测任务
   */
  router.delete('/:backtestId', authenticateToken, async (req, res) => {
    try {
      const { backtestId } = req.params;
      const userId = req.user.id;
      
      const backtest = backtests.get(backtestId);
      
      if (!backtest) {
        return res.status(404).json({
          success: false,
          message: '回测任务不存在',
          code: 'BACKTEST_NOT_FOUND'
        });
      }
      
      if (backtest.userId !== userId) {
        return res.status(403).json({
          success: false,
          message: '无权删除此回测任务',
          code: 'INSUFFICIENT_PERMISSION'
        });
      }
      
      if (backtest.status === 'running') {
        return res.status(400).json({
          success: false,
          message: '不能删除正在运行的回测任务',
          code: 'CANNOT_DELETE_RUNNING'
        });
      }
      
      backtests.delete(backtestId);
      backtestResults.delete(backtestId);
      
      res.json({
        success: true,
        message: '回测任务删除成功'
      });
      
    } catch (error) {
      console.error('删除回测错误:', error);
      res.status(500).json({
        success: false,
        message: '删除回测任务失败',
        code: 'DELETE_BACKTEST_ERROR'
      });
    }
  });

  /**
   * 获取策略模板列表
   */
  router.get('/templates/strategies', async (req, res) => {
    try {
      const strategies = [
        {
          id: 'ma_cross',
          name: '双均线策略',
          description: '基于短期和长期移动平均线交叉的经典策略',
          category: '趋势跟踪',
          params: {
            shortPeriod: { type: 'number', default: 5, min: 1, max: 50, description: '短期均线周期' },
            longPeriod: { type: 'number', default: 20, min: 5, max: 200, description: '长期均线周期' },
            initialCapital: { type: 'number', default: 1000000, min: 10000, description: '初始资金' },
            startDate: { type: 'date', default: '2024-01-01', description: '回测开始日期' },
            endDate: { type: 'date', default: '2024-12-31', description: '回测结束日期' }
          }
        },
        {
          id: 'rsi_reversal',
          name: 'RSI逆向策略',
          description: '基于RSI指标超买超卖的逆向交易策略',
          category: '均值回归',
          params: {
            rsiPeriod: { type: 'number', default: 14, min: 5, max: 30, description: 'RSI计算周期' },
            overbought: { type: 'number', default: 70, min: 60, max: 90, description: '超买阈值' },
            oversold: { type: 'number', default: 30, min: 10, max: 40, description: '超卖阈值' },
            initialCapital: { type: 'number', default: 1000000, min: 10000, description: '初始资金' },
            startDate: { type: 'date', default: '2024-01-01', description: '回测开始日期' },
            endDate: { type: 'date', default: '2024-12-31', description: '回测结束日期' }
          }
        },
        {
          id: 'macd_cross',
          name: 'MACD交叉策略',
          description: 'MACD指标金叉死叉交易策略',
          category: '趋势跟踪',
          params: {
            fastPeriod: { type: 'number', default: 12, min: 5, max: 20, description: '快线周期' },
            slowPeriod: { type: 'number', default: 26, min: 20, max: 50, description: '慢线周期' },
            signalPeriod: { type: 'number', default: 9, min: 5, max: 15, description: '信号线周期' },
            initialCapital: { type: 'number', default: 1000000, min: 10000, description: '初始资金' },
            startDate: { type: 'date', default: '2024-01-01', description: '回测开始日期' },
            endDate: { type: 'date', default: '2024-12-31', description: '回测结束日期' }
          }
        }
      ];
      
      res.json({
        success: true,
        data: {
          strategies,
          count: strategies.length
        }
      });
      
    } catch (error) {
      console.error('获取策略模板错误:', error);
      res.status(500).json({
        success: false,
        message: '获取策略模板失败',
        code: 'STRATEGIES_ERROR'
      });
    }
  });

  return router;
}

/**
 * 验证策略参数
 */
function validateStrategyParams(strategy, params) {
  const requiredParams = {
    ma_cross: ['shortPeriod', 'longPeriod', 'initialCapital', 'startDate', 'endDate'],
    rsi_reversal: ['rsiPeriod', 'overbought', 'oversold', 'initialCapital', 'startDate', 'endDate'],
    macd_cross: ['fastPeriod', 'slowPeriod', 'signalPeriod', 'initialCapital', 'startDate', 'endDate']
  };

  const required = requiredParams[strategy];
  if (!required) {
    return { valid: false, message: '不支持的策略类型' };
  }

  for (const param of required) {
    if (params[param] === undefined || params[param] === null) {
      return { valid: false, message: `缺少参数: ${param}` };
    }
  }

  // 特定验证
  if (params.initialCapital && params.initialCapital < 10000) {
    return { valid: false, message: '初始资金不能少于1万元' };
  }

  if (params.startDate && params.endDate && new Date(params.startDate) >= new Date(params.endDate)) {
    return { valid: false, message: '开始日期必须早于结束日期' };
  }

  return { valid: true };
}

/**
 * 执行回测
 */
async function executeBacktest(backtestId, dataLoader) {
  try {
    const backtest = backtests.get(backtestId);
    if (!backtest) return;

    console.log(`🔄 开始执行回测: ${backtestId} - ${backtest.name}`);

    // 更新状态为运行中
    backtest.status = 'running';
    backtest.progress = 0;
    backtest.startTime = new Date().toISOString();
    backtests.set(backtestId, backtest);

    // 模拟回测执行过程
    const totalSteps = 100;
    for (let i = 0; i <= totalSteps; i++) {
      await new Promise(resolve => setTimeout(resolve, 50)); // 模拟计算时间
      
      backtest.progress = i;
      backtests.set(backtestId, backtest);
      
      if (i % 20 === 0) {
        console.log(`📈 回测进度: ${backtestId} - ${i}%`);
      }
    }

    // 生成回测结果
    const results = generateBacktestResults(backtest);
    
    // 更新回测状态
    backtest.status = 'completed';
    backtest.progress = 100;
    backtest.endTime = new Date().toISOString();
    backtest.duration = Math.floor((new Date(backtest.endTime) - new Date(backtest.startTime)) / 1000);
    backtest.results = results.summary;
    
    backtests.set(backtestId, backtest);
    backtestResults.set(backtestId, results);

    console.log(`✅ 回测完成: ${backtestId} - 收益率: ${(results.summary.totalReturn * 100).toFixed(2)}%`);

  } catch (error) {
    console.error(`❌ 回测执行错误 ${backtestId}:`, error);
    
    // 更新为失败状态
    const backtest = backtests.get(backtestId);
    if (backtest) {
      backtest.status = 'failed';
      backtest.endTime = new Date().toISOString();
      backtests.set(backtestId, backtest);
    }
  }
}

/**
 * 生成回测结果
 */
function generateBacktestResults(backtest) {
  const { params } = backtest;
  const initialCapital = params.initialCapital;
  
  // 模拟生成回测数据
  const randomReturn = (Math.random() - 0.3) * 0.4; // -30% 到 +10% 的收益范围
  const finalValue = initialCapital * (1 + randomReturn);
  
  const totalReturn = randomReturn;
  const annualizedReturn = totalReturn * 2; // 假设半年期，年化
  const maxDrawdown = Math.abs(randomReturn * 0.6);
  const sharpeRatio = totalReturn > 0 ? 0.5 + Math.random() * 2 : -Math.random() * 2;
  const winRate = 0.3 + Math.random() * 0.4; // 30%-70% 胜率
  const profitLossRatio = 0.5 + Math.random() * 2;
  const totalTrades = Math.floor(50 + Math.random() * 200);
  
  // 生成权益曲线数据
  const equityCurve = [];
  const trades = [];
  let currentValue = initialCapital;
  
  const days = Math.floor((new Date(params.endDate) - new Date(params.startDate)) / (1000 * 60 * 60 * 24));
  
  for (let i = 0; i <= days; i++) {
    const date = new Date(params.startDate);
    date.setDate(date.getDate() + i);
    
    // 随机波动
    const dailyReturn = (Math.random() - 0.5) * 0.04; // ±2% 日波动
    currentValue *= (1 + dailyReturn);
    
    equityCurve.push({
      date: date.toISOString().split('T')[0],
      value: Number(currentValue.toFixed(2)),
      return: Number(((currentValue - initialCapital) / initialCapital * 100).toFixed(2))
    });
  }
  
  // 生成交易记录
  for (let i = 0; i < totalTrades; i++) {
    const tradeDate = new Date(params.startDate);
    tradeDate.setDate(tradeDate.getDate() + Math.floor(Math.random() * days));
    
    const isWin = Math.random() < winRate;
    const returnRate = isWin ? Math.random() * 0.1 : -Math.random() * 0.08;
    const quantity = Math.floor(100 + Math.random() * 1000) * 100;
    const price = 10 + Math.random() * 40;
    
    trades.push({
      id: `trade_${i + 1}`,
      date: tradeDate.toISOString().split('T')[0],
      symbol: backtest.symbol,
      side: i % 2 === 0 ? 'buy' : 'sell',
      quantity,
      price: Number(price.toFixed(2)),
      value: Number((quantity * price).toFixed(2)),
      pnl: Number((quantity * price * returnRate).toFixed(2)),
      returnRate: Number((returnRate * 100).toFixed(2))
    });
  }
  
  return {
    summary: {
      totalReturn: Number(totalReturn.toFixed(4)),
      annualizedReturn: Number(annualizedReturn.toFixed(4)),
      maxDrawdown: Number(maxDrawdown.toFixed(4)),
      sharpeRatio: Number(sharpeRatio.toFixed(2)),
      winRate: Number(winRate.toFixed(2)),
      profitLossRatio: Number(profitLossRatio.toFixed(2)),
      totalTrades,
      finalValue: Number(finalValue.toFixed(2))
    },
    equityCurve,
    trades: trades.sort((a, b) => new Date(a.date) - new Date(b.date)),
    metrics: {
      volatility: Number((Math.random() * 0.3).toFixed(4)),
      beta: Number((0.5 + Math.random()).toFixed(2)),
      alpha: Number((randomReturn * 0.5).toFixed(4)),
      informationRatio: Number((sharpeRatio * 0.8).toFixed(2))
    }
  };
}

/**
 * 生成详细结果数据
 */
function generateDetailedResults(backtest) {
  if (backtest.results) {
    return generateBacktestResults(backtest);
  }
  return null;
}