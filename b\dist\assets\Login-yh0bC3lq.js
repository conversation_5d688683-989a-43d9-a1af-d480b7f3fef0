import{c as h,o as ee,e as te,t as N,s as e,i as y,m as T,b as Y,h as Z,r as ie,a as O,A as ne,u as re}from"./index-D_4j4rLs.js";var oe=N("<div><div><div></div><div>🧩</div><div></div></div><div><div></div><div></div><div></div><button title=刷新验证码>↻"),ae=N("<div>");function se($){const[u,I]=h(!1),[g,R]=h(!1),[z,L]=h(0),[l,b]=h(!1),[C,A]=h(0),[P,Q]=h(0),X=()=>$.width||320,w=()=>$.height||160,_=()=>$.sliderText||"向右滑动验证",M=()=>{const n=Math.random()*(X()-120)+60;return Q(n),n},m=()=>{I(!1),R(!1),L(0),b(!1),M()},j=()=>{Math.abs(z()-P())<10?(I(!0),R(!1),$.onSuccess?.()):(R(!0),I(!1),$.onFail?.(),setTimeout(m,1e3))},V=n=>{if(u())return;b(!0);const o="touches"in n?n.touches[0].clientX:n.clientX;A(o-z())},S=n=>{if(!l()||u())return;n.preventDefault();const o="touches"in n?n.touches[0].clientX:n.clientX,x=Math.max(0,Math.min(o-C(),X()-60));L(x)},E=()=>{!l()||u()||(b(!1),j())};return ee(()=>{M(),document.addEventListener("mousemove",S),document.addEventListener("mouseup",E),document.addEventListener("touchmove",S,{passive:!1}),document.addEventListener("touchend",E)}),te(()=>{document.removeEventListener("mousemove",S),document.removeEventListener("mouseup",E),document.removeEventListener("touchmove",S),document.removeEventListener("touchend",E)}),(()=>{var n=oe(),o=n.firstChild,x=o.firstChild,c=x.nextSibling,i=c.nextSibling,v=o.nextSibling,a=v.firstChild,s=a.nextSibling,p=s.nextSibling,d=p.nextSibling;return e(n,"border","1px solid #e1e5e9"),e(n,"borderRadius","8px"),e(n,"backgroundColor","#f7f9fa"),e(n,"overflow","hidden"),e(n,"userSelect","none"),e(o,"background","linear-gradient(135deg, #667eea 0%, #764ba2 100%)"),e(o,"position","relative"),e(o,"display","flex"),e(o,"alignItems","center"),e(o,"justifyContent","center"),e(x,"position","absolute"),e(x,"width","100%"),e(x,"height","100%"),e(x,"backgroundImage",`
            radial-gradient(circle at 20% 20%, rgba(255,255,255,0.2) 2px, transparent 2px),
            radial-gradient(circle at 80% 80%, rgba(255,255,255,0.2) 2px, transparent 2px),
            radial-gradient(circle at 40% 60%, rgba(255,255,255,0.1) 1px, transparent 1px)
          `),e(x,"backgroundSize","50px 50px, 30px 30px, 20px 20px"),e(c,"position","absolute"),e(c,"top","50%"),e(c,"transform","translateY(-50%)"),e(c,"width","50px"),e(c,"height","50px"),e(c,"border","2px dashed rgba(255,255,255,0.6)"),e(c,"borderRadius","8px"),e(c,"display","flex"),e(c,"alignItems","center"),e(c,"justifyContent","center"),e(c,"color","rgba(255,255,255,0.8)"),e(c,"fontSize","20px"),e(i,"position","absolute"),e(i,"top","50%"),e(i,"transform","translateY(-50%)"),e(i,"width","50px"),e(i,"height","50px"),e(i,"borderRadius","8px"),e(i,"display","flex"),e(i,"alignItems","center"),e(i,"justifyContent","center"),e(i,"color","white"),e(i,"fontSize","20px"),e(i,"boxShadow","0 4px 8px rgba(0,0,0,0.2)"),y(i,(()=>{var t=T(()=>!!u());return()=>t()?"✓":g()?"✗":"🧩"})()),y(o,(()=>{var t=T(()=>!!(u()||g()));return()=>t()&&(()=>{var r=ae();return e(r,"position","absolute"),e(r,"top","50%"),e(r,"left","50%"),e(r,"transform","translate(-50%, -50%)"),e(r,"backgroundColor","rgba(0,0,0,0.8)"),e(r,"color","white"),e(r,"padding","8px 16px"),e(r,"borderRadius","20px"),e(r,"fontSize","14px"),e(r,"fontWeight","500"),y(r,()=>u()?"✅ 验证成功":"❌ 验证失败"),r})()})(),null),e(v,"height","50px"),e(v,"backgroundColor","#f7f9fa"),e(v,"borderTop","1px solid #e1e5e9"),e(v,"position","relative"),e(v,"display","flex"),e(v,"alignItems","center"),a.$$touchstart=V,a.$$mousedown=V,e(a,"position","absolute"),e(a,"width","60px"),e(a,"height","40px"),e(a,"border","1px solid #d9d9d9"),e(a,"borderRadius","6px"),e(a,"display","flex"),e(a,"alignItems","center"),e(a,"justifyContent","center"),e(a,"color","white"),e(a,"fontSize","18px"),e(a,"boxShadow","0 2px 4px rgba(0,0,0,0.1)"),e(a,"zIndex","10"),y(a,(()=>{var t=T(()=>!!u());return()=>t()?"✓":g()?"✗":"→"})()),e(s,"position","absolute"),e(s,"left","0"),e(s,"top","50%"),e(s,"transform","translateY(-50%)"),e(s,"height","40px"),e(s,"borderRadius","6px"),e(s,"transition","all 0.3s ease"),e(p,"width","100%"),e(p,"textAlign","center"),e(p,"color","#999"),e(p,"fontSize","14px"),e(p,"pointerEvents","none"),e(p,"zIndex","5"),y(p,(()=>{var t=T(()=>!!u());return()=>t()?"验证成功":T(()=>!!g())()?"验证失败，请重试":_()})()),d.$$click=()=>{m(),$.onRefresh?.()},e(d,"position","absolute"),e(d,"right","10px"),e(d,"width","30px"),e(d,"height","30px"),e(d,"border","none"),e(d,"backgroundColor","transparent"),e(d,"cursor","pointer"),e(d,"fontSize","16px"),e(d,"color","#999"),e(d,"borderRadius","50%"),e(d,"display","flex"),e(d,"alignItems","center"),e(d,"justifyContent","center"),e(d,"zIndex","10"),Y(t=>{var r=`${X()}px`,f=`${w()}px`,k=`${P()}px`,F=`${z()}px`,D=u()?"#52c41a":g()?"#ff4d4f":"#1890ff",U=l()?"none":"all 0.3s ease",W=l()?"grabbing":"grab",q=`${z()}px`,B=u()?"#52c41a":g()?"#ff4d4f":"#1890ff",G=l()?"grabbing":"grab",H=l()?"none":"all 0.3s ease",J=`${z()+30}px`,K=u()?"#f6ffed":g()?"#fff2f0":"#e6f7ff";return r!==t.e&&e(n,"width",t.e=r),f!==t.t&&e(o,"height",t.t=f),k!==t.a&&e(c,"left",t.a=k),F!==t.o&&e(i,"left",t.o=F),D!==t.i&&e(i,"backgroundColor",t.i=D),U!==t.n&&e(i,"transition",t.n=U),W!==t.s&&e(i,"cursor",t.s=W),q!==t.h&&e(a,"left",t.h=q),B!==t.r&&e(a,"backgroundColor",t.r=B),G!==t.d&&e(a,"cursor",t.d=G),H!==t.l&&e(a,"transition",t.l=H),J!==t.u&&e(s,"width",t.u=J),K!==t.c&&e(s,"backgroundColor",t.c=K),t},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0}),n})()}Z(["mousedown","touchstart","click"]);var de=N('<div style=min-height:100vh;align-items:center;justify-content:center><div style="border-radius:12px;box-shadow:0 20px 40px rgba(0,0,0,0.1);max-width:400px"><div style=text-align:center;margin-bottom:30px><div style=border-radius:12px;align-items:center;justify-content:center;font-size:24px;font-weight:bold>Q</div><h1 style=font-size:24px;font-weight:bold>量化交易平台</h1><p>请登录您的账户</p></div><form><div style=margin-bottom:20px><label style=font-size:14px;font-weight:500;margin-bottom:6px>用户名</label><input type=text placeholder=请输入用户名 style=border-radius:6px;font-size:14px;box-sizing:border-box></div><div style=margin-bottom:20px><label style=font-size:14px;font-weight:500;margin-bottom:6px>密码</label><input type=password placeholder=请输入密码 style=border-radius:6px;font-size:14px;box-sizing:border-box></div><div style=margin-bottom:20px><label style=font-size:14px;font-weight:500;margin-bottom:6px>安全验证</label></div><button type=submit style=border-radius:6px;font-size:16px;font-weight:500;margin-bottom:16px></button><div style=text-align:center></div></form><div style=margin-top:24px;background-color:#f3f4f6;border-radius:6px;font-size:12px;text-align:center><div style=font-weight:500;margin-bottom:4px>演示账户</div><div>用户名: admin</div><div>密码: 123456'),le=N("<div style=border-radius:6px;font-size:14px;text-align:center;margin-bottom:16px>");function ue(){const $=ie(),[u,I]=h(""),[g,R]=h(""),[z,L]=h(!1),[l,b]=h(""),[C,A]=h(!1),P=()=>{L(!0),b("✅ 滑动验证成功")},Q=()=>{L(!1),b("❌ 滑动验证失败，请重试")},X=async w=>{if(w.preventDefault(),!z()){b("❌ 请先完成滑动验证");return}if(!u()||!g()){b("❌ 请输入用户名和密码");return}A(!0),b("🔄 正在登录...");try{await re.login({username:u(),password:g()}),b("✅ 登录成功！正在跳转..."),setTimeout(()=>{$("/")},1e3)}catch{b("❌ 用户名或密码错误"),L(!1)}finally{A(!1)}};return(()=>{var w=de(),_=w.firstChild,M=_.firstChild,m=M.firstChild,j=m.nextSibling,V=j.nextSibling,S=M.nextSibling,E=S.firstChild,n=E.firstChild,o=n.nextSibling,x=E.nextSibling,c=x.firstChild,i=c.nextSibling,v=x.nextSibling,a=v.firstChild,s=v.nextSibling,p=s.nextSibling,d=S.nextSibling;return d.firstChild,e(w,"background","linear-gradient(135deg, #667eea 0%, #764ba2 100%)"),e(w,"display","flex"),e(w,"padding","20px"),e(_,"background","white"),e(_,"padding","40px"),e(_,"width","100%"),e(m,"width","60px"),e(m,"height","60px"),e(m,"background","linear-gradient(135deg, #3b82f6, #1d4ed8)"),e(m,"display","flex"),e(m,"margin","0 auto 16px"),e(m,"color","white"),e(j,"color","#111827"),e(j,"margin","0 0 8px"),e(V,"color","#6b7280"),e(V,"margin","0"),S.addEventListener("submit",X),e(n,"display","block"),e(n,"color","#374151"),o.addEventListener("blur",t=>t.currentTarget.style.borderColor="#d1d5db"),o.addEventListener("focus",t=>t.currentTarget.style.borderColor="#3b82f6"),o.$$input=t=>I(t.currentTarget.value),e(o,"width","100%"),e(o,"padding","12px"),e(o,"border","1px solid #d1d5db"),e(o,"outline","none"),e(o,"transition","border-color 0.2s"),e(c,"display","block"),e(c,"color","#374151"),i.addEventListener("blur",t=>t.currentTarget.style.borderColor="#d1d5db"),i.addEventListener("focus",t=>t.currentTarget.style.borderColor="#3b82f6"),i.$$input=t=>R(t.currentTarget.value),e(i,"width","100%"),e(i,"padding","12px"),e(i,"border","1px solid #d1d5db"),e(i,"outline","none"),e(i,"transition","border-color 0.2s"),e(a,"display","block"),e(a,"color","#374151"),y(v,O(se,{width:320,height:120,sliderText:"向右滑动完成验证",onSuccess:P,onFail:Q,onRefresh:()=>L(!1)}),null),s.addEventListener("mouseleave",t=>{C()||(t.currentTarget.style.backgroundColor="#3b82f6")}),s.addEventListener("mouseenter",t=>{C()||(t.currentTarget.style.backgroundColor="#2563eb")}),e(s,"width","100%"),e(s,"padding","12px"),e(s,"color","white"),e(s,"border","none"),e(s,"transition","background-color 0.2s"),y(s,()=>C()?"登录中...":"登录"),y(S,(()=>{var t=T(()=>!!l());return()=>t()&&(()=>{var r=le();return e(r,"padding","12px"),y(r,l),Y(f=>{var k=l().includes("成功")?"#f0f9ff":l().includes("失败")||l().includes("错误")?"#fef2f2":"#f9fafb",F=l().includes("成功")?"#1e40af":l().includes("失败")||l().includes("错误")?"#dc2626":"#374151",D=`1px solid ${l().includes("成功")?"#bfdbfe":l().includes("失败")||l().includes("错误")?"#fecaca":"#e5e7eb"}`;return k!==f.e&&e(r,"background-color",f.e=k),F!==f.t&&e(r,"color",f.t=F),D!==f.a&&e(r,"border",f.a=D),f},{e:void 0,t:void 0,a:void 0}),r})()})(),p),y(p,O(ne,{href:"/",style:{color:"#6b7280","font-size":"14px","text-decoration":"none"},children:"← 返回首页"})),e(d,"padding","12px"),e(d,"color","#6b7280"),Y(t=>{var r=C(),f=C()?"#9ca3af":"#3b82f6",k=C()?"not-allowed":"pointer";return r!==t.e&&(s.disabled=t.e=r),f!==t.t&&e(s,"background",t.t=f),k!==t.a&&e(s,"cursor",t.a=k),t},{e:void 0,t:void 0,a:void 0}),Y(()=>o.value=u()),Y(()=>i.value=g()),w})()}Z(["input"]);export{ue as default};
