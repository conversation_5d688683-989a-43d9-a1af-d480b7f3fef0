import { createSignal, createEffect, onCleanup, onMount, Show, For } from 'solid-js'
import { createStore } from 'solid-js/store'
import { css } from '../../styled-system/css'
import { formatCurrency, formatPrice, formatPercent, formatChange } from '../../utils/formatters'
import { useAtom } from 'jotai'
import { tradingAtom, userAtom, marketDataAtom } from '../../stores/atoms'

interface StockInfo {
  symbol: string
  name: string
  currentPrice: number
  change: number
  changePercent: number
  exchange?: string
}

interface Position {
  symbol: string
  totalQuantity: number
  availableQuantity: number
  avgPrice: number
  unrealizedPnl: number
  unrealizedPnlPercent: number
}

interface RiskWarning {
  type: string
  level: 'info' | 'warning' | 'error'
  message: string
}

interface OrderFormData {
  symbol: string
  side: 'buy' | 'sell'
  orderType: 'limit' | 'market' | 'stop' | 'stop-profit'
  quantity: number
  price: number
}

interface Props {
  defaultSymbol?: string
  defaultSide?: 'buy' | 'sell'
  quickTradeMode?: boolean
  onSubmit?: (data: OrderFormData) => void
  onStockSelect?: (stock: StockInfo) => void
  onSideChange?: (side: 'buy' | 'sell') => void
}

export function OrderForm(props: Props) {
  // State management
  const [trading] = useAtom(tradingAtom)
  const [user] = useAtom(userAtom)
  const [marketData] = useAtom(marketDataAtom)

  // Form state
  const [form, setForm] = createStore<OrderFormData>({
    symbol: props.defaultSymbol || '',
    side: props.defaultSide || 'buy',
    orderType: 'limit',
    quantity: 0,
    price: 0
  })

  const [submitting, setSubmitting] = createSignal(false)
  const [searchLoading, setSearchLoading] = createSignal(false)
  const [selectedStock, setSelectedStock] = createSignal<StockInfo | null>(null)
  const [realTimePrice, setRealTimePrice] = createSignal<number | null>(null)
  const [stockSuggestions, setStockSuggestions] = createSignal<StockInfo[]>([])
  const [showSuggestions, setShowSuggestions] = createSignal(false)
  const [errors, setErrors] = createStore<Record<string, string>>({})

  // Computed values
  const availableCash = () => trading.account?.availableCash || 0

  const currentPosition = () => {
    if (!form.symbol) return null
    return trading.positions?.find((p: Position) => p.symbol === form.symbol) || null
  }

  const showPriceInput = () => {
    return ['limit', 'stop', 'stop-profit'].includes(form.orderType)
  }

  const maxQuantity = () => {
    if (form.side === 'sell' && currentPosition()) {
      return currentPosition()!.availableQuantity
    }

    if (form.side === 'buy' && form.price > 0) {
      return Math.floor(availableCash() / form.price / 100) * 100
    }

    return 0
  }

  const canCalculateQuantity = () => {
    return form.side === 'buy' && form.price > 0 && availableCash() > 0
  }

  const estimatedAmount = () => {
    if (form.orderType === 'market' && selectedStock()) {
      return selectedStock()!.currentPrice * form.quantity
    }
    return form.price * form.quantity
  }

  const estimatedFee = () => {
    const feeRate = 0.0003
    const minFee = 5
    return Math.max(estimatedAmount() * feeRate, minFee)
  }

  const canSubmit = () => {
    if (submitting()) return false
    if (!form.symbol || !form.quantity) return false
    if (showPriceInput() && !form.price) return false

    if (form.side === 'sell') {
      const position = currentPosition()
      return position && form.quantity <= position.availableQuantity
    }

    if (form.side === 'buy') {
      const totalAmount = estimatedAmount() + estimatedFee()
      return totalAmount <= availableCash()
    }

    return false
  }

  const submitButtonText = () => {
    if (submitting()) return '提交中...'

    const action = form.side === 'buy' ? '买入' : '卖出'
    const symbol = selectedStock()?.name || form.symbol

    if (form.quantity && symbol) {
      return `${action} ${symbol} ${form.quantity}股`
    }

    return action
  }

  const riskWarnings = () => {
    const warnings: RiskWarning[] = []

    if (form.side === 'buy') {
      const totalAmount = estimatedAmount() + estimatedFee()
      const riskRatio = totalAmount / availableCash()

      if (riskRatio > 0.9) {
        warnings.push({
          type: 'capital',
          level: 'warning',
          message: '此笔交易将占用大部分可用资金，请注意风险控制'
        })
      }
    }

    if (showPriceInput() && selectedStock() && form.price > 0) {
      const stock = selectedStock()!
      const deviation = Math.abs(form.price - stock.currentPrice) / stock.currentPrice

      if (deviation > 0.05) {
        warnings.push({
          type: 'price',
          level: 'warning',
          message: '委托价格偏离市价较大，请确认价格是否正确'
        })
      }
    }

    return warnings
  }

  // Methods
  const searchStocks = async (query: string) => {
    if (!query.trim()) {
      setStockSuggestions([])
      setShowSuggestions(false)
      return
    }

    setSearchLoading(true)
    
    try {
      // Mock API call - replace with real implementation
      const mockStocks: StockInfo[] = [
        { symbol: '000001', name: '平安银行', currentPrice: 12.50, change: 0.15, changePercent: 1.22 },
        { symbol: '000002', name: '万科A', currentPrice: 18.30, change: -0.25, changePercent: -1.35 },
        { symbol: '600000', name: '浦发银行', currentPrice: 8.90, change: 0.05, changePercent: 0.56 }
      ].filter(stock => 
        stock.symbol.includes(query) || stock.name.includes(query)
      )

      setStockSuggestions(mockStocks)
      setShowSuggestions(mockStocks.length > 0)
    } catch (error) {
      console.error('搜索股票失败:', error)
    } finally {
      setSearchLoading(false)
    }
  }

  const handleStockSelect = (stock: StockInfo) => {
    setSelectedStock(stock)
    setForm('symbol', stock.symbol)
    
    if (showPriceInput()) {
      setForm('price', stock.currentPrice)
    }

    setShowSuggestions(false)
    props.onStockSelect?.(stock)
    calculateAmount()
  }

  const handleSideChange = (side: 'buy' | 'sell') => {
    setForm('side', side)
    props.onSideChange?.(side)

    if (side === 'sell' && !currentPosition()) {
      // Show warning - in real app you'd use a toast/notification system
      console.warn('您没有该股票的持仓')
    }

    if (form.quantity > maxQuantity()) {
      setForm('quantity', maxQuantity())
    }
  }

  const handleOrderTypeChange = (orderType: OrderFormData['orderType']) => {
    setForm('orderType', orderType)

    if (orderType === 'market') {
      setForm('price', 0)
    } else if (selectedStock()) {
      setForm('price', selectedStock()!.currentPrice)
    }

    calculateAmount()
  }

  const setPriceByMarket = (offset: number) => {
    const stock = selectedStock()
    if (!stock) return

    const basePrice = stock.currentPrice
    setForm('price', Number((basePrice * (1 + offset)).toFixed(2)))
    calculateAmount()
  }

  const setQuantityByPercent = (percent: number) => {
    if (form.side === 'sell' && currentPosition()) {
      const position = currentPosition()!
      setForm('quantity', Math.floor(position.availableQuantity * percent / 100) * 100)
    } else if (form.side === 'buy' && form.price > 0) {
      const maxAmount = availableCash() * percent
      setForm('quantity', Math.floor(maxAmount / form.price / 100) * 100)
    }

    calculateAmount()
  }

  const calculateAmount = () => {
    // Trigger reactivity - amounts are computed automatically
  }

  const getPriceClass = (changePercent: number) => {
    if (changePercent > 0) return 'text-red-500'
    if (changePercent < 0) return 'text-green-500'
    return 'text-gray-500'
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!form.symbol) newErrors.symbol = '请选择股票'
    if (!form.quantity) newErrors.quantity = '请输入数量'
    if (showPriceInput() && !form.price) newErrors.price = '请输入价格'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async () => {
    if (!validateForm()) return

    const warnings = riskWarnings()
    if (warnings.some(w => w.level === 'error')) {
      console.error('存在风险错误，无法提交订单')
      return
    }

    if (warnings.some(w => w.level === 'warning')) {
      const confirmed = confirm('检测到风险警告，是否继续提交订单？')
      if (!confirmed) return
    }

    setSubmitting(true)

    try {
      const orderData = {
        symbol: form.symbol,
        side: form.side,
        orderType: form.orderType,
        quantity: form.quantity,
        price: form.price
      }

      props.onSubmit?.(orderData)
      
      // Reset form
      setForm({
        symbol: '',
        side: 'buy',
        orderType: 'limit',
        quantity: 0,
        price: 0
      })
      setSelectedStock(null)

    } catch (error) {
      console.error('提交订单失败:', error)
    } finally {
      setSubmitting(false)
    }
  }

  // Effects
  createEffect(() => {
    if (form.symbol && !selectedStock()) {
      searchStocks(form.symbol)
    }
  })

  onMount(() => {
    if (props.defaultSymbol) {
      searchStocks(props.defaultSymbol)
    }
  })

  return (
    <div class={css({
      bg: 'white',
      borderRadius: '8px',
      padding: '20px',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
    })}>
      {/* Form Header */}
      <div class={css({
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px',
        paddingBottom: '12px',
        borderBottom: '1px solid #e8e8e8'
      })}>
        <h3 class={css({ margin: 0, fontSize: '18px', color: '#333' })}>
          下单交易
        </h3>
        <div class={css({ fontSize: '14px', color: '#666' })}>
          可用资金: {formatCurrency(availableCash())}
        </div>
      </div>

      <form onSubmit={(e) => { e.preventDefault(); handleSubmit() }}>
        {/* Stock Symbol Input */}
        <div class={css({ marginBottom: '16px' })}>
          <label class={css({ display: 'block', marginBottom: '8px', fontWeight: 'bold' })}>
            股票代码
          </label>
          <div class={css({ position: 'relative' })}>
            <input
              type="text"
              value={form.symbol}
              onInput={(e) => {
                const value = e.currentTarget.value
                setForm('symbol', value)
                searchStocks(value)
              }}
              onBlur={() => setShowSuggestions(false)}
              placeholder="输入股票代码或名称"
              class={css({
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #dcdfe6',
                borderRadius: '4px',
                fontSize: '14px'
              })}
            />
            
            <Show when={showSuggestions() && stockSuggestions().length > 0}>
              <div class={css({
                position: 'absolute',
                top: '100%',
                left: 0,
                right: 0,
                bg: 'white',
                border: '1px solid #dcdfe6',
                borderRadius: '4px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                zIndex: 10,
                maxHeight: '200px',
                overflowY: 'auto'
              })}>
                <For each={stockSuggestions()}>
                  {(stock) => (
                    <div
                      class={css({
                        padding: '8px 12px',
                        cursor: 'pointer',
                        borderBottom: '1px solid #f0f0f0',
                        '&:hover': { bg: '#f5f7fa' },
                        '&:last-child': { borderBottom: 'none' }
                      })}
                      onMouseDown={() => handleStockSelect(stock)}
                    >
                      <div class={css({ display: 'flex', justifyContent: 'space-between', alignItems: 'center' })}>
                        <div>
                          <div class={css({ fontWeight: 600, color: '#333' })}>
                            {stock.symbol}
                          </div>
                          <div class={css({ fontSize: '12px', color: '#666' })}>
                            {stock.name}
                          </div>
                        </div>
                        <div class={css({ textAlign: 'right' })}>
                          <div class={getPriceClass(stock.changePercent)}>
                            {formatPrice(stock.currentPrice)}
                          </div>
                          <div class={`${css({ fontSize: '12px' })} ${getPriceClass(stock.changePercent)}`}>
                            {formatChange(stock.change, stock.changePercent)}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </For>
              </div>
            </Show>
          </div>
        </div>

        {/* Trading Side */}
        <div class={css({ marginBottom: '16px' })}>
          <label class={css({ display: 'block', marginBottom: '8px', fontWeight: 'bold' })}>
            交易方向
          </label>
          <div class={css({ display: 'flex', gap: '8px' })}>
            <button
              type="button"
              onClick={() => handleSideChange('buy')}
              class={css({
                flex: 1,
                padding: '8px 16px',
                border: '1px solid',
                borderRadius: '4px',
                fontSize: '14px',
                cursor: 'pointer',
                borderColor: form.side === 'buy' ? '#f56c6c' : '#dcdfe6',
                bg: form.side === 'buy' ? '#f56c6c' : 'white',
                color: form.side === 'buy' ? 'white' : '#606266'
              })}
            >
              买入
            </button>
            <button
              type="button"
              onClick={() => handleSideChange('sell')}
              class={css({
                flex: 1,
                padding: '8px 16px',
                border: '1px solid',
                borderRadius: '4px',
                fontSize: '14px',
                cursor: 'pointer',
                borderColor: form.side === 'sell' ? '#67c23a' : '#dcdfe6',
                bg: form.side === 'sell' ? '#67c23a' : 'white',
                color: form.side === 'sell' ? 'white' : '#606266'
              })}
            >
              卖出
            </button>
          </div>
        </div>

        {/* Order Type */}
        <div class={css({ marginBottom: '16px' })}>
          <label class={css({ display: 'block', marginBottom: '8px', fontWeight: 'bold' })}>
            订单类型
          </label>
          <select
            value={form.orderType}
            onChange={(e) => handleOrderTypeChange(e.currentTarget.value as OrderFormData['orderType'])}
            class={css({
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #dcdfe6',
              borderRadius: '4px',
              fontSize: '14px'
            })}
          >
            <option value="limit">限价单</option>
            <option value="market">市价单</option>
            <option value="stop">止损单</option>
            <option value="stop-profit">止盈单</option>
          </select>
        </div>

        {/* Price Input */}
        <Show when={showPriceInput()}>
          <div class={css({ marginBottom: '16px' })}>
            <label class={css({ display: 'block', marginBottom: '8px', fontWeight: 'bold' })}>
              委托价格
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={form.price}
              onInput={(e) => {
                setForm('price', Number(e.currentTarget.value))
                calculateAmount()
              }}
              placeholder="输入委托价格"
              class={css({
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #dcdfe6',
                borderRadius: '4px',
                fontSize: '14px',
                marginBottom: '8px'
              })}
            />
            <div class={css({ display: 'flex', gap: '4px' })}>
              <button
                type="button"
                onClick={() => setPriceByMarket(-0.01)}
                disabled={!selectedStock()}
                class={css({
                  padding: '4px 8px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '4px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  bg: 'white'
                })}
              >
                -1%
              </button>
              <button
                type="button"
                onClick={() => setPriceByMarket(0)}
                disabled={!selectedStock()}
                class={css({
                  padding: '4px 8px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '4px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  bg: 'white'
                })}
              >
                现价
              </button>
              <button
                type="button"
                onClick={() => setPriceByMarket(0.01)}
                disabled={!selectedStock()}
                class={css({
                  padding: '4px 8px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '4px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  bg: 'white'
                })}
              >
                +1%
              </button>
            </div>
          </div>
        </Show>

        {/* Quantity Input */}
        <div class={css({ marginBottom: '16px' })}>
          <label class={css({ display: 'block', marginBottom: '8px', fontWeight: 'bold' })}>
            委托数量
          </label>
          <input
            type="number"
            step="100"
            min="0"
            max={maxQuantity()}
            value={form.quantity}
            onInput={(e) => {
              setForm('quantity', Number(e.currentTarget.value))
              calculateAmount()
            }}
            placeholder="输入委托数量"
            class={css({
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #dcdfe6',
              borderRadius: '4px',
              fontSize: '14px',
              marginBottom: '8px'
            })}
          />
          <div class={css({ display: 'flex', gap: '4px' })}>
            <button
              type="button"
              onClick={() => setQuantityByPercent(0.25)}
              disabled={!canCalculateQuantity()}
              class={css({
                padding: '4px 8px',
                border: '1px solid #dcdfe6',
                borderRadius: '4px',
                fontSize: '12px',
                cursor: 'pointer',
                bg: 'white'
              })}
            >
              1/4
            </button>
            <button
              type="button"
              onClick={() => setQuantityByPercent(0.5)}
              disabled={!canCalculateQuantity()}
              class={css({
                padding: '4px 8px',
                border: '1px solid #dcdfe6',
                borderRadius: '4px',
                fontSize: '12px',
                cursor: 'pointer',
                bg: 'white'
              })}
            >
              1/2
            </button>
            <button
              type="button"
              onClick={() => setQuantityByPercent(1)}
              disabled={!canCalculateQuantity()}
              class={css({
                padding: '4px 8px',
                border: '1px solid #dcdfe6',
                borderRadius: '4px',
                fontSize: '12px',
                cursor: 'pointer',
                bg: 'white'
              })}
            >
              全部
            </button>
          </div>
        </div>

        {/* Amount Display */}
        <div class={css({ marginBottom: '16px' })}>
          <label class={css({ display: 'block', marginBottom: '8px', fontWeight: 'bold' })}>
            委托金额
          </label>
          <div class={css({ display: 'flex', flexDirection: 'column', gap: '4px' })}>
            <span class={css({ fontSize: '16px', fontWeight: 600, color: '#333' })}>
              {formatCurrency(estimatedAmount())}
            </span>
            <span class={css({ fontSize: '12px', color: '#666' })}>
              手续费: {formatCurrency(estimatedFee())}
            </span>
          </div>
        </div>

        {/* Position Info */}
        <Show when={form.side === 'sell' && currentPosition()}>
          <div class={css({ marginBottom: '16px' })}>
            <label class={css({ display: 'block', marginBottom: '8px', fontWeight: 'bold' })}>
              持仓信息
            </label>
            <div class={css({ bg: '#f5f7fa', padding: '12px', borderRadius: '4px' })}>
              {(() => {
                const position = currentPosition()!
                return (
                  <>
                    <div class={css({ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' })}>
                      <span>持仓数量:</span>
                      <span>{position.totalQuantity}</span>
                    </div>
                    <div class={css({ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' })}>
                      <span>可卖数量:</span>
                      <span>{position.availableQuantity}</span>
                    </div>
                    <div class={css({ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' })}>
                      <span>成本价:</span>
                      <span>{formatPrice(position.avgPrice)}</span>
                    </div>
                    <div class={css({ display: 'flex', justifyContent: 'space-between' })}>
                      <span>浮动盈亏:</span>
                      <span class={getPriceClass(position.unrealizedPnlPercent)}>
                        {formatCurrency(position.unrealizedPnl)}
                        ({formatPercent(position.unrealizedPnlPercent)})
                      </span>
                    </div>
                  </>
                )
              })()}
            </div>
          </div>
        </Show>

        {/* Risk Warnings */}
        <Show when={riskWarnings().length > 0}>
          <div class={css({ marginBottom: '16px' })}>
            <For each={riskWarnings()}>
              {(warning) => (
                <div class={css({
                  padding: '8px 12px',
                  marginBottom: '8px',
                  borderRadius: '4px',
                  fontSize: '14px',
                  bg: warning.level === 'warning' ? '#fdf6ec' : '#f0f9ff',
                  color: warning.level === 'warning' ? '#e6a23c' : '#409eff',
                  border: `1px solid ${warning.level === 'warning' ? '#f5dab1' : '#b3d8ff'}`
                })}>
                  ⚠️ {warning.message}
                </div>
              )}
            </For>
          </div>
        </Show>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={!canSubmit()}
          class={css({
            width: '100%',
            padding: '12px',
            bg: canSubmit() ? '#409eff' : '#c0c4cc',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '16px',
            cursor: canSubmit() ? 'pointer' : 'not-allowed',
            opacity: canSubmit() ? 1 : 0.6
          })}
        >
          {submitButtonText()}
        </button>
      </form>
    </div>
  )
}

export default OrderForm