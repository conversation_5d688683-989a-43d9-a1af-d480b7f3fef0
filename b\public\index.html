<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="轻量级量化交易前端平台 - 专业的量化投资工具" />
  <meta name="keywords" content="量化交易,量化投资,SolidJS,轻量级,金融图表,AI辅助" />
  <meta name="author" content="Quant Frontend Team" />
  
  <!-- 预连接优化 -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  
  <!-- 字体加载 -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet" />
  
  <!-- 图标 -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="icon" type="image/png" href="/favicon.png" />
  
  <!-- PWA支持 -->
  <link rel="manifest" href="/manifest.json" />
  <meta name="theme-color" content="#3b82f6" />
  
  <!-- iOS Safari支持 -->
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="default" />
  <meta name="apple-mobile-web-app-title" content="量化交易平台" />
  
  <title>量化交易前端平台</title>
  
  <!-- 初始加载样式 -->
  <style>
    #loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.3s ease;
    }
    
    .loading-content {
      text-align: center;
      color: white;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .loading-text {
      font-family: 'Inter', sans-serif;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 8px;
    }
    
    .loading-subtitle {
      font-family: 'Inter', sans-serif;
      font-size: 14px;
      opacity: 0.8;
    }
  </style>
</head>
<body>
  <!-- 加载动画 -->
  <div id="loading">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <div class="loading-text">量化交易平台</div>
      <div class="loading-subtitle">正在加载核心模块...</div>
    </div>
  </div>
  
  <!-- 应用根节点 -->
  <div id="root"></div>
  
  <!-- 应用入口 -->
  <script type="module" src="/src/index.tsx"></script>
  
  <!-- 移除加载动画 -->
  <script>
    window.addEventListener('load', () => {
      setTimeout(() => {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.style.opacity = '0';
          setTimeout(() => loading.remove(), 300);
        }
      }, 500);
    });
  </script>
</body>
</html>
