/**
 * 状态类型定义
 * 为现有的 Signals 状态系统提供类型支持
 * 注意：这个文件现在主要用于类型定义，实际状态管理使用 stores/ 目录下的文件
 */

// 用户相关原子
export interface User {
  id: string;
  username: string;
  email: string;
  nickname: string;
  avatar?: string;
  phone?: string;
  role: 'admin' | 'user' | 'trader';
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
  lastLoginAt: string;
  preferences: {
    theme: 'light' | 'dark';
    language: 'zh-CN' | 'en-US';
    timezone: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
    trading: {
      confirmOrders: boolean;
      showRiskWarnings: boolean;
      defaultOrderType: 'market' | 'limit';
    };
  };
  profile: {
    realName: string;
    gender: 'male' | 'female';
    investmentExperience: 'beginner' | 'intermediate' | 'expert';
    riskTolerance: 'conservative' | 'moderate' | 'aggressive';
  };
}

// 状态管理使用现有的 stores 系统
// 这里只保留类型定义供参考

// 通知类型定义
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: number;
  read: boolean;
}

// 市场数据类型定义
export interface QuoteData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

// 策略类型定义
export interface Strategy {
  id: string;
  name: string;
  description: string;
  type: 'trend' | 'mean_reversion' | 'arbitrage' | 'custom';
  status: 'draft' | 'active' | 'paused' | 'stopped';
  code: string;
  parameters: Record<string, any>;
  performance: {
    totalReturn: number;
    sharpeRatio: number;
    maxDrawdown: number;
    winRate: number;
  };
  createdAt: string;
  updatedAt: string;
}

// 回测类型定义
export interface BacktestResult {
  id: string;
  strategyId: string;
  status: 'running' | 'completed' | 'failed';
  startDate: string;
  endDate: string;
  initialCapital: number;
  finalCapital: number;
  totalReturn: number;
  sharpeRatio: number;
  maxDrawdown: number;
  trades: number;
  winRate: number;
  createdAt: string;
}

// 其他状态类型定义
export type WSConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

export interface ModalState {
  isOpen: boolean;
  type: string;
  data?: any;
}

export interface ErrorState {
  message: string;
  code?: string;
  timestamp: number;
}

// 注意：实际的状态管理使用 stores/ 目录下的文件
// 这里的类型定义可以在需要时导入使用
