import{c as H,t as C,i as r,b as M,m as Y,d as w,e as i,f as a,F as P,g as q}from"./index-DkTGN_uB.js";var B=C("<div><div></div><div><h3> - 策略性能</h3><div></div></div><div><div><h3>策略代码</h3><div><button type=button>保存策略</button><button type=button>运行回测</button></div></div><div><pre>"),G=C("<div><div><h3></h3><div></div></div><p></p><div><span>累计收益</span><span>"),J=C("<div><div></div><div>");function Z(){const[y,k]=H("momentum"),[Q,V]=H("editor"),z=[{id:"momentum",name:"动量策略",description:"基于价格动量的交易策略",status:"active",profit:"+12.5%",code:`# 动量策略示例
import pandas as pd
import numpy as np

def momentum_strategy(data, period=20):
    """
    动量策略：当价格突破N日高点时买入，跌破N日低点时卖出
    """
    data['high_n'] = data['high'].rolling(period).max()
    data['low_n'] = data['low'].rolling(period).min()

    # 生成交易信号
    data['signal'] = 0
    data.loc[data['close'] > data['high_n'].shift(1), 'signal'] = 1  # 买入
    data.loc[data['close'] < data['low_n'].shift(1), 'signal'] = -1  # 卖出

    return data

# 策略参数
PERIOD = 20
STOP_LOSS = 0.05  # 5% 止损
TAKE_PROFIT = 0.15  # 15% 止盈`},{id:"mean_reversion",name:"均值回归策略",description:"基于价格均值回归的交易策略",status:"testing",profit:"+8.3%",code:`# 均值回归策略示例
import pandas as pd
import numpy as np

def mean_reversion_strategy(data, period=20, std_dev=2):
    """
    均值回归策略：当价格偏离均线超过N个标准差时进行反向交易
    """
    data['ma'] = data['close'].rolling(period).mean()
    data['std'] = data['close'].rolling(period).std()
    data['upper_band'] = data['ma'] + std_dev * data['std']
    data['lower_band'] = data['ma'] - std_dev * data['std']

    # 生成交易信号
    data['signal'] = 0
    data.loc[data['close'] < data['lower_band'], 'signal'] = 1  # 买入
    data.loc[data['close'] > data['upper_band'], 'signal'] = -1  # 卖出

    return data`},{id:"grid_trading",name:"网格交易策略",description:"在震荡市场中的网格交易策略",status:"paused",profit:"+5.7%",code:`# 网格交易策略示例
import pandas as pd
import numpy as np

def grid_trading_strategy(data, grid_size=0.02, max_positions=5):
    """
    网格交易策略：在价格网格中进行买卖操作
    """
    base_price = data['close'].iloc[0]

    # 计算网格价位
    grid_levels = []
    for i in range(-max_positions, max_positions + 1):
        grid_levels.append(base_price * (1 + i * grid_size))

    data['signal'] = 0
    positions = 0

    for i in range(1, len(data)):
        current_price = data['close'].iloc[i]

        # 检查是否触及网格线
        for level in grid_levels:
            if abs(current_price - level) < base_price * 0.001:  # 容差
                if current_price < base_price and positions < max_positions:
                    data.iloc[i, data.columns.get_loc('signal')] = 1  # 买入
                    positions += 1
                elif current_price > base_price and positions > -max_positions:
                    data.iloc[i, data.columns.get_loc('signal')] = -1  # 卖出
                    positions -= 1

    return data`}],R=()=>z.find(v=>v.id===y()),K=[{label:"总收益率",value:"+12.5%",trend:"up"},{label:"年化收益率",value:"+18.7%",trend:"up"},{label:"最大回撤",value:"-3.2%",trend:"down"},{label:"夏普比率",value:"1.85",trend:"up"},{label:"胜率",value:"68.5%",trend:"up"},{label:"交易次数",value:"156",trend:"neutral"}];return(()=>{var v=B(),h=v.firstChild,_=h.nextSibling,m=_.firstChild,L=m.firstChild,T=m.nextSibling,$=_.nextSibling,S=$.firstChild,W=S.firstChild,E=W.nextSibling,I=E.firstChild,U=I.nextSibling,F=S.nextSibling,N=F.firstChild;return r(h,M(P,{each:z,children:e=>(()=>{var d=G(),n=d.firstChild,l=n.firstChild,o=l.nextSibling,s=n.nextSibling,p=s.nextSibling,c=p.firstChild,f=c.nextSibling;return d.$$click=()=>k(e.id),r(l,()=>e.name),r(o,(()=>{var t=Y(()=>e.status==="active");return()=>t()?"运行中":e.status==="testing"?"测试中":"已暂停"})()),r(s,()=>e.description),r(f,()=>e.profit),w(t=>{var x=i({bg:"white",borderRadius:"12px",p:"20px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",border:y()===e.id?"2px solid #1890ff":"1px solid #f0f0f0",cursor:"pointer",transition:"all 0.3s ease",_hover:{boxShadow:"0 4px 16px rgba(0,0,0,0.15)",transform:"translateY(-2px)"}}),g=i({display:"flex",alignItems:"center",justifyContent:"space-between",mb:"12px"}),u=i({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),b=i({px:"8px",py:"4px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",bg:e.status==="active"?"#f6ffed":e.status==="testing"?"#fff7e6":"#f5f5f5",color:e.status==="active"?"#52c41a":e.status==="testing"?"#fa8c16":"#8c8c8c"}),O=i({fontSize:"14px",color:"#8c8c8c",mb:"12px",lineHeight:"1.4"}),j=i({display:"flex",alignItems:"center",justifyContent:"space-between"}),A=i({fontSize:"12px",color:"#8c8c8c"}),D=i({fontSize:"16px",fontWeight:"700",color:"#52c41a"});return x!==t.e&&a(d,t.e=x),g!==t.t&&a(n,t.t=g),u!==t.a&&a(l,t.a=u),b!==t.o&&a(o,t.o=b),O!==t.i&&a(s,t.i=O),j!==t.n&&a(p,t.n=j),A!==t.s&&a(c,t.s=A),D!==t.h&&a(f,t.h=D),t},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0}),d})()})),r(m,()=>R()?.name,L),r(T,M(P,{each:K,children:e=>(()=>{var d=J(),n=d.firstChild,l=n.nextSibling;return r(n,()=>e.label),r(l,()=>e.value),w(o=>{var s=i({textAlign:"center",p:"16px",borderRadius:"8px",bg:"#fafafa",border:"1px solid #f0f0f0"}),p=i({fontSize:"12px",color:"#8c8c8c",mb:"8px"}),c=i({fontSize:"20px",fontWeight:"700",color:e.trend==="up"?"#52c41a":e.trend==="down"?"#ff4d4f":"#262626"});return s!==o.e&&a(d,o.e=s),p!==o.t&&a(n,o.t=p),c!==o.a&&a(l,o.a=c),o},{e:void 0,t:void 0,a:void 0}),d})()})),r(N,()=>R()?.code),w(e=>{var d=i({display:"flex",flexDirection:"column",gap:"24px"}),n=i({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"16px"}),l=i({bg:"white",borderRadius:"16px",p:"24px",boxShadow:"0 4px 20px rgba(0,0,0,0.08)",border:"1px solid #f0f0f0"}),o=i({fontSize:"18px",fontWeight:"600",color:"#262626",margin:0,mb:"20px"}),s=i({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(150px, 1fr))",gap:"16px"}),p=i({bg:"white",borderRadius:"16px",p:"24px",boxShadow:"0 4px 20px rgba(0,0,0,0.08)",border:"1px solid #f0f0f0"}),c=i({display:"flex",alignItems:"center",justifyContent:"space-between",mb:"20px"}),f=i({fontSize:"18px",fontWeight:"600",color:"#262626",margin:0}),t=i({display:"flex",gap:"8px"}),x=i({px:"16px",py:"8px",bg:"#52c41a",color:"white",border:"none",borderRadius:"6px",fontSize:"14px",cursor:"pointer",transition:"all 0.2s ease",_hover:{bg:"#73d13d"}}),g=i({px:"16px",py:"8px",bg:"#1890ff",color:"white",border:"none",borderRadius:"6px",fontSize:"14px",cursor:"pointer",transition:"all 0.2s ease",_hover:{bg:"#40a9ff"}}),u=i({bg:"#1e1e1e",borderRadius:"8px",p:"16px",fontFamily:'Monaco, Menlo, "Ubuntu Mono", monospace',fontSize:"14px",lineHeight:"1.5",color:"#d4d4d4",overflow:"auto",maxHeight:"500px"}),b=i({margin:0,whiteSpace:"pre-wrap"});return d!==e.e&&a(v,e.e=d),n!==e.t&&a(h,e.t=n),l!==e.a&&a(_,e.a=l),o!==e.o&&a(m,e.o=o),s!==e.i&&a(T,e.i=s),p!==e.n&&a($,e.n=p),c!==e.s&&a(S,e.s=c),f!==e.h&&a(W,e.h=f),t!==e.r&&a(E,e.r=t),x!==e.d&&a(I,e.d=x),g!==e.l&&a(U,e.l=g),u!==e.u&&a(F,e.u=u),b!==e.c&&a(N,e.c=b),e},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0}),v})()}q(["click"]);export{Z as default};
