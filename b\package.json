{"name": "quant-frontend", "version": "1.0.0", "description": "轻量级量化交易前端平台", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "biome check --apply src", "format": "biome format --write src", "type-check": "tsc --noEmit"}, "dependencies": {"solid-js": "^1.8.0", "@solidjs/router": "^0.13.0", "jotai": "^2.6.0", "@pandacss/dev": "^0.39.0", "lightweight-charts": "^4.0.1", "@observablehq/plot": "^0.6.0", "codemirror": "^6.0.1", "@codemirror/lang-javascript": "^6.0.1", "@codemirror/lang-python": "^6.0.1", "@codemirror/theme-one-dark": "^6.0.1", "@codemirror/view": "^6.0.1", "@codemirror/state": "^6.0.1", "@solid-primitives/event-bus": "^1.0.0", "@solid-primitives/storage": "^1.0.0", "@xenova/transformers": "^2.0.0", "solid-icons": "^1.0.0", "decimal.js": "^10.4.3", "date-fns": "^3.0.0"}, "devDependencies": {"vite": "^5.0.0", "vite-plugin-solid": "^2.8.0", "@pandacss/vite-plugin": "^0.39.0", "rollup-plugin-visualizer": "^5.12.0", "typescript": "^5.0.0", "@biomejs/biome": "^1.7.0", "@types/node": "^20.0.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["quantitative-trading", "solidjs", "lightweight", "financial", "charts", "ai-assisted"], "author": "Quant Frontend Team", "license": "MIT"}