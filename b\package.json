{"name": "quant-frontend", "version": "1.0.0", "description": "轻量级量化交易前端平台", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "echo '<PERSON><PERSON> skipped'", "format": "echo 'Formatting skipped'", "type-check": "tsc --noEmit"}, "dependencies": {"solid-js": "^1.8.0", "@solidjs/router": "^0.13.0", "decimal.js": "^10.4.3", "date-fns": "^3.0.0", "lightweight-charts": "^4.1.0", "@monaco-editor/loader": "^1.4.0", "monaco-editor": "^0.45.0", "jotai": "^2.6.0", "@pandacss/dev": "^0.39.0", "@park-ui/panda-preset": "^0.32.0", "socket.io-client": "^4.7.0", "numeral": "^2.0.6", "big.js": "^6.2.1", "uuid": "^9.0.1"}, "devDependencies": {"vite": "^5.0.0", "vite-plugin-solid": "^2.8.0", "typescript": "^5.0.0", "@types/node": "^20.0.0", "@types/numeral": "^2.0.5", "@types/big.js": "^6.2.2", "@types/uuid": "^9.0.7", "postcss": "^8.4.0", "autoprefixer": "^10.4.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["quantitative-trading", "solidjs", "lightweight", "financial", "charts", "ai-assisted"], "author": "Quant Frontend Team", "license": "MIT"}