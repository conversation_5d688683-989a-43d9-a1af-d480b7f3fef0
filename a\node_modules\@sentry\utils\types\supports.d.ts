export { supportsHist<PERSON> } from './vendor/supportsHistory';
/**
 * Tells whether current environment supports ErrorEvent objects
 * {@link supportsErrorEvent}.
 *
 * @returns Answer to the given question.
 */
export declare function supportsErrorEvent(): boolean;
/**
 * Tells whether current environment supports DOMError objects
 * {@link supportsDOMError}.
 *
 * @returns Answer to the given question.
 */
export declare function supportsDOMError(): boolean;
/**
 * Tells whether current environment supports DOMException objects
 * {@link supportsDOMException}.
 *
 * @returns Answer to the given question.
 */
export declare function supportsDOMException(): boolean;
/**
 * Tells whether current environment supports Fetch API
 * {@link supportsFetch}.
 *
 * @returns Answer to the given question.
 */
export declare function supportsFetch(): boolean;
/**
 * isNativeFetch checks if the given function is a native implementation of fetch()
 */
export declare function isNativeFetch(func: Function): boolean;
/**
 * Tells whether current environment supports Fetch API natively
 * {@link supportsNativeFetch}.
 *
 * @returns true if `window.fetch` is natively implemented, false otherwise
 */
export declare function supportsNativeFetch(): boolean;
/**
 * Tells whether current environment supports ReportingObserver API
 * {@link supportsReportingObserver}.
 *
 * @returns Answer to the given question.
 */
export declare function supportsReportingObserver(): boolean;
/**
 * Tells whether current environment supports Referrer Policy API
 * {@link supportsReferrerPolicy}.
 *
 * @returns Answer to the given question.
 */
export declare function supportsReferrerPolicy(): boolean;
//# sourceMappingURL=supports.d.ts.map