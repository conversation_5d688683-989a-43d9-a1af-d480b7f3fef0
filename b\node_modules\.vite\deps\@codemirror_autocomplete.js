import {
  CompletionContext,
  acceptCompletion,
  autocompletion,
  clearSnippet,
  closeBrackets,
  closeBracketsKeymap,
  closeCompletion,
  completeAnyWord,
  completeFromList,
  completionKeymap,
  completionStatus,
  currentCompletions,
  deleteBracketPair,
  hasNextSnippetField,
  hasPrevSnippetField,
  ifIn,
  ifNotIn,
  insertBracket,
  insertCompletionText,
  moveCompletionSelection,
  nextSnippetField,
  pickedCompletion,
  prevSnippetField,
  selectedCompletion,
  selectedCompletionIndex,
  setSelectedCompletion,
  snippet,
  snippetCompletion,
  snippetKeymap,
  startCompletion
} from "./chunk-5D2PLMOT.js";
import "./chunk-WMETQKA6.js";
import "./chunk-LNCBHTYT.js";
import "./chunk-5BGWBVN3.js";
import "./chunk-JEVQZFNC.js";
import "./chunk-WVUCPEO5.js";
export {
  CompletionContext,
  acceptCompletion,
  autocompletion,
  clearSnippet,
  closeBrackets,
  closeBracketsKeymap,
  closeCompletion,
  completeAnyWord,
  completeFromList,
  completionKeymap,
  completionStatus,
  currentCompletions,
  deleteBracketPair,
  hasNextSnippetField,
  hasPrevSnippetField,
  ifIn,
  ifNotIn,
  insertBracket,
  insertCompletionText,
  moveCompletionSelection,
  nextSnippetField,
  pickedCompletion,
  prevSnippetField,
  selectedCompletion,
  selectedCompletionIndex,
  setSelectedCompletion,
  snippet,
  snippetCompletion,
  snippetKeymap,
  startCompletion
};
//# sourceMappingURL=@codemirror_autocomplete.js.map
