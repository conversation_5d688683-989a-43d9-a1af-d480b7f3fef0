import {
  CompletionContext,
  acceptCompletion,
  autocompletion,
  clearSnippet,
  closeBrackets,
  closeBracketsKeymap,
  closeCompletion,
  completeAnyWord,
  completeFromList,
  completionKeymap,
  completionStatus,
  currentCompletions,
  deleteBracketPair,
  hasNextSnippetField,
  hasPrevSnippetField,
  ifIn,
  ifNotIn,
  insertBracket,
  insertCompletionText,
  moveCompletionSelection,
  nextSnippetField,
  pickedCompletion,
  prevSnippetField,
  selectedCompletion,
  selectedCompletionIndex,
  setSelectedCompletion,
  snippet,
  snippetCompletion,
  snippetKeymap,
  startCompletion
} from "./chunk-SQTTRNUG.js";
import "./chunk-NT7MLCKP.js";
import "./chunk-2FMXLZOA.js";
import "./chunk-JEVQZFNC.js";
import "./chunk-DC5AMYBS.js";
export {
  CompletionContext,
  acceptCompletion,
  autocompletion,
  clearSnippet,
  closeBrackets,
  closeBracketsKeymap,
  closeCompletion,
  completeAnyWord,
  completeFromList,
  completionKeymap,
  completionStatus,
  currentCompletions,
  deleteBracketPair,
  hasNextSnippetField,
  hasPrevSnippetField,
  ifIn,
  ifNotIn,
  insertBracket,
  insertCompletionText,
  moveCompletionSelection,
  nextSnippetField,
  pickedCompletion,
  prevSnippetField,
  selectedCompletion,
  selectedCompletionIndex,
  setSelectedCompletion,
  snippet,
  snippetCompletion,
  snippetKeymap,
  startCompletion
};
//# sourceMappingURL=@codemirror_autocomplete.js.map
