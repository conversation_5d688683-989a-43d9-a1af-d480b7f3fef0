/**
 * WebSocket连接管理
 */
import { ENV_CONFIG, WS_PATHS } from './constants'

// WebSocket消息类型
export interface WSMessage {
  type: string
  data: any
  timestamp?: number
  id?: string
}

// WebSocket事件类型
export type WSEventType = 'open' | 'close' | 'error' | 'message' | 'reconnect'

// WebSocket事件监听器
export type WSEventListener = (event: any) => void

// WebSocket配置
export interface WSConfig {
  url: string
  protocols?: string[]
  reconnect?: boolean
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeatInterval?: number
  heartbeatMessage?: string
}

// 连接状态
export type WSConnectionState = 'connecting' | 'connected' | 'disconnected' | 'reconnecting' | 'error'

/**
 * WebSocket连接管理器
 */
export class WebSocketManager {
  private ws: WebSocket | null = null
  private config: WSConfig
  private listeners: Map<WSEventType, Set<WSEventListener>> = new Map()
  private reconnectTimer: number | null = null
  private heartbeatTimer: number | null = null
  private reconnectAttempts = 0
  private state: WSConnectionState = 'disconnected'
  private messageQueue: WSMessage[] = []

  constructor(config: WSConfig) {
    this.config = {
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000,
      heartbeatMessage: JSON.stringify({ type: 'ping' }),
      ...config
    }

    // 初始化事件监听器映射
    const eventTypes: WSEventType[] = ['open', 'close', 'error', 'message', 'reconnect']
    eventTypes.forEach(type => {
      this.listeners.set(type, new Set())
    })
  }

  /**
   * 连接WebSocket
   */
  connect(): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return
    }

    this.state = 'connecting'
    this.emit('connecting', { state: this.state })

    try {
      this.ws = new WebSocket(this.config.url, this.config.protocols)
      this.setupEventHandlers()
    } catch (error) {
      this.state = 'error'
      this.emit('error', error)
      this.handleReconnect()
    }
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.config.reconnect = false
    this.clearTimers()
    
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    
    this.state = 'disconnected'
    this.emit('close', { code: 1000, reason: 'Manual disconnect' })
  }

  /**
   * 发送消息
   */
  send(message: WSMessage | string): void {
    const msg = typeof message === 'string' ? message : JSON.stringify(message)

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(msg)
    } else {
      // 连接未建立时，将消息加入队列
      if (typeof message !== 'string') {
        this.messageQueue.push(message)
      }
      
      // 尝试重新连接
      if (this.state === 'disconnected') {
        this.connect()
      }
    }
  }

  /**
   * 添加事件监听器
   */
  on(event: WSEventType, listener: WSEventListener): void {
    const listeners = this.listeners.get(event)
    if (listeners) {
      listeners.add(listener)
    }
  }

  /**
   * 移除事件监听器
   */
  off(event: WSEventType, listener: WSEventListener): void {
    const listeners = this.listeners.get(event)
    if (listeners) {
      listeners.delete(listener)
    }
  }

  /**
   * 获取连接状态
   */
  getState(): WSConnectionState {
    return this.state
  }

  /**
   * 获取WebSocket实例
   */
  getWebSocket(): WebSocket | null {
    return this.ws
  }

  /**
   * 设置WebSocket事件处理器
   */
  private setupEventHandlers(): void {
    if (!this.ws) return

    this.ws.onopen = (event) => {
      this.state = 'connected'
      this.reconnectAttempts = 0
      this.emit('open', event)
      
      // 发送队列中的消息
      this.flushMessageQueue()
      
      // 启动心跳
      this.startHeartbeat()
    }

    this.ws.onclose = (event) => {
      this.state = 'disconnected'
      this.clearTimers()
      this.emit('close', event)
      
      // 自动重连
      if (this.config.reconnect && event.code !== 1000) {
        this.handleReconnect()
      }
    }

    this.ws.onerror = (event) => {
      this.state = 'error'
      this.emit('error', event)
    }

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.emit('message', data)
      } catch (error) {
        // 如果不是JSON格式，直接传递原始数据
        this.emit('message', { type: 'raw', data: event.data })
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(event: WSEventType, data: any): void {
    const listeners = this.listeners.get(event)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`WebSocket事件监听器错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 处理重连
   */
  private handleReconnect(): void {
    if (!this.config.reconnect || 
        this.reconnectAttempts >= (this.config.maxReconnectAttempts || 5)) {
      return
    }

    this.state = 'reconnecting'
    this.reconnectAttempts++
    
    this.emit('reconnect', {
      attempt: this.reconnectAttempts,
      maxAttempts: this.config.maxReconnectAttempts
    })

    this.reconnectTimer = window.setTimeout(() => {
      this.connect()
    }, this.config.reconnectInterval)
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    if (!this.config.heartbeatInterval || !this.config.heartbeatMessage) {
      return
    }

    this.heartbeatTimer = window.setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(this.config.heartbeatMessage!)
      }
    }, this.config.heartbeatInterval)
  }

  /**
   * 清除定时器
   */
  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 发送队列中的消息
   */
  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      if (message) {
        this.send(message)
      }
    }
  }
}

/**
 * 市场数据WebSocket连接
 */
export class MarketWebSocket extends WebSocketManager {
  constructor() {
    const wsUrl = ENV_CONFIG.wsUrl + WS_PATHS.MARKET
    super({
      url: wsUrl,
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000
    })
  }

  /**
   * 订阅股票行情
   */
  subscribeQuote(symbols: string[]): void {
    this.send({
      type: 'subscribe',
      data: {
        channel: 'quote',
        symbols
      }
    })
  }

  /**
   * 取消订阅股票行情
   */
  unsubscribeQuote(symbols: string[]): void {
    this.send({
      type: 'unsubscribe',
      data: {
        channel: 'quote',
        symbols
      }
    })
  }

  /**
   * 订阅K线数据
   */
  subscribeKLine(symbol: string, period: string): void {
    this.send({
      type: 'subscribe',
      data: {
        channel: 'kline',
        symbol,
        period
      }
    })
  }

  /**
   * 订阅市场深度
   */
  subscribeDepth(symbol: string): void {
    this.send({
      type: 'subscribe',
      data: {
        channel: 'depth',
        symbol
      }
    })
  }
}

/**
 * 交易WebSocket连接
 */
export class TradingWebSocket extends WebSocketManager {
  constructor() {
    const wsUrl = ENV_CONFIG.wsUrl + WS_PATHS.TRADING
    super({
      url: wsUrl,
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000
    })
  }

  /**
   * 订阅账户信息
   */
  subscribeAccount(): void {
    this.send({
      type: 'subscribe',
      data: {
        channel: 'account'
      }
    })
  }

  /**
   * 订阅订单更新
   */
  subscribeOrders(): void {
    this.send({
      type: 'subscribe',
      data: {
        channel: 'orders'
      }
    })
  }

  /**
   * 订阅持仓更新
   */
  subscribePositions(): void {
    this.send({
      type: 'subscribe',
      data: {
        channel: 'positions'
      }
    })
  }
}

/**
 * 策略WebSocket连接
 */
export class StrategyWebSocket extends WebSocketManager {
  constructor() {
    const wsUrl = ENV_CONFIG.wsUrl + WS_PATHS.STRATEGY
    super({
      url: wsUrl,
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000
    })
  }

  /**
   * 订阅策略状态
   */
  subscribeStrategyStatus(strategyId: string): void {
    this.send({
      type: 'subscribe',
      data: {
        channel: 'strategy_status',
        strategyId
      }
    })
  }

  /**
   * 订阅策略信号
   */
  subscribeStrategySignals(strategyId: string): void {
    this.send({
      type: 'subscribe',
      data: {
        channel: 'strategy_signals',
        strategyId
      }
    })
  }

  /**
   * 订阅回测进度
   */
  subscribeBacktestProgress(backtestId: string): void {
    this.send({
      type: 'subscribe',
      data: {
        channel: 'backtest_progress',
        backtestId
      }
    })
  }
}

// 创建全局实例
export const marketWS = new MarketWebSocket()
export const tradingWS = new TradingWebSocket()
export const strategyWS = new StrategyWebSocket()

// 导出默认实例
export { marketWS as default }
