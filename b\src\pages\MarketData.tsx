import { useAtomValue } from 'jotai';
import { For } from 'solid-js';
import { topMoversAtom, watchlistAtom, tickersAtom } from '@/stores/market';
import RealTimeTicker from '@/components/RealTimeTicker';
import FinancialChart from '@/components/FinancialChart';
import { css } from '../../styled-system/css';
import { FiTrendingUp, FiTrendingDown, FiActivity } from 'solid-icons/fi';

export default function MarketData() {
  const topMovers = useAtomValue(topMoversAtom);
  const watchlist = useAtomValue(watchlistAtom);
  const tickers = useAtomValue(tickersAtom);
  
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
  };
  
  const formatPercent = (percent: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(percent / 100);
  };
  
  const formatVolume = (volume: number) => {
    if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(1)}M`;
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(1)}K`;
    }
    return volume.toString();
  };
  
  return (
    <div class={css({
      w: 'full',
      maxW: '7xl',
      mx: 'auto',
      px: 4,
      py: 6
    })}>
      {/* 页面标题 */}
      <div class={css({
        mb: 8
      })}>
        <h1 class={css({
          fontSize: '3xl',
          fontWeight: 'bold',
          color: 'gray.900',
          mb: 2,
          _dark: { color: 'gray.100' }
        })}>
          市场数据
        </h1>
        <p class={css({
          fontSize: 'lg',
          color: 'gray.600',
          _dark: { color: 'gray.400' }
        })}>
          实时监控全球金融市场动态
        </p>
      </div>
      
      {/* 实时行情 */}
      <RealTimeTicker />
      
      {/* 主要内容 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: { base: '1fr', xl: '2fr 1fr' },
        gap: 6
      })}>
        {/* 图表区域 */}
        <div>
          <FinancialChart height={500} showVolume={true} />
        </div>
        
        {/* 数据面板 */}
        <div class={css({
          display: 'flex',
          flexDirection: 'column',
          gap: 6
        })}>
          {/* 监控列表 */}
          <div class={css({
            p: 6,
            bg: 'white',
            rounded: 'xl',
            border: '1px solid',
            borderColor: 'gray.200',
            _dark: {
              bg: 'gray.800',
              borderColor: 'gray.700'
            }
          })}>
            <h3 class={css({
              fontSize: 'lg',
              fontWeight: 'semibold',
              color: 'gray.900',
              mb: 4,
              _dark: { color: 'gray.100' }
            })}>
              监控列表
            </h3>
            
            <div class={css({
              space: 'y-2'
            })}>
              <For each={watchlist}>
                {(symbol) => {
                  const ticker = () => tickers[symbol];
                  
                  return (
                    <div class={css({
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      p: 3,
                      bg: 'gray.50',
                      rounded: 'lg',
                      transition: 'all 0.2s ease',
                      _hover: {
                        bg: 'gray.100'
                      },
                      _dark: {
                        bg: 'gray.700',
                        _hover: {
                          bg: 'gray.600'
                        }
                      }
                    })}>
                      <div>
                        <div class={css({
                          fontWeight: 'medium',
                          fontSize: 'sm',
                          color: 'gray.900',
                          _dark: { color: 'gray.100' }
                        })}>
                          {symbol}
                        </div>
                        {ticker() && (
                          <div class={css({
                            fontSize: 'xs',
                            color: 'gray.600',
                            fontFamily: 'mono',
                            _dark: { color: 'gray.400' }
                          })}>
                            ${formatPrice(ticker().price)}
                          </div>
                        )}
                      </div>
                      
                      {ticker() && (
                        <div class={css({
                          textAlign: 'right'
                        })}>
                          <div class={css({
                            fontSize: 'xs',
                            fontWeight: 'medium',
                            color: ticker().changePercent >= 0 ? 'success.600' : 'danger.600'
                          })}>
                            {ticker().changePercent >= 0 ? '+' : ''}
                            {formatPercent(ticker().changePercent)}
                          </div>
                          <div class={css({
                            fontSize: 'xs',
                            color: 'gray.500',
                            _dark: { color: 'gray.400' }
                          })}>
                            {formatVolume(ticker().volume)}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                }}
              </For>
            </div>
          </div>
          
          {/* 市场分析 */}
          <div class={css({
            p: 6,
            bg: 'white',
            rounded: 'xl',
            border: '1px solid',
            borderColor: 'gray.200',
            _dark: {
              bg: 'gray.800',
              borderColor: 'gray.700'
            }
          })}>
            <h3 class={css({
              fontSize: 'lg',
              fontWeight: 'semibold',
              color: 'gray.900',
              mb: 4,
              _dark: { color: 'gray.100' }
            })}>
              市场分析
            </h3>
            
            <div class={css({
              space: 'y-4'
            })}>
              <div class={css({
                p: 4,
                bg: 'success.50',
                rounded: 'lg',
                border: '1px solid',
                borderColor: 'success.200',
                _dark: {
                  bg: 'success.900/20',
                  borderColor: 'success.800'
                }
              })}>
                <div class={css({
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  mb: 2
                })}>
                  <FiTrendingUp size={16} class={css({ color: 'success.600' })} />
                  <span class={css({
                    fontSize: 'sm',
                    fontWeight: 'medium',
                    color: 'success.800',
                    _dark: { color: 'success.300' }
                  })}>
                    市场情绪积极
                  </span>
                </div>
                <p class={css({
                  fontSize: 'xs',
                  color: 'success.700',
                  lineHeight: 1.4,
                  _dark: { color: 'success.400' }
                })}>
                  大部分资产呈现上涨趋势，投资者信心较强，建议关注科技股和成长股机会。
                </p>
              </div>
              
              <div class={css({
                p: 4,
                bg: 'warning.50',
                rounded: 'lg',
                border: '1px solid',
                borderColor: 'warning.200',
                _dark: {
                  bg: 'warning.900/20',
                  borderColor: 'warning.800'
                }
              })}>
                <div class={css({
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  mb: 2
                })}>
                  <FiActivity size={16} class={css({ color: 'warning.600' })} />
                  <span class={css({
                    fontSize: 'sm',
                    fontWeight: 'medium',
                    color: 'warning.800',
                    _dark: { color: 'warning.300' }
                  })}>
                    波动性增加
                  </span>
                </div>
                <p class={css({
                  fontSize: 'xs',
                  color: 'warning.700',
                  lineHeight: 1.4,
                  _dark: { color: 'warning.400' }
                })}>
                  近期市场波动性有所增加，建议适当控制仓位，关注风险管理。
                </p>
              </div>
              
              <div class={css({
                p: 4,
                bg: 'primary.50',
                rounded: 'lg',
                border: '1px solid',
                borderColor: 'primary.200',
                _dark: {
                  bg: 'primary.900/20',
                  borderColor: 'primary.800'
                }
              })}>
                <div class={css({
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  mb: 2
                })}>
                  <FiActivity size={16} class={css({ color: 'primary.600' })} />
                  <span class={css({
                    fontSize: 'sm',
                    fontWeight: 'medium',
                    color: 'primary.800',
                    _dark: { color: 'primary.300' }
                  })}>
                    AI建议
                  </span>
                </div>
                <p class={css({
                  fontSize: 'xs',
                  color: 'primary.700',
                  lineHeight: 1.4,
                  _dark: { color: 'primary.400' }
                })}>
                  基于当前市场数据分析，建议采用动量策略，关注突破性机会。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
