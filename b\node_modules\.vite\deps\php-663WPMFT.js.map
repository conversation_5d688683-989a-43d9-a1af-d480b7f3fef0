{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/php/php.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/php/php.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*(#|//)region\\\\b\"),\n      end: new RegExp(\"^\\\\s*(#|//)endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  tokenizer: {\n    root: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.root\" }],\n      [/<!DOCTYPE/, \"metatag.html\", \"@doctype\"],\n      [/<!--/, \"comment.html\", \"@comment\"],\n      [/(<)(\\w+)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<)(script)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@script\" }]],\n      [/(<)(style)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@style\" }]],\n      [/(<)([:\\w]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)(\\w+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/[^<]+/]\n    ],\n    doctype: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.comment\" }],\n      [/[^>]+/, \"metatag.content.html\"],\n      [/>/, \"metatag.html\", \"@pop\"]\n    ],\n    comment: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.comment\" }],\n      [/-->/, \"comment.html\", \"@pop\"],\n      [/[^-]+/, \"comment.content.html\"],\n      [/./, \"comment.content.html\"]\n    ],\n    otherTag: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.otherTag\" }],\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/]\n    ],\n    script: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.script\" }],\n      [/type/, \"attribute.name\", \"@scriptAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      [\n        /(<\\/)(script\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    scriptAfterType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.scriptAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@scriptAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptAfterTypeEquals: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.scriptAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptWithCustomType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.scriptWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptEmbedded: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInEmbeddedState.scriptEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/script/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    style: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.style\" }],\n      [/type/, \"attribute.name\", \"@styleAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      [\n        /(<\\/)(style\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    styleAfterType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.styleAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@styleAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleAfterTypeEquals: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.styleAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleWithCustomType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.styleWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleEmbedded: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInEmbeddedState.styleEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/style/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    phpInSimpleState: [\n      [/<\\?((php)|=)?/, \"metatag.php\"],\n      [/\\?>/, { token: \"metatag.php\", switchTo: \"@$S2.$S3\" }],\n      { include: \"phpRoot\" }\n    ],\n    phpInEmbeddedState: [\n      [/<\\?((php)|=)?/, \"metatag.php\"],\n      [\n        /\\?>/,\n        {\n          token: \"metatag.php\",\n          switchTo: \"@$S2.$S3\",\n          nextEmbedded: \"$S3\"\n        }\n      ],\n      { include: \"phpRoot\" }\n    ],\n    phpRoot: [\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@phpKeywords\": { token: \"keyword.php\" },\n            \"@phpCompileTimeConstants\": { token: \"constant.php\" },\n            \"@default\": \"identifier.php\"\n          }\n        }\n      ],\n      [\n        /[$a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@phpPreDefinedVariables\": {\n              token: \"variable.predefined.php\"\n            },\n            \"@default\": \"variable.php\"\n          }\n        }\n      ],\n      [/[{}]/, \"delimiter.bracket.php\"],\n      [/[\\[\\]]/, \"delimiter.array.php\"],\n      [/[()]/, \"delimiter.parenthesis.php\"],\n      [/[ \\t\\r\\n]+/],\n      [/(#|\\/\\/)$/, \"comment.php\"],\n      [/(#|\\/\\/)/, \"comment.php\", \"@phpLineComment\"],\n      [/\\/\\*/, \"comment.php\", \"@phpComment\"],\n      [/\"/, \"string.php\", \"@phpDoubleQuoteString\"],\n      [/'/, \"string.php\", \"@phpSingleQuoteString\"],\n      [/[\\+\\-\\*\\%\\&\\|\\^\\~\\!\\=\\<\\>\\/\\?\\;\\:\\.\\,\\@]/, \"delimiter.php\"],\n      [/\\d*\\d+[eE]([\\-+]?\\d+)?/, \"number.float.php\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float.php\"],\n      [/0[xX][0-9a-fA-F']*[0-9a-fA-F]/, \"number.hex.php\"],\n      [/0[0-7']*[0-7]/, \"number.octal.php\"],\n      [/0[bB][0-1']*[0-1]/, \"number.binary.php\"],\n      [/\\d[\\d']*/, \"number.php\"],\n      [/\\d/, \"number.php\"]\n    ],\n    phpComment: [\n      [/\\*\\//, \"comment.php\", \"@pop\"],\n      [/[^*]+/, \"comment.php\"],\n      [/./, \"comment.php\"]\n    ],\n    phpLineComment: [\n      [/\\?>/, { token: \"@rematch\", next: \"@pop\" }],\n      [/.$/, \"comment.php\", \"@pop\"],\n      [/[^?]+$/, \"comment.php\", \"@pop\"],\n      [/[^?]+/, \"comment.php\"],\n      [/./, \"comment.php\"]\n    ],\n    phpDoubleQuoteString: [\n      [/[^\\\\\"]+/, \"string.php\"],\n      [/@escapes/, \"string.escape.php\"],\n      [/\\\\./, \"string.escape.invalid.php\"],\n      [/\"/, \"string.php\", \"@pop\"]\n    ],\n    phpSingleQuoteString: [\n      [/[^\\\\']+/, \"string.php\"],\n      [/@escapes/, \"string.escape.php\"],\n      [/\\\\./, \"string.escape.invalid.php\"],\n      [/'/, \"string.php\", \"@pop\"]\n    ]\n  },\n  phpKeywords: [\n    \"abstract\",\n    \"and\",\n    \"array\",\n    \"as\",\n    \"break\",\n    \"callable\",\n    \"case\",\n    \"catch\",\n    \"cfunction\",\n    \"class\",\n    \"clone\",\n    \"const\",\n    \"continue\",\n    \"declare\",\n    \"default\",\n    \"do\",\n    \"else\",\n    \"elseif\",\n    \"enddeclare\",\n    \"endfor\",\n    \"endforeach\",\n    \"endif\",\n    \"endswitch\",\n    \"endwhile\",\n    \"extends\",\n    \"false\",\n    \"final\",\n    \"for\",\n    \"foreach\",\n    \"function\",\n    \"global\",\n    \"goto\",\n    \"if\",\n    \"implements\",\n    \"interface\",\n    \"instanceof\",\n    \"insteadof\",\n    \"namespace\",\n    \"new\",\n    \"null\",\n    \"object\",\n    \"old_function\",\n    \"or\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"resource\",\n    \"static\",\n    \"switch\",\n    \"throw\",\n    \"trait\",\n    \"try\",\n    \"true\",\n    \"use\",\n    \"var\",\n    \"while\",\n    \"xor\",\n    \"die\",\n    \"echo\",\n    \"empty\",\n    \"exit\",\n    \"eval\",\n    \"include\",\n    \"include_once\",\n    \"isset\",\n    \"list\",\n    \"require\",\n    \"require_once\",\n    \"return\",\n    \"print\",\n    \"unset\",\n    \"yield\",\n    \"__construct\"\n  ],\n  phpCompileTimeConstants: [\n    \"__CLASS__\",\n    \"__DIR__\",\n    \"__FILE__\",\n    \"__LINE__\",\n    \"__NAMESPACE__\",\n    \"__METHOD__\",\n    \"__FUNCTION__\",\n    \"__TRAIT__\"\n  ],\n  phpPreDefinedVariables: [\n    \"$GLOBALS\",\n    \"$_SERVER\",\n    \"$_GET\",\n    \"$_POST\",\n    \"$_FILES\",\n    \"$_REQUEST\",\n    \"$_SESSION\",\n    \"$_ENV\",\n    \"$_COOKIE\",\n    \"$php_errormsg\",\n    \"$HTTP_RAW_POST_DATA\",\n    \"$http_response_header\",\n    \"$argc\",\n    \"$argv\"\n  ],\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,aAAa;AAAA,EACb,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EACxD;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,sBAAsB;AAAA,MACxC,KAAK,IAAI,OAAO,yBAAyB;AAAA,IAC3C;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,MAC3E,CAAC,aAAa,gBAAgB,UAAU;AAAA,MACxC,CAAC,QAAQ,gBAAgB,UAAU;AAAA,MACnC,CAAC,iBAAiB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,MAClE,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,UAAU,CAAC,CAAC;AAAA,MAC1E,CAAC,cAAc,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,SAAS,CAAC,CAAC;AAAA,MACxE,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC5E,CAAC,cAAc,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC3E,CAAC,KAAK,gBAAgB;AAAA,MACtB,CAAC,OAAO;AAAA,IACV;AAAA,IACA,SAAS;AAAA,MACP,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,4BAA4B,CAAC;AAAA,MAC9E,CAAC,SAAS,sBAAsB;AAAA,MAChC,CAAC,KAAK,gBAAgB,MAAM;AAAA,IAC9B;AAAA,IACA,SAAS;AAAA,MACP,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,4BAA4B,CAAC;AAAA,MAC9E,CAAC,OAAO,gBAAgB,MAAM;AAAA,MAC9B,CAAC,SAAS,sBAAsB;AAAA,MAChC,CAAC,KAAK,sBAAsB;AAAA,IAC9B;AAAA,IACA,UAAU;AAAA,MACR,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,6BAA6B,CAAC;AAAA,MAC/E,CAAC,QAAQ,kBAAkB,MAAM;AAAA,MACjC,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA,IACf;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,2BAA2B,CAAC;AAAA,MAC7E,CAAC,QAAQ,kBAAkB,kBAAkB;AAAA,MAC7C,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY;AAAA,MACb;AAAA,QACE;AAAA,QACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,MAC1E;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA,CAAC,KAAK,aAAa,wBAAwB;AAAA,MAC3C;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACvD;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACvD;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACvD;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,aAAa,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,IACzE;AAAA,IACA,OAAO;AAAA,MACL,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,0BAA0B,CAAC;AAAA,MAC5E,CAAC,QAAQ,kBAAkB,iBAAiB;AAAA,MAC5C,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY;AAAA,MACb;AAAA,QACE;AAAA,QACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,MAC1E;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA,CAAC,KAAK,aAAa,uBAAuB;AAAA,MAC1C;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACtD;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACtD;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACtD;AAAA,IACA,eAAe;AAAA,MACb;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,IACxE;AAAA,IACA,kBAAkB;AAAA,MAChB,CAAC,iBAAiB,aAAa;AAAA,MAC/B,CAAC,OAAO,EAAE,OAAO,eAAe,UAAU,WAAW,CAAC;AAAA,MACtD,EAAE,SAAS,UAAU;AAAA,IACvB;AAAA,IACA,oBAAoB;AAAA,MAClB,CAAC,iBAAiB,aAAa;AAAA,MAC/B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,EAAE,SAAS,UAAU;AAAA,IACvB;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,gBAAgB,EAAE,OAAO,cAAc;AAAA,YACvC,4BAA4B,EAAE,OAAO,eAAe;AAAA,YACpD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,2BAA2B;AAAA,cACzB,OAAO;AAAA,YACT;AAAA,YACA,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,QAAQ,uBAAuB;AAAA,MAChC,CAAC,UAAU,qBAAqB;AAAA,MAChC,CAAC,QAAQ,2BAA2B;AAAA,MACpC,CAAC,YAAY;AAAA,MACb,CAAC,aAAa,aAAa;AAAA,MAC3B,CAAC,YAAY,eAAe,iBAAiB;AAAA,MAC7C,CAAC,QAAQ,eAAe,aAAa;AAAA,MACrC,CAAC,KAAK,cAAc,uBAAuB;AAAA,MAC3C,CAAC,KAAK,cAAc,uBAAuB;AAAA,MAC3C,CAAC,4CAA4C,eAAe;AAAA,MAC5D,CAAC,0BAA0B,kBAAkB;AAAA,MAC7C,CAAC,4BAA4B,kBAAkB;AAAA,MAC/C,CAAC,iCAAiC,gBAAgB;AAAA,MAClD,CAAC,iBAAiB,kBAAkB;AAAA,MACpC,CAAC,qBAAqB,mBAAmB;AAAA,MACzC,CAAC,YAAY,YAAY;AAAA,MACzB,CAAC,MAAM,YAAY;AAAA,IACrB;AAAA,IACA,YAAY;AAAA,MACV,CAAC,QAAQ,eAAe,MAAM;AAAA,MAC9B,CAAC,SAAS,aAAa;AAAA,MACvB,CAAC,KAAK,aAAa;AAAA,IACrB;AAAA,IACA,gBAAgB;AAAA,MACd,CAAC,OAAO,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,MAC3C,CAAC,MAAM,eAAe,MAAM;AAAA,MAC5B,CAAC,UAAU,eAAe,MAAM;AAAA,MAChC,CAAC,SAAS,aAAa;AAAA,MACvB,CAAC,KAAK,aAAa;AAAA,IACrB;AAAA,IACA,sBAAsB;AAAA,MACpB,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,YAAY,mBAAmB;AAAA,MAChC,CAAC,OAAO,2BAA2B;AAAA,MACnC,CAAC,KAAK,cAAc,MAAM;AAAA,IAC5B;AAAA,IACA,sBAAsB;AAAA,MACpB,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,YAAY,mBAAmB;AAAA,MAChC,CAAC,OAAO,2BAA2B;AAAA,MACnC,CAAC,KAAK,cAAc,MAAM;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,yBAAyB;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,wBAAwB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AACX;", "names": []}