import { atom } from 'jotai';

// 策略类型定义
export interface Strategy {
  id: string;
  name: string;
  description: string;
  code: string;
  language: 'python' | 'javascript';
  parameters: Record<string, any>;
  createdAt: number;
  updatedAt: number;
  author: string;
  tags: string[];
  isPublic: boolean;
}

export interface BacktestResult {
  id: string;
  strategyId: string;
  startDate: string;
  endDate: string;
  initialCapital: number;
  finalValue: number;
  totalReturn: number;
  annualizedReturn: number;
  maxDrawdown: number;
  sharpeRatio: number;
  winRate: number;
  profitFactor: number;
  totalTrades: number;
  createdAt: number;
  trades: Trade[];
  equity: EquityPoint[];
}

export interface Trade {
  id: string;
  symbol: string;
  side: 'buy' | 'sell';
  quantity: number;
  price: number;
  timestamp: number;
  pnl: number;
}

export interface EquityPoint {
  timestamp: number;
  value: number;
}

export interface StrategyTemplate {
  id: string;
  name: string;
  description: string;
  code: string;
  parameters: Record<string, any>;
  category: string;
}

// 基础原子状态
export const strategiesAtom = atom<Strategy[]>([]);
export const currentStrategyAtom = atom<Strategy | null>(null);
export const backtestResultsAtom = atom<BacktestResult[]>([]);
export const isBacktestingAtom = atom<boolean>(false);

// 策略模板
export const strategyTemplatesAtom = atom<StrategyTemplate[]>([
  {
    id: 'dual-ma',
    name: '双均线策略',
    description: '基于短期和长期移动平均线的经典趋势跟踪策略',
    category: '趋势跟踪',
    parameters: {
      shortPeriod: 5,
      longPeriod: 20,
      symbol: 'AAPL'
    },
    code: `# 双均线策略
def initialize(context):
    context.short_period = 5
    context.long_period = 20
    context.symbol = 'AAPL'

def handle_data(context, data):
    # 获取历史价格数据
    prices = data.history(context.symbol, 'price', context.long_period + 1, '1d')
    
    # 计算移动平均线
    short_ma = prices.tail(context.short_period).mean()
    long_ma = prices.tail(context.long_period).mean()
    
    # 获取当前持仓
    current_position = context.portfolio.positions[context.symbol].amount
    
    # 交易逻辑
    if short_ma > long_ma and current_position <= 0:
        # 金叉买入
        order_target_percent(context.symbol, 1.0)
        log.info(f"买入信号: 短期均线({short_ma:.2f}) > 长期均线({long_ma:.2f})")
    elif short_ma < long_ma and current_position > 0:
        # 死叉卖出
        order_target_percent(context.symbol, 0.0)
        log.info(f"卖出信号: 短期均线({short_ma:.2f}) < 长期均线({long_ma:.2f})")
`
  },
  {
    id: 'rsi-reversal',
    name: 'RSI反转策略',
    description: '基于RSI指标的均值回归策略，在超买超卖区域进行反向操作',
    category: '均值回归',
    parameters: {
      rsiPeriod: 14,
      oversoldLevel: 30,
      overboughtLevel: 70,
      symbol: 'AAPL'
    },
    code: `# RSI反转策略
import talib

def initialize(context):
    context.rsi_period = 14
    context.oversold_level = 30
    context.overbought_level = 70
    context.symbol = 'AAPL'

def handle_data(context, data):
    # 获取历史价格数据
    prices = data.history(context.symbol, 'price', context.rsi_period + 10, '1d')
    
    # 计算RSI
    rsi = talib.RSI(prices.values, timeperiod=context.rsi_period)[-1]
    
    # 获取当前持仓
    current_position = context.portfolio.positions[context.symbol].amount
    
    # 交易逻辑
    if rsi < context.oversold_level and current_position <= 0:
        # RSI超卖，买入
        order_target_percent(context.symbol, 1.0)
        log.info(f"买入信号: RSI({rsi:.2f}) < 超卖线({context.oversold_level})")
    elif rsi > context.overbought_level and current_position > 0:
        # RSI超买，卖出
        order_target_percent(context.symbol, 0.0)
        log.info(f"卖出信号: RSI({rsi:.2f}) > 超买线({context.overbought_level})")
`
  },
  {
    id: 'bollinger-bands',
    name: '布林带策略',
    description: '基于布林带的突破和回归策略',
    category: '技术指标',
    parameters: {
      period: 20,
      stdDev: 2,
      symbol: 'AAPL'
    },
    code: `# 布林带策略
import talib

def initialize(context):
    context.period = 20
    context.std_dev = 2
    context.symbol = 'AAPL'

def handle_data(context, data):
    # 获取历史价格数据
    prices = data.history(context.symbol, 'price', context.period + 5, '1d')
    
    # 计算布林带
    upper, middle, lower = talib.BBANDS(
        prices.values, 
        timeperiod=context.period, 
        nbdevup=context.std_dev, 
        nbdevdn=context.std_dev
    )
    
    current_price = prices.iloc[-1]
    current_upper = upper[-1]
    current_lower = lower[-1]
    
    # 获取当前持仓
    current_position = context.portfolio.positions[context.symbol].amount
    
    # 交易逻辑
    if current_price < current_lower and current_position <= 0:
        # 价格跌破下轨，买入
        order_target_percent(context.symbol, 1.0)
        log.info(f"买入信号: 价格({current_price:.2f}) < 下轨({current_lower:.2f})")
    elif current_price > current_upper and current_position > 0:
        # 价格突破上轨，卖出
        order_target_percent(context.symbol, 0.0)
        log.info(f"卖出信号: 价格({current_price:.2f}) > 上轨({current_upper:.2f})")
`
  }
]);

// 衍生状态
export const currentBacktestResultAtom = atom((get) => {
  const results = get(backtestResultsAtom);
  const currentStrategy = get(currentStrategyAtom);
  
  if (!currentStrategy) return null;
  
  return results
    .filter(result => result.strategyId === currentStrategy.id)
    .sort((a, b) => b.createdAt - a.createdAt)[0] || null;
});

export const strategyStatsAtom = atom((get) => {
  const strategies = get(strategiesAtom);
  const results = get(backtestResultsAtom);
  
  return {
    totalStrategies: strategies.length,
    publicStrategies: strategies.filter(s => s.isPublic).length,
    totalBacktests: results.length,
    avgReturn: results.length > 0 
      ? results.reduce((sum, r) => sum + r.totalReturn, 0) / results.length 
      : 0
  };
});

// 操作函数
export const addStrategyAtom = atom(
  null,
  (get, set, strategy: Omit<Strategy, 'id' | 'createdAt' | 'updatedAt'>) => {
    const strategies = get(strategiesAtom);
    const newStrategy: Strategy = {
      ...strategy,
      id: `strategy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
    set(strategiesAtom, [...strategies, newStrategy]);
    return newStrategy;
  }
);

export const updateStrategyAtom = atom(
  null,
  (get, set, id: string, updates: Partial<Strategy>) => {
    const strategies = get(strategiesAtom);
    set(strategiesAtom, strategies.map(strategy => 
      strategy.id === id 
        ? { ...strategy, ...updates, updatedAt: Date.now() }
        : strategy
    ));
  }
);

export const deleteStrategyAtom = atom(
  null,
  (get, set, id: string) => {
    const strategies = get(strategiesAtom);
    set(strategiesAtom, strategies.filter(strategy => strategy.id !== id));
    
    // 如果删除的是当前策略，清空当前策略
    const currentStrategy = get(currentStrategyAtom);
    if (currentStrategy?.id === id) {
      set(currentStrategyAtom, null);
    }
  }
);

export const addBacktestResultAtom = atom(
  null,
  (get, set, result: Omit<BacktestResult, 'id' | 'createdAt'>) => {
    const results = get(backtestResultsAtom);
    const newResult: BacktestResult = {
      ...result,
      id: `backtest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: Date.now()
    };
    set(backtestResultsAtom, [...results, newResult]);
    return newResult;
  }
);

// 创建策略从模板
export const createStrategyFromTemplateAtom = atom(
  null,
  (get, set, templateId: string, customName?: string) => {
    const templates = get(strategyTemplatesAtom);
    const template = templates.find(t => t.id === templateId);
    
    if (!template) {
      throw new Error(`Template with id ${templateId} not found`);
    }
    
    const newStrategy = {
      name: customName || `${template.name} - ${new Date().toLocaleDateString()}`,
      description: template.description,
      code: template.code,
      language: 'python' as const,
      parameters: { ...template.parameters },
      author: '当前用户',
      tags: [template.category],
      isPublic: false
    };
    
    return set(addStrategyAtom, newStrategy);
  }
);
