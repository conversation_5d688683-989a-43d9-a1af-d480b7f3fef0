import { createSignal } from 'solid-js';
import { A } from '@solidjs/router';
import SimpleSlideVerify from '../components/SimpleSlideVerify';

export default function Login() {
  const [username, setUsername] = createSignal('');
  const [password, setPassword] = createSignal('');
  const [isVerified, setIsVerified] = createSignal(false);
  const [loginMessage, setLoginMessage] = createSignal('');
  const [isLoading, setIsLoading] = createSignal(false);

  const handleVerifySuccess = () => {
    setIsVerified(true);
    setLoginMessage('✅ 滑动验证成功');
  };

  const handleVerifyFail = () => {
    setIsVerified(false);
    setLoginMessage('❌ 滑动验证失败，请重试');
  };

  const handleLogin = async (e: Event) => {
    e.preventDefault();
    
    if (!isVerified()) {
      setLoginMessage('❌ 请先完成滑动验证');
      return;
    }

    if (!username() || !password()) {
      setLoginMessage('❌ 请输入用户名和密码');
      return;
    }

    setIsLoading(true);
    setLoginMessage('🔄 正在登录...');

    // 模拟登录请求
    setTimeout(() => {
      setIsLoading(false);
      if (username() === 'admin' && password() === '123456') {
        setLoginMessage('✅ 登录成功！正在跳转...');
        // 这里可以跳转到仪表盘
        setTimeout(() => {
          window.location.href = '/';
        }, 1000);
      } else {
        setLoginMessage('❌ 用户名或密码错误');
        setIsVerified(false); // 重置验证状态
      }
    }, 1500);
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <div style={{
        background: 'white',
        borderRadius: '12px',
        boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
        padding: '40px',
        width: '100%',
        maxWidth: '400px'
      }}>
        {/* Logo和标题 */}
        <div style={{
          textAlign: 'center',
          marginBottom: '30px'
        }}>
          <div style={{
            width: '60px',
            height: '60px',
            background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 16px',
            color: 'white',
            fontSize: '24px',
            fontWeight: 'bold'
          }}>
            Q
          </div>
          <h1 style={{
            fontSize: '24px',
            fontWeight: 'bold',
            color: '#111827',
            margin: '0 0 8px'
          }}>
            量化交易平台
          </h1>
          <p style={{
            color: '#6b7280',
            margin: '0'
          }}>
            请登录您的账户
          </p>
        </div>

        {/* 登录表单 */}
        <form onSubmit={handleLogin}>
          <div style={{ marginBottom: '20px' }}>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151',
              marginBottom: '6px'
            }}>
              用户名
            </label>
            <input
              type="text"
              value={username()}
              onInput={(e) => setUsername(e.currentTarget.value)}
              placeholder="请输入用户名"
              style={{
                width: '100%',
                padding: '12px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '14px',
                outline: 'none',
                transition: 'border-color 0.2s',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => e.currentTarget.style.borderColor = '#3b82f6'}
              onBlur={(e) => e.currentTarget.style.borderColor = '#d1d5db'}
            />
          </div>

          <div style={{ marginBottom: '20px' }}>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151',
              marginBottom: '6px'
            }}>
              密码
            </label>
            <input
              type="password"
              value={password()}
              onInput={(e) => setPassword(e.currentTarget.value)}
              placeholder="请输入密码"
              style={{
                width: '100%',
                padding: '12px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '14px',
                outline: 'none',
                transition: 'border-color 0.2s',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => e.currentTarget.style.borderColor = '#3b82f6'}
              onBlur={(e) => e.currentTarget.style.borderColor = '#d1d5db'}
            />
          </div>

          {/* 滑动验证 */}
          <div style={{ marginBottom: '20px' }}>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151',
              marginBottom: '6px'
            }}>
              安全验证
            </label>
            <SimpleSlideVerify
              width={320}
              height={120}
              sliderText="向右滑动完成验证"
              onSuccess={handleVerifySuccess}
              onFail={handleVerifyFail}
              onRefresh={() => setIsVerified(false)}
            />
          </div>

          {/* 登录按钮 */}
          <button
            type="submit"
            disabled={isLoading()}
            style={{
              width: '100%',
              padding: '12px',
              background: isLoading() ? '#9ca3af' : '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontSize: '16px',
              fontWeight: '500',
              cursor: isLoading() ? 'not-allowed' : 'pointer',
              transition: 'background-color 0.2s',
              marginBottom: '16px'
            }}
            onMouseEnter={(e) => {
              if (!isLoading()) {
                e.currentTarget.style.backgroundColor = '#2563eb';
              }
            }}
            onMouseLeave={(e) => {
              if (!isLoading()) {
                e.currentTarget.style.backgroundColor = '#3b82f6';
              }
            }}
          >
            {isLoading() ? '登录中...' : '登录'}
          </button>

          {/* 消息提示 */}
          {loginMessage() && (
            <div style={{
              padding: '12px',
              borderRadius: '6px',
              fontSize: '14px',
              textAlign: 'center',
              backgroundColor: loginMessage().includes('成功') ? '#f0f9ff' : 
                             loginMessage().includes('失败') || loginMessage().includes('错误') ? '#fef2f2' : '#f9fafb',
              color: loginMessage().includes('成功') ? '#1e40af' : 
                     loginMessage().includes('失败') || loginMessage().includes('错误') ? '#dc2626' : '#374151',
              border: `1px solid ${loginMessage().includes('成功') ? '#bfdbfe' : 
                                  loginMessage().includes('失败') || loginMessage().includes('错误') ? '#fecaca' : '#e5e7eb'}`,
              marginBottom: '16px'
            }}>
              {loginMessage()}
            </div>
          )}

          {/* 返回链接 */}
          <div style={{ textAlign: 'center' }}>
            <A 
              href="/"
              style={{
                color: '#6b7280',
                fontSize: '14px',
                textDecoration: 'none'
              }}
            >
              ← 返回首页
            </A>
          </div>
        </form>

        {/* 演示提示 */}
        <div style={{
          marginTop: '24px',
          padding: '12px',
          backgroundColor: '#f3f4f6',
          borderRadius: '6px',
          fontSize: '12px',
          color: '#6b7280',
          textAlign: 'center'
        }}>
          <div style={{ fontWeight: '500', marginBottom: '4px' }}>演示账户</div>
          <div>用户名: admin</div>
          <div>密码: 123456</div>
        </div>
      </div>
    </div>
  );
}
