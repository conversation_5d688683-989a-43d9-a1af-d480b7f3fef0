import { createSignal } from 'solid-js';
import { A, useNavigate } from '@solidjs/router';
import SimpleSlideVerify from '../components/SimpleSlideVerify';
import { userStore } from '../stores';

export default function Login() {
  const navigate = useNavigate();
  const [username, setUsername] = createSignal('');
  const [password, setPassword] = createSignal('');
  const [isVerified, setIsVerified] = createSignal(false);
  const [loginMessage, setLoginMessage] = createSignal('');
  const [isLoading, setIsLoading] = createSignal(false);

  const handleVerifySuccess = () => {
    setIsVerified(true);
    setLoginMessage('✅ 滑动验证成功');
  };

  const handleVerifyFail = () => {
    setIsVerified(false);
    setLoginMessage('❌ 滑动验证失败，请重试');
  };

  const handleLogin = async (e: Event) => {
    e.preventDefault();

    if (!isVerified()) {
      setLoginMessage('❌ 请先完成滑动验证');
      return;
    }

    if (!username() || !password()) {
      setLoginMessage('❌ 请输入用户名和密码');
      return;
    }

    setIsLoading(true);
    setLoginMessage('🔄 正在登录...');

    try {
      // 使用状态管理进行登录
      await userStore.login({
        username: username(),
        password: password()
      });

      setLoginMessage('✅ 登录成功！正在跳转...');

      // 跳转到仪表盘
      setTimeout(() => {
        navigate('/');
      }, 1000);

    } catch (error) {
      setLoginMessage('❌ 用户名或密码错误');
      setIsVerified(false); // 重置验证状态
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{
      'min-height': '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      'align-items': 'center',
      'justify-content': 'center',
      padding: '20px'
    }}>
      <div style={{
        background: 'white',
        'border-radius': '12px',
        'box-shadow': '0 20px 40px rgba(0,0,0,0.1)',
        padding: '40px',
        width: '100%',
        'max-width': '400px'
      }}>
        {/* Logo和标题 */}
        <div style={{
          'text-align': 'center',
          'margin-bottom': '30px'
        }}>
          <div style={{
            width: '60px',
            height: '60px',
            background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
            'border-radius': '12px',
            display: 'flex',
            'align-items': 'center',
            'justify-content': 'center',
            margin: '0 auto 16px',
            color: 'white',
            'font-size': '24px',
            'font-weight': 'bold'
          }}>
            Q
          </div>
          <h1 style={{
            'font-size': '24px',
            'font-weight': 'bold',
            color: '#111827',
            margin: '0 0 8px'
          }}>
            量化交易平台
          </h1>
          <p style={{
            color: '#6b7280',
            margin: '0'
          }}>
            请登录您的账户
          </p>
        </div>

        {/* 登录表单 */}
        <form onSubmit={handleLogin}>
          <div style={{ 'margin-bottom': '20px' }}>
            <label style={{
              display: 'block',
              'font-size': '14px',
              'font-weight': '500',
              color: '#374151',
              'margin-bottom': '6px'
            }}>
              用户名
            </label>
            <input
              type="text"
              value={username()}
              onInput={(e) => setUsername(e.currentTarget.value)}
              placeholder="请输入用户名"
              style={{
                width: '100%',
                padding: '12px',
                border: '1px solid #d1d5db',
                'border-radius': '6px',
                'font-size': '14px',
                outline: 'none',
                transition: 'border-color 0.2s',
                'box-sizing': 'border-box'
              }}
              onFocus={(e) => e.currentTarget.style.borderColor = '#3b82f6'}
              onBlur={(e) => e.currentTarget.style.borderColor = '#d1d5db'}
            />
          </div>

          <div style={{ 'margin-bottom': '20px' }}>
            <label style={{
              display: 'block',
              'font-size': '14px',
              'font-weight': '500',
              color: '#374151',
              'margin-bottom': '6px'
            }}>
              密码
            </label>
            <input
              type="password"
              value={password()}
              onInput={(e) => setPassword(e.currentTarget.value)}
              placeholder="请输入密码"
              style={{
                width: '100%',
                padding: '12px',
                border: '1px solid #d1d5db',
                'border-radius': '6px',
                'font-size': '14px',
                outline: 'none',
                transition: 'border-color 0.2s',
                'box-sizing': 'border-box'
              }}
              onFocus={(e) => e.currentTarget.style.borderColor = '#3b82f6'}
              onBlur={(e) => e.currentTarget.style.borderColor = '#d1d5db'}
            />
          </div>

          {/* 滑动验证 */}
          <div style={{ 'margin-bottom': '20px' }}>
            <label style={{
              display: 'block',
              'font-size': '14px',
              'font-weight': '500',
              color: '#374151',
              'margin-bottom': '6px'
            }}>
              安全验证
            </label>
            <SimpleSlideVerify
              width={320}
              height={120}
              sliderText="向右滑动完成验证"
              onSuccess={handleVerifySuccess}
              onFail={handleVerifyFail}
              onRefresh={() => setIsVerified(false)}
            />
          </div>

          {/* 登录按钮 */}
          <button
            type="submit"
            disabled={isLoading()}
            style={{
              width: '100%',
              padding: '12px',
              background: isLoading() ? '#9ca3af' : '#3b82f6',
              color: 'white',
              border: 'none',
              'border-radius': '6px',
              'font-size': '16px',
              'font-weight': '500',
              cursor: isLoading() ? 'not-allowed' : 'pointer',
              transition: 'background-color 0.2s',
              'margin-bottom': '16px'
            }}
            onMouseEnter={(e) => {
              if (!isLoading()) {
                e.currentTarget.style.backgroundColor = '#2563eb';
              }
            }}
            onMouseLeave={(e) => {
              if (!isLoading()) {
                e.currentTarget.style.backgroundColor = '#3b82f6';
              }
            }}
          >
            {isLoading() ? '登录中...' : '登录'}
          </button>

          {/* 消息提示 */}
          {loginMessage() && (
            <div style={{
              padding: '12px',
              'border-radius': '6px',
              'font-size': '14px',
              'text-align': 'center',
              'background-color': loginMessage().includes('成功') ? '#f0f9ff' :
                             loginMessage().includes('失败') || loginMessage().includes('错误') ? '#fef2f2' : '#f9fafb',
              color: loginMessage().includes('成功') ? '#1e40af' :
                     loginMessage().includes('失败') || loginMessage().includes('错误') ? '#dc2626' : '#374151',
              border: `1px solid ${loginMessage().includes('成功') ? '#bfdbfe' :
                                  loginMessage().includes('失败') || loginMessage().includes('错误') ? '#fecaca' : '#e5e7eb'}`,
              'margin-bottom': '16px'
            }}>
              {loginMessage()}
            </div>
          )}

          {/* 返回链接 */}
          <div style={{ 'text-align': 'center' }}>
            <A
              href="/"
              style={{
                color: '#6b7280',
                'font-size': '14px',
                'text-decoration': 'none'
              }}
            >
              ← 返回首页
            </A>
          </div>
        </form>

        {/* 演示提示 */}
        <div style={{
          'margin-top': '24px',
          padding: '12px',
          'background-color': '#f3f4f6',
          'border-radius': '6px',
          'font-size': '12px',
          color: '#6b7280',
          'text-align': 'center'
        }}>
          <div style={{ 'font-weight': '500', 'margin-bottom': '4px' }}>演示账户</div>
          <div>用户名: admin</div>
          <div>密码: 123456</div>
        </div>
      </div>
    </div>
  );
}
