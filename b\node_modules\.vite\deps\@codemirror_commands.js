import {
  blockComment,
  blockUncomment,
  copyLineDown,
  copyLineUp,
  cursorCharBackward,
  cursorCharBackwardLogical,
  cursorCharForward,
  cursorCharForwardLogical,
  cursorCharLeft,
  cursorCharRight,
  cursorDocEnd,
  cursorDocStart,
  cursorGroupBackward,
  cursorGroupForward,
  cursorGroupForwardWin,
  cursorGroupLeft,
  cursorGroupRight,
  cursorLineBoundaryBackward,
  cursorLineBoundaryForward,
  cursorLineBoundaryLeft,
  cursorLineBoundaryRight,
  cursorLineDown,
  cursorLineEnd,
  cursorLineStart,
  cursorLineUp,
  cursorMatchingBracket,
  cursorPageDown,
  cursorPageUp,
  cursorSubwordBackward,
  cursorSubwordForward,
  cursorSyntaxLeft,
  cursorSyntaxRight,
  defaultKeymap,
  deleteCharBackward,
  deleteCharBackwardStrict,
  deleteCharForward,
  deleteGroupBackward,
  deleteGroupForward,
  deleteLine,
  deleteLineBoundaryBackward,
  deleteLineBoundaryForward,
  deleteToLineEnd,
  deleteToLineStart,
  deleteTrailingWhitespace,
  emacsStyleKeymap,
  history,
  historyField,
  historyKeymap,
  indentLess,
  indentMore,
  indentSelection,
  indentWithTab,
  insertBlankLine,
  insertNewline,
  insertNewlineAndIndent,
  insertNewlineKeepIndent,
  insertTab,
  invertedEffects,
  isolateHistory,
  lineComment,
  lineUncomment,
  moveLineDown,
  moveLineUp,
  redo,
  redoDepth,
  redoSelection,
  selectAll,
  selectCharBackward,
  selectCharBackwardLogical,
  selectCharForward,
  selectCharForwardLogical,
  selectCharLeft,
  selectCharRight,
  selectDocEnd,
  selectDocStart,
  selectGroupBackward,
  selectGroupForward,
  selectGroupForwardWin,
  selectGroupLeft,
  selectGroupRight,
  selectLine,
  selectLineBoundaryBackward,
  selectLineBoundaryForward,
  selectLineBoundaryLeft,
  selectLineBoundaryRight,
  selectLineDown,
  selectLineEnd,
  selectLineStart,
  selectLineUp,
  selectMatchingBracket,
  selectPageDown,
  selectPageUp,
  selectParentSyntax,
  selectSubwordBackward,
  selectSubwordForward,
  selectSyntaxLeft,
  selectSyntaxRight,
  simplifySelection,
  splitLine,
  standardKeymap,
  temporarilySetTabFocusMode,
  toggleBlockComment,
  toggleBlockCommentByLine,
  toggleComment,
  toggleLineComment,
  toggleTabFocusMode,
  transposeChars,
  undo,
  undoDepth,
  undoSelection
} from "./chunk-XJBDELHZ.js";
import "./chunk-NT7MLCKP.js";
import "./chunk-2FMXLZOA.js";
import "./chunk-JEVQZFNC.js";
import "./chunk-DC5AMYBS.js";
export {
  blockComment,
  blockUncomment,
  copyLineDown,
  copyLineUp,
  cursorCharBackward,
  cursorCharBackwardLogical,
  cursorCharForward,
  cursorCharForwardLogical,
  cursorCharLeft,
  cursorCharRight,
  cursorDocEnd,
  cursorDocStart,
  cursorGroupBackward,
  cursorGroupForward,
  cursorGroupForwardWin,
  cursorGroupLeft,
  cursorGroupRight,
  cursorLineBoundaryBackward,
  cursorLineBoundaryForward,
  cursorLineBoundaryLeft,
  cursorLineBoundaryRight,
  cursorLineDown,
  cursorLineEnd,
  cursorLineStart,
  cursorLineUp,
  cursorMatchingBracket,
  cursorPageDown,
  cursorPageUp,
  cursorSubwordBackward,
  cursorSubwordForward,
  cursorSyntaxLeft,
  cursorSyntaxRight,
  defaultKeymap,
  deleteCharBackward,
  deleteCharBackwardStrict,
  deleteCharForward,
  deleteGroupBackward,
  deleteGroupForward,
  deleteLine,
  deleteLineBoundaryBackward,
  deleteLineBoundaryForward,
  deleteToLineEnd,
  deleteToLineStart,
  deleteTrailingWhitespace,
  emacsStyleKeymap,
  history,
  historyField,
  historyKeymap,
  indentLess,
  indentMore,
  indentSelection,
  indentWithTab,
  insertBlankLine,
  insertNewline,
  insertNewlineAndIndent,
  insertNewlineKeepIndent,
  insertTab,
  invertedEffects,
  isolateHistory,
  lineComment,
  lineUncomment,
  moveLineDown,
  moveLineUp,
  redo,
  redoDepth,
  redoSelection,
  selectAll,
  selectCharBackward,
  selectCharBackwardLogical,
  selectCharForward,
  selectCharForwardLogical,
  selectCharLeft,
  selectCharRight,
  selectDocEnd,
  selectDocStart,
  selectGroupBackward,
  selectGroupForward,
  selectGroupForwardWin,
  selectGroupLeft,
  selectGroupRight,
  selectLine,
  selectLineBoundaryBackward,
  selectLineBoundaryForward,
  selectLineBoundaryLeft,
  selectLineBoundaryRight,
  selectLineDown,
  selectLineEnd,
  selectLineStart,
  selectLineUp,
  selectMatchingBracket,
  selectPageDown,
  selectPageUp,
  selectParentSyntax,
  selectSubwordBackward,
  selectSubwordForward,
  selectSyntaxLeft,
  selectSyntaxRight,
  simplifySelection,
  splitLine,
  standardKeymap,
  temporarilySetTabFocusMode,
  toggleBlockComment,
  toggleBlockCommentByLine,
  toggleComment,
  toggleLineComment,
  toggleTabFocusMode,
  transposeChars,
  undo,
  undoDepth,
  undoSelection
};
//# sourceMappingURL=@codemirror_commands.js.map
