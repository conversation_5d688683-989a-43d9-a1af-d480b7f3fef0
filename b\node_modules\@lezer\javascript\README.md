# @lezer/javascript

This is a JavaScript grammar for the
[lezer](https://lezer.codemirror.net/) parser system.

It parses modern JavaScript, and supports a `"ts"`
[dialect](https://lezer.codemirror.net/docs/guide/#dialects) to parse
TypeScript, and a `"jsx"` dialect to parse JSX.

The `top` option can be set to `"SingleExpression"` or
`"SingleClassItem"` to parse an expression or class item instead of a
full program.

The code is licensed under an MIT license.
