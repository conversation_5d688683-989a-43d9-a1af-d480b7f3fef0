import { css } from '../../styled-system/css';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
}

export default function LoadingSpinner(props: LoadingSpinnerProps) {
  const size = () => props.size || 'md';
  const text = () => props.text || '加载中...';
  
  const sizeClasses = {
    sm: { w: 4, h: 4, borderWidth: 2 },
    md: { w: 8, h: 8, borderWidth: 3 },
    lg: { w: 12, h: 12, borderWidth: 4 }
  };
  
  return (
    <div class={css({
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 3,
      py: 8
    })}>
      {/* 旋转动画 */}
      <div 
        class={css({
          ...sizeClasses[size()],
          border: `${sizeClasses[size()].borderWidth}px solid`,
          borderColor: 'gray.200',
          borderTopColor: 'primary.500',
          rounded: 'full',
          animation: 'spin 1s linear infinite',
          _dark: {
            borderColor: 'gray.700',
            borderTopColor: 'primary.400'
          }
        })}
      />
      
      {/* 加载文本 */}
      <p class={css({
        color: 'gray.600',
        fontSize: 'sm',
        fontWeight: 'medium',
        _dark: {
          color: 'gray.400'
        }
      })}>
        {text()}
      </p>
    </div>
  );
}
