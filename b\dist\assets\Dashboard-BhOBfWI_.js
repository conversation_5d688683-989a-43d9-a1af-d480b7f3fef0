import{c as _,t as g,i as l,a as s,u as C,S as x,s as e,m as w,b as R}from"./index-D_4j4rLs.js";var M=g("<div><p>⚠️ 您尚未登录，请先 <a href=/login>登录</a> 以查看完整功能。"),V=g("<div><h1>量化交易仪表盘</h1><p>欢迎使用量化交易平台！</p><p>当前时间: </p><div><h2>🎉 路由工作正常！</h2><p>这是Dashboard页面</p><p>SolidJS Router 已经正确配置</p></div><div><h2>滑动验证组件演示</h2><p>参考开源项目实现的滑动验证组件：</p><div><h3>Canvas版本（复杂实现）</h3></div><div><h3>简化版本（推荐使用）"),D=g("<div>");function F(){const[T,b]=_(new Date().toLocaleString("zh-CN")),u=()=>C.state.isAuthenticated;return setInterval(()=>{b(new Date().toLocaleString("zh-CN"))},1e3),(()=>{var f=V(),S=f.firstChild,m=S.nextSibling,h=m.nextSibling;h.firstChild;var a=h.nextSibling,t=a.nextSibling,y=t.firstChild,$=y.nextSibling,o=$.nextSibling;o.firstChild;var c=o.nextSibling;return c.firstChild,l(h,()=>new Date().toLocaleString("zh-CN"),null),l(f,s(x,{get when(){return u()},get children(){return s(UserProfile,{})}}),a),l(f,s(x,{get when(){return!u()},get children(){var r=M(),i=r.firstChild,n=i.firstChild,d=n.nextSibling;return e(r,"background","#fef3c7"),e(r,"border","1px solid #f59e0b"),e(r,"borderRadius","8px"),e(r,"padding","16px"),e(r,"margin","20px 0"),e(i,"margin","0"),e(i,"color","#92400e"),e(d,"color","#d97706"),e(d,"textDecoration","underline"),r}}),a),e(a,"padding","20px"),e(a,"background","#f0f0f0"),e(a,"margin","20px 0"),e(a,"borderRadius","8px"),e(t,"padding","20px"),e(t,"background","white"),e(t,"margin","20px 0"),e(t,"borderRadius","8px"),e(t,"boxShadow","0 2px 8px rgba(0,0,0,0.1)"),e(o,"margin","20px 0"),l(o,s(SlideVerify,{width:320,height:160,sliderText:"向右滑动完成验证",onSuccess:handleVerifySuccess,onFail:handleVerifyFail,onRefresh:handleVerifyRefresh}),null),e(c,"margin","20px 0"),l(c,s(SimpleSlideVerify,{width:320,height:160,sliderText:"向右滑动完成验证",onSuccess:handleVerifySuccess,onFail:handleVerifyFail,onRefresh:handleVerifyRefresh}),null),l(t,(()=>{var r=w(()=>!!verifyMessage());return()=>r()&&(()=>{var i=D();return e(i,"padding","10px"),e(i,"marginTop","10px"),e(i,"borderRadius","4px"),l(i,verifyMessage),R(n=>{var d=verifyMessage().includes("成功")?"#f6ffed":verifyMessage().includes("失败")?"#fff2f0":"#f0f9ff",p=`1px solid ${verifyMessage().includes("成功")?"#b7eb8f":verifyMessage().includes("失败")?"#ffccc7":"#91d5ff"}`,v=verifyMessage().includes("成功")?"#52c41a":verifyMessage().includes("失败")?"#ff4d4f":"#1890ff";return d!==n.e&&e(i,"backgroundColor",n.e=d),p!==n.t&&e(i,"border",n.t=p),v!==n.a&&e(i,"color",n.a=v),n},{e:void 0,t:void 0,a:void 0}),i})()})(),null),f})()}export{F as default};
