import { A } from '@solidjs/router';
import { userStore } from '../stores';

export default function Unauthorized() {
  const user = () => userStore.state.user;

  return (
    <div style={{
      display: 'flex',
      'flex-direction': 'column',
      'align-items': 'center',
      'justify-content': 'center',
      'min-height': '60vh',
      'text-align': 'center',
      padding: '40px 20px'
    }}>
      <div style={{
        'font-size': '120px',
        'margin-bottom': '20px'
      }}>
        🚫
      </div>
      
      <h1 style={{
        'font-size': '2.5rem',
        'font-weight': 'bold',
        color: '#dc2626',
        'margin-bottom': '16px'
      }}>
        访问被拒绝
      </h1>
      
      <p style={{
        'font-size': '1.1rem',
        color: '#6b7280',
        'margin-bottom': '8px',
        'max-width': '500px',
        'line-height': '1.6'
      }}>
        抱歉，您没有权限访问此页面。
      </p>
      
      <p style={{
        'font-size': '0.9rem',
        color: '#9ca3af',
        'margin-bottom': '32px'
      }}>
        当前角色：{user()?.role === 'admin' ? '管理员' : user()?.role === 'vip' ? '高级用户' : '普通用户'}
      </p>

      <div style={{
        display: 'flex',
        gap: '16px',
        'flex-wrap': 'wrap',
        'justify-content': 'center'
      }}>
        <A
          href="/"
          style={{
            padding: '12px 24px',
            'background-color': '#3b82f6',
            color: 'white',
            'text-decoration': 'none',
            'border-radius': '8px',
            'font-weight': '500',
            transition: 'all 0.2s'
          }}
          onMouseEnter={(e) => {
            (e.target as HTMLElement).style.backgroundColor = '#2563eb';
          }}
          onMouseLeave={(e) => {
            (e.target as HTMLElement).style.backgroundColor = '#3b82f6';
          }}
        >
          🏠 返回首页
        </A>
        
        <button
          style={{
            padding: '12px 24px',
            'background-color': '#f3f4f6',
            color: '#374151',
            border: '1px solid #d1d5db',
            'border-radius': '8px',
            'font-weight': '500',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}
          onClick={() => window.history.back()}
          onMouseEnter={(e) => {
            (e.target as HTMLElement).style.backgroundColor = '#e5e7eb';
          }}
          onMouseLeave={(e) => {
            (e.target as HTMLElement).style.backgroundColor = '#f3f4f6';
          }}
        >
          ← 返回上页
        </button>
      </div>

      <div style={{
        'margin-top': '40px',
        padding: '20px',
        'background-color': '#fef3c7',
        border: '1px solid #f59e0b',
        'border-radius': '8px',
        'max-width': '500px'
      }}>
        <h3 style={{
          'font-size': '1.1rem',
          'font-weight': '600',
          color: '#92400e',
          'margin-bottom': '8px'
        }}>
          💡 需要更高权限？
        </h3>
        <p style={{
          'font-size': '0.9rem',
          color: '#92400e',
          margin: '0',
          'line-height': '1.5'
        }}>
          请联系系统管理员申请相应的访问权限，或使用具有足够权限的账户重新登录。
        </p>
      </div>
    </div>
  );
}
