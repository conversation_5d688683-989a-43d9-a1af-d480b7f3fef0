export declare const enum Action {
    ReduceFlag = 65536,
    ValueMask = 65535,
    ReduceDepthShift = 19,
    RepeatFlag = 131072,
    GotoFlag = 131072,
    StayFlag = 262144
}
export declare const enum StateFlag {
    Skipped = 1,
    Accepting = 2
}
export declare const enum Specialize {
    Specialize = 0,
    Extend = 1
}
export declare const enum Term {
    Err = 0
}
export declare const enum Seq {
    End = 65535,
    Done = 0,
    Next = 1,
    Other = 2
}
export declare const enum ParseState {
    Flags = 0,
    Actions = 1,
    Skip = 2,
    TokenizerMask = 3,
    DefaultReduce = 4,
    ForcedReduce = 5,
    Size = 6
}
export declare const enum Encode {
    BigValCode = 126,
    BigVal = 65535,
    Start = 32,
    Gap1 = 34,
    Gap2 = 92,
    Base = 46
}
export declare const enum File {
    Version = 14
}
