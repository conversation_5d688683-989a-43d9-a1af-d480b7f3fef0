import{c as y,h as Ae,k as ge,p as De,t as Ee,s as e,i as V,b as Ie,e as Le}from"./index-B8TYkGLu.js";var Pe=Ee('<div style=max-width:1280px><div style=align-items:center;justify-content:space-between;margin-bottom:32px><div><h1 style=font-size:2.5rem;font-weight:bold;margin-bottom:8px>回测分析</h1><p style=font-size:1.125rem>验证策略历史表现，优化交易参数</p><div style=align-items:center;margin-top:8px><span style=font-size:0.875rem>总回测: </span><span style=font-size:0.875rem>已完成: </span></div></div><button style=border-radius:8px;font-size:14px;font-weight:500></button></div><div style=border-radius:12px;margin-bottom:24px><h2 style=font-size:1.25rem;font-weight:600;margin-bottom:16px>回测配置</h2><div style="grid-template-columns:repeat(auto-fit, minmax(200px, 1fr))"><div><label style=font-size:14px;font-weight:500;margin-bottom:8px>选择策略</label><select style=border-radius:6px;font-size:14px><option value>选择策略</option><option value=dual-ma>双均线策略</option><option value=rsi>RSI反转策略</option><option value=bollinger>布林带策略</option></select></div><div><label style=font-size:14px;font-weight:500;margin-bottom:8px>开始日期</label><input type=date value=2023-01-01 style=border-radius:6px;font-size:14px></div><div><label style=font-size:14px;font-weight:500;margin-bottom:8px>结束日期</label><input type=date value=2024-01-01 style=border-radius:6px;font-size:14px></div><div><label style=font-size:14px;font-weight:500;margin-bottom:8px>初始资金</label><input type=number value=100000 style=border-radius:6px;font-size:14px></div></div></div><div style="grid-template-columns:2fr 1fr"><div style=flex-direction:column><div style=border-radius:12px><h3 style=font-size:1.125rem;font-weight:600;margin-bottom:16px>关键指标</h3><div style="grid-template-columns:repeat(4, 1fr)"><div style=text-align:center><div style=font-size:2rem;font-weight:bold;margin-bottom:4px>+25.0%</div><div style=font-size:14px>总收益率</div></div><div style=text-align:center><div style=font-size:2rem;font-weight:bold;margin-bottom:4px>-8.5%</div><div style=font-size:14px>最大回撤</div></div><div style=text-align:center><div style=font-size:2rem;font-weight:bold;margin-bottom:4px>1.45</div><div style=font-size:14px>夏普比率</div></div><div style=text-align:center><div style=font-size:2rem;font-weight:bold;margin-bottom:4px>68.5%</div><div style=font-size:14px>胜率</div></div></div></div><div style=border-radius:12px;align-items:center;justify-content:center><div style=text-align:center><div style=font-size:4rem;margin-bottom:16px>📊</div><p style=font-size:18px;margin-bottom:8px>收益曲线图表</p><p style=font-size:14px>(图表组件将在后续版本中实现)</p></div></div></div><div style=flex-direction:column><div style=border-radius:12px><h3 style=font-size:1rem;font-weight:600;margin-bottom:12px>历史回测</h3><div style=flex-direction:column><div style=border-radius:8px><div style=align-items:center;justify-content:space-between;margin-bottom:4px><span style=font-size:14px;font-weight:500>回测 #1</span><span style=font-size:12px;font-weight:500>+25%</span></div><div style=font-size:12px>2024-01-01</div></div><div style=border-radius:8px><div style=align-items:center;justify-content:space-between;margin-bottom:4px><span style=font-size:14px;font-weight:500>回测 #2</span><span style=font-size:12px;font-weight:500>-5%</span></div><div style=font-size:12px>2024-01-02</div></div></div></div><div style=border-radius:12px><h3 style=font-size:1rem;font-weight:600;margin-bottom:12px>风险指标</h3><div style=flex-direction:column><div style=align-items:center;justify-content:space-between><span style=font-size:14px>波动率</span><span style=font-size:14px;font-weight:500>15.2%</span></div><div style=align-items:center;justify-content:space-between><span style=font-size:14px>VaR (95%)</span><span style=font-size:14px;font-weight:500>-2.8%</span></div><div style=align-items:center;justify-content:space-between><span style=font-size:14px>贝塔系数</span><span style=font-size:14px;font-weight:500>0.85');function He(){const[q,F]=y([]),[Ve,be]=y([]),[p,x]=y(!1),[qe,Fe]=y(null);Ae(async()=>{try{x(!0);const[t,n]=await Promise.all([ge.getBacktests(),De.getStrategies()]);F(t.items),be(n.items)}catch(t){console.error("获取数据失败:",t)}finally{x(!1)}});const ce=async()=>{try{x(!0);const t=await ge.createBacktest({name:`回测任务 ${Date.now()}`,strategyId:"strategy-001",symbol:"000001",startDate:"2023-01-01",endDate:"2024-01-01",initialCapital:1e5});F(n=>[t,...n])}catch(t){console.error("创建回测失败:",t)}finally{x(!1)}};return(()=>{var t=Pe(),n=t.firstChild,G=n.firstChild,H=G.firstChild,J=H.nextSibling,v=J.nextSibling,f=v.firstChild;f.firstChild;var m=f.nextSibling;m.firstChild;var l=G.nextSibling,o=n.nextSibling,K=o.firstChild,h=K.nextSibling,M=h.firstChild,$=M.firstChild,_=$.nextSibling,N=M.nextSibling,u=N.firstChild,S=u.nextSibling,O=N.nextSibling,w=O.firstChild,z=w.nextSibling,ye=O.nextSibling,C=ye.firstChild,k=C.nextSibling,B=o.nextSibling,g=B.firstChild,d=g.firstChild,Q=d.firstChild,j=Q.nextSibling,T=j.firstChild,U=T.firstChild,ve=U.nextSibling,W=T.nextSibling,X=W.firstChild,me=X.nextSibling,Y=W.nextSibling,Z=Y.firstChild,he=Z.nextSibling,$e=Y.nextSibling,ee=$e.firstChild,_e=ee.nextSibling,s=d.nextSibling,te=s.firstChild,ue=te.firstChild,Se=ue.nextSibling;Se.nextSibling;var R=g.nextSibling,r=R.firstChild,ie=r.firstChild,A=ie.nextSibling,a=A.firstChild,D=a.firstChild,le=D.firstChild,we=le.nextSibling,ze=D.nextSibling,b=a.nextSibling,E=b.firstChild,ne=E.firstChild,Ce=ne.nextSibling,ke=E.nextSibling,c=r.nextSibling,se=c.firstChild,I=se.nextSibling,L=I.firstChild,oe=L.firstChild,Be=oe.nextSibling,P=L.nextSibling,de=P.firstChild,je=de.nextSibling,re=P.nextSibling,ae=re.firstChild,Re=ae.nextSibling;return e(t,"width","100%"),e(t,"margin","0 auto"),e(t,"padding","0 16px 24px"),e(n,"display","flex"),e(H,"color","#111827"),e(J,"color","#6b7280"),e(v,"display","flex"),e(v,"gap","16px"),e(f,"color","#9ca3af"),V(f,()=>q().length,null),e(m,"color","#9ca3af"),V(m,()=>q().filter(i=>i.status==="completed").length,null),l.$$click=ce,e(l,"padding","12px 24px"),e(l,"color","white"),e(l,"border","none"),V(l,()=>p()?"⏳ 处理中...":"🚀 运行回测"),e(o,"padding","24px"),e(o,"background","white"),e(o,"border","1px solid #e5e7eb"),e(K,"color","#111827"),e(h,"display","grid"),e(h,"gap","16px"),e($,"display","block"),e($,"color","#374151"),e(_,"width","100%"),e(_,"padding","8px 12px"),e(_,"border","1px solid #d1d5db"),e(u,"display","block"),e(u,"color","#374151"),e(S,"width","100%"),e(S,"padding","8px 12px"),e(S,"border","1px solid #d1d5db"),e(w,"display","block"),e(w,"color","#374151"),e(z,"width","100%"),e(z,"padding","8px 12px"),e(z,"border","1px solid #d1d5db"),e(C,"display","block"),e(C,"color","#374151"),e(k,"width","100%"),e(k,"padding","8px 12px"),e(k,"border","1px solid #d1d5db"),e(B,"display","grid"),e(B,"gap","24px"),e(g,"display","flex"),e(g,"gap","24px"),e(d,"padding","24px"),e(d,"background","white"),e(d,"border","1px solid #e5e7eb"),e(Q,"color","#111827"),e(j,"display","grid"),e(j,"gap","24px"),e(U,"color","#22c55e"),e(ve,"color","#6b7280"),e(X,"color","#ef4444"),e(me,"color","#6b7280"),e(Z,"color","#3b82f6"),e(he,"color","#6b7280"),e(ee,"color","#f59e0b"),e(_e,"color","#6b7280"),e(s,"padding","24px"),e(s,"background","white"),e(s,"border","1px solid #e5e7eb"),e(s,"height","400px"),e(s,"display","flex"),e(te,"color","#9ca3af"),e(R,"display","flex"),e(R,"gap","24px"),e(r,"padding","20px"),e(r,"background","white"),e(r,"border","1px solid #e5e7eb"),e(ie,"color","#111827"),e(A,"display","flex"),e(A,"gap","8px"),e(a,"padding","12px"),e(a,"background","#f9fafb"),e(a,"cursor","pointer"),e(D,"display","flex"),e(le,"color","#111827"),e(we,"color","#22c55e"),e(ze,"color","#6b7280"),e(b,"padding","12px"),e(b,"background","#f9fafb"),e(b,"cursor","pointer"),e(E,"display","flex"),e(ne,"color","#111827"),e(Ce,"color","#ef4444"),e(ke,"color","#6b7280"),e(c,"padding","20px"),e(c,"background","white"),e(c,"border","1px solid #e5e7eb"),e(se,"color","#111827"),e(I,"display","flex"),e(I,"gap","12px"),e(L,"display","flex"),e(oe,"color","#6b7280"),e(Be,"color","#111827"),e(P,"display","flex"),e(de,"color","#6b7280"),e(je,"color","#111827"),e(re,"display","flex"),e(ae,"color","#6b7280"),e(Re,"color","#111827"),Ie(i=>{var pe=p(),xe=p()?"#9ca3af":"#3b82f6",fe=p()?"not-allowed":"pointer";return pe!==i.e&&(l.disabled=i.e=pe),xe!==i.t&&e(l,"background",i.t=xe),fe!==i.a&&e(l,"cursor",i.a=fe),i},{e:void 0,t:void 0,a:void 0}),t})()}Le(["click"]);export{He as default};
