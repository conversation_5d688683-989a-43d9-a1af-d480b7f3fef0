import { createSignal, createEffect, onCleanup, For, Show, createMemo } from 'solid-js';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import numeral from 'numeral';
import { v4 as uuidv4 } from 'uuid';

export interface Order {
  id: string;
  orderNo: string;
  symbol: string;
  direction: 'buy' | 'sell';
  orderType: 'market' | 'limit' | 'stop' | 'takeProfit';
  quantity: number;
  price: number;
  stopPrice?: number;
  status: 'pending' | 'filled' | 'cancelled' | 'partiallyFilled';
  filledQuantity: number;
  averagePrice: number;
  createTime: number;
  updateTime: number;
  commission: number;
  filledAmount?: number;
  notes?: string;
}

export interface Position {
  symbol: string;
  quantity: number;
  averageCost: number;
  currentPrice: number;
  unrealizedPnL: number;
  realizedPnL: number;
  totalValue: number;
  lastUpdate: number;
}

interface OrderManagementProps {
  orders?: Order[];
  positions?: Position[];
  onCreateOrder?: (order: Partial<Order>) => Promise<void>;
  onCancelOrder?: (orderId: string) => Promise<void>;
  onModifyOrder?: (orderId: string, changes: Partial<Order>) => Promise<void>;
  onClosePosition?: (symbol: string, quantity?: number) => Promise<void>;
  onCancelAllOrders?: () => Promise<void>;
  onRefreshOrders?: () => Promise<void>;
  theme?: 'light' | 'dark';
  tradingEnabled?: boolean;
  loading?: boolean;
}

export default function OrderManagement(props: OrderManagementProps) {
  const [activeTab, setActiveTab] = createSignal<'pending' | 'filled' | 'cancelled'>('pending');
  const [selectedOrder, setSelectedOrder] = createSignal<Order | null>(null);
  const [selectedPosition, setSelectedPosition] = createSignal<Position | null>(null);
  const [modifyDialogVisible, setModifyDialogVisible] = createSignal(false);
  const [detailDialogVisible, setDetailDialogVisible] = createSignal(false);
  const [modifying, setModifying] = createSignal(false);
  
  // 修改订单表单
  const [modifyForm, setModifyForm] = createSignal({
    orderNo: '',
    symbol: '',
    price: 0,
    quantity: 0,
  });

  const [isSubmitting, setIsSubmitting] = createSignal(false);
  const [updateTime, setUpdateTime] = createSignal(Date.now());

  // 定时更新时间戳
  const timeInterval = setInterval(() => {
    setUpdateTime(Date.now());
  }, 1000);

  onCleanup(() => {
    clearInterval(timeInterval);
  });

  // 计算属性 - 按状态分组的订单
  const pendingOrders = createMemo(() => {
    return props.orders?.filter(order => order.status === 'pending' || order.status === 'partiallyFilled') || [];
  });

  const filledOrders = createMemo(() => {
    return props.orders?.filter(order => order.status === 'filled') || [];
  });

  const cancelledOrders = createMemo(() => {
    return props.orders?.filter(order => order.status === 'cancelled') || [];
  });

  const currentOrders = createMemo(() => {
    switch (activeTab()) {
      case 'pending':
        return pendingOrders();
      case 'filled':
        return filledOrders();
      case 'cancelled':
        return cancelledOrders();
      default:
        return [];
    }
  });

  // 格式化价格
  const formatPrice = (price: number) => {
    return numeral(price).format('0,0.00');
  };

  // 格式化百分比
  const formatPercent = (value: number) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${numeral(value).format('0.00')}%`;
  };

  // 获取订单状态颜色
  const getOrderStatusColor = (status: Order['status']) => {
    const colors = {
      pending: props.theme === 'dark' ? '#f59e0b' : '#d97706',
      filled: props.theme === 'dark' ? '#10b981' : '#059669',
      cancelled: props.theme === 'dark' ? '#ef4444' : '#dc2626',
      partial: props.theme === 'dark' ? '#3b82f6' : '#2563eb',
    };
    return colors[status];
  };

  // 获取盈亏颜色
  const getPnLColor = (pnl: number) => {
    if (pnl > 0) return props.theme === 'dark' ? '#10b981' : '#059669';
    if (pnl < 0) return props.theme === 'dark' ? '#ef4444' : '#dc2626';
    return props.theme === 'dark' ? '#9ca3af' : '#6b7280';
  };

  // 获取订单类型显示文本
  const getOrderTypeText = (type: Order['orderType']) => {
    const texts = {
      market: '市价单',
      limit: '限价单',
      stop: '止损单',
    };
    return texts[type];
  };

  // 获取订单状态显示文本
  const getOrderStatusText = (status: Order['status']) => {
    const texts = {
      pending: '待成交',
      filled: '已成交',
      cancelled: '已取消',
      partial: '部分成交',
    };
    return texts[status];
  };

  // 提交新订单
  const handleSubmitOrder = async () => {
    if (!props.onCreateOrder || !props.tradingEnabled) return;
    
    const order = newOrder();
    if (!order.symbol || order.quantity <= 0) {
      alert('请填写完整的订单信息');
      return;
    }

    if (order.orderType === 'limit' && order.price <= 0) {
      alert('限价单需要设置价格');
      return;
    }

    if (order.orderType === 'stop' && order.stopPrice <= 0) {
      alert('止损单需要设置止损价格');
      return;
    }

    setIsSubmitting(true);
    try {
      await props.onCreateOrder({
        ...order,
        id: uuidv4(),
        status: 'pending',
        filledQuantity: 0,
        averagePrice: 0,
        createTime: Date.now(),
        updateTime: Date.now(),
        commission: 0,
      });
      
      // 重置表单
      setNewOrder({
        symbol: '',
        type: 'buy',
        orderType: 'market',
        quantity: 0,
        price: 0,
        stopPrice: 0,
        notes: '',
      });
      
      // 切换到订单列表
      setActiveTab('orders');
    } catch (error) {
      console.error('创建订单失败:', error);
      alert('创建订单失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 取消订单
  const handleCancelOrder = async (orderId: string) => {
    if (!props.onCancelOrder) return;
    
    const confirmed = confirm('确认取消此订单？');
    if (!confirmed) return;

    try {
      await props.onCancelOrder(orderId);
    } catch (error) {
      console.error('取消订单失败:', error);
      alert('取消订单失败，请重试');
    }
  };

  // 平仓
  const handleClosePosition = async (symbol: string, quantity?: number) => {
    if (!props.onClosePosition) return;
    
    const confirmed = confirm(`确认平仓 ${symbol} ${quantity ? quantity + '股' : '全部'}？`);
    if (!confirmed) return;

    try {
      await props.onClosePosition(symbol, quantity);
    } catch (error) {
      console.error('平仓失败:', error);
      alert('平仓失败，请重试');
    }
  };

  return (
    <div style={{
      background: props.theme === 'dark' ? '#1f2937' : '#ffffff',
      'border-radius': '8px',
      'box-shadow': '0 1px 3px 0 rgb(0 0 0 / 0.1)',
      overflow: 'hidden',
    }}>
      {/* 标签导航 */}
      <div style={{
        display: 'flex',
        'border-bottom': `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
      }}>
        <button
          onClick={() => setActiveTab('orders')}
          style={{
            padding: '16px 24px',
            border: 'none',
            background: activeTab() === 'orders' 
              ? (props.theme === 'dark' ? '#374151' : '#f3f4f6')
              : 'transparent',
            color: props.theme === 'dark' ? '#f9fafb' : '#374151',
            cursor: 'pointer',
            'font-weight': '500',
            'border-bottom': activeTab() === 'orders' ? '2px solid #3b82f6' : 'none',
          }}
        >
          订单管理 ({filteredOrders().length})
        </button>
        <button
          onClick={() => setActiveTab('positions')}
          style={{
            padding: '16px 24px',
            border: 'none',
            background: activeTab() === 'positions' 
              ? (props.theme === 'dark' ? '#374151' : '#f3f4f6')
              : 'transparent',
            color: props.theme === 'dark' ? '#f9fafb' : '#374151',
            cursor: 'pointer',
            'font-weight': '500',
            'border-bottom': activeTab() === 'positions' ? '2px solid #3b82f6' : 'none',
          }}
        >
          持仓管理 ({props.positions?.length || 0})
        </button>
        <Show when={props.tradingEnabled}>
          <button
            onClick={() => setActiveTab('create')}
            style={{
              padding: '16px 24px',
              border: 'none',
              background: activeTab() === 'create' 
                ? (props.theme === 'dark' ? '#374151' : '#f3f4f6')
                : 'transparent',
              color: props.theme === 'dark' ? '#f9fafb' : '#374151',
              cursor: 'pointer',
              'font-weight': '500',
              'border-bottom': activeTab() === 'create' ? '2px solid #3b82f6' : 'none',
            }}
          >
            下单交易
          </button>
        </Show>
      </div>

      {/* 订单管理 */}
      <Show when={activeTab() === 'orders'}>
        <div style={{ padding: '16px' }}>
          {/* 过滤器 */}
          <div style={{
            display: 'flex',
            gap: '8px',
            'margin-bottom': '16px',
          }}>
            <For each={[
              { key: 'all', label: '全部' },
              { key: 'pending', label: '待成交' },
              { key: 'filled', label: '已成交' },
              { key: 'cancelled', label: '已取消' },
            ]}>
              {(filter) => (
                <button
                  onClick={() => setOrderFilter(filter.key as any)}
                  style={{
                    padding: '6px 12px',
                    'border-radius': '4px',
                    border: `1px solid ${props.theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                    background: orderFilter() === filter.key 
                      ? '#3b82f6' 
                      : (props.theme === 'dark' ? '#374151' : '#f9fafb'),
                    color: orderFilter() === filter.key 
                      ? 'white' 
                      : (props.theme === 'dark' ? '#f9fafb' : '#374151'),
                    cursor: 'pointer',
                    'font-size': '12px',
                  }}
                >
                  {filter.label}
                </button>
              )}
            </For>
          </div>

          {/* 订单列表 */}
          <div style={{ 'max-height': '400px', 'overflow-y': 'auto' }}>
            <Show when={filteredOrders().length > 0} fallback={
              <div style={{
                padding: '32px',
                'text-align': 'center',
                color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
              }}>
                暂无订单
              </div>
            }>
              <For each={filteredOrders()}>
                {(order) => (
                  <div style={{
                    padding: '16px',
                    'border-bottom': `1px solid ${props.theme === 'dark' ? '#374151' : '#f3f4f6'}`,
                    display: 'grid',
                    'grid-template-columns': '1fr 1fr 1fr 1fr 1fr 1fr 100px',
                    'align-items': 'center',
                    gap: '12px',
                  }}>
                    <div>
                      <div style={{
                        'font-weight': '600',
                        color: props.theme === 'dark' ? '#f9fafb' : '#111827',
                      }}>
                        {order.symbol}
                      </div>
                      <div style={{
                        'font-size': '12px',
                        color: order.type === 'buy' ? '#10b981' : '#ef4444',
                      }}>
                        {order.type === 'buy' ? '买入' : '卖出'}
                      </div>
                    </div>

                    <div>
                      <div style={{
                        color: props.theme === 'dark' ? '#d1d5db' : '#374151',
                      }}>
                        {getOrderTypeText(order.orderType)}
                      </div>
                      <div style={{
                        'font-size': '12px',
                        color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
                      }}>
                        {order.quantity} 股
                      </div>
                    </div>

                    <div>
                      <Show when={order.price}>
                        <div style={{
                          color: props.theme === 'dark' ? '#d1d5db' : '#374151',
                        }}>
                          ¥{formatPrice(order.price!)}
                        </div>
                      </Show>
                      <Show when={order.stopPrice}>
                        <div style={{
                          'font-size': '12px',
                          color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
                        }}>
                          止损: ¥{formatPrice(order.stopPrice!)}
                        </div>
                      </Show>
                    </div>

                    <div>
                      <div style={{
                        color: getOrderStatusColor(order.status),
                        'font-weight': '500',
                      }}>
                        {getOrderStatusText(order.status)}
                      </div>
                      <Show when={order.status === 'partial'}>
                        <div style={{
                          'font-size': '12px',
                          color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
                        }}>
                          {order.filledQuantity}/{order.quantity}
                        </div>
                      </Show>
                    </div>

                    <div>
                      <Show when={order.averagePrice > 0}>
                        <div style={{
                          color: props.theme === 'dark' ? '#d1d5db' : '#374151',
                        }}>
                          ¥{formatPrice(order.averagePrice)}
                        </div>
                      </Show>
                    </div>

                    <div>
                      <div style={{
                        'font-size': '12px',
                        color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
                      }}>
                        {format(order.createTime, 'MM-dd HH:mm', { locale: zhCN })}
                      </div>
                    </div>

                    <div>
                      <Show when={order.status === 'pending'}>
                        <button
                          onClick={() => handleCancelOrder(order.id)}
                          style={{
                            padding: '4px 8px',
                            'border-radius': '4px',
                            border: 'none',
                            background: '#ef4444',
                            color: 'white',
                            cursor: 'pointer',
                            'font-size': '12px',
                          }}
                        >
                          取消
                        </button>
                      </Show>
                    </div>
                  </div>
                )}
              </For>
            </Show>
          </div>
        </div>
      </Show>

      {/* 持仓管理 */}
      <Show when={activeTab() === 'positions'}>
        <div style={{ padding: '16px' }}>
          <div style={{ 'max-height': '400px', 'overflow-y': 'auto' }}>
            <Show when={props.positions && props.positions.length > 0} fallback={
              <div style={{
                padding: '32px',
                'text-align': 'center',
                color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
              }}>
                暂无持仓
              </div>
            }>
              <For each={props.positions}>
                {(position) => (
                  <div style={{
                    padding: '16px',
                    'border-bottom': `1px solid ${props.theme === 'dark' ? '#374151' : '#f3f4f6'}`,
                    display: 'grid',
                    'grid-template-columns': '1fr 1fr 1fr 1fr 1fr 1fr 100px',
                    'align-items': 'center',
                    gap: '12px',
                  }}>
                    <div>
                      <div style={{
                        'font-weight': '600',
                        color: props.theme === 'dark' ? '#f9fafb' : '#111827',
                      }}>
                        {position.symbol}
                      </div>
                      <div style={{
                        'font-size': '12px',
                        color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
                      }}>
                        {position.quantity} 股
                      </div>
                    </div>

                    <div>
                      <div style={{
                        color: props.theme === 'dark' ? '#d1d5db' : '#374151',
                      }}>
                        ¥{formatPrice(position.averageCost)}
                      </div>
                      <div style={{
                        'font-size': '12px',
                        color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
                      }}>
                        成本价
                      </div>
                    </div>

                    <div>
                      <div style={{
                        color: props.theme === 'dark' ? '#d1d5db' : '#374151',
                        'font-weight': '500',
                      }}>
                        ¥{formatPrice(position.currentPrice)}
                      </div>
                      <div style={{
                        'font-size': '12px',
                        color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
                      }}>
                        现价
                      </div>
                    </div>

                    <div>
                      <div style={{
                        color: getPnLColor(position.unrealizedPnL),
                        'font-weight': '500',
                      }}>
                        ¥{formatPrice(Math.abs(position.unrealizedPnL))}
                      </div>
                      <div style={{
                        'font-size': '12px',
                        color: getPnLColor(position.unrealizedPnL),
                      }}>
                        {formatPercent((position.currentPrice - position.averageCost) / position.averageCost * 100)}
                      </div>
                    </div>

                    <div>
                      <div style={{
                        color: props.theme === 'dark' ? '#d1d5db' : '#374151',
                        'font-weight': '500',
                      }}>
                        ¥{formatPrice(position.totalValue)}
                      </div>
                      <div style={{
                        'font-size': '12px',
                        color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
                      }}>
                        市值
                      </div>
                    </div>

                    <div>
                      <div style={{
                        'font-size': '12px',
                        color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
                      }}>
                        {format(position.lastUpdate, 'MM-dd HH:mm', { locale: zhCN })}
                      </div>
                    </div>

                    <div>
                      <Show when={props.tradingEnabled}>
                        <button
                          onClick={() => handleClosePosition(position.symbol)}
                          style={{
                            padding: '4px 8px',
                            'border-radius': '4px',
                            border: 'none',
                            background: '#ef4444',
                            color: 'white',
                            cursor: 'pointer',
                            'font-size': '12px',
                          }}
                        >
                          平仓
                        </button>
                      </Show>
                    </div>
                  </div>
                )}
              </For>
            </Show>
          </div>
        </div>
      </Show>

      {/* 下单交易 */}
      <Show when={activeTab() === 'create' && props.tradingEnabled}>
        <div style={{ padding: '24px' }}>
          <div style={{
            'max-width': '500px',
            margin: '0 auto',
          }}>
            <h3 style={{
              margin: '0 0 24px 0',
              'font-size': '18px',
              'font-weight': 'bold',
              color: props.theme === 'dark' ? '#f9fafb' : '#111827',
              'text-align': 'center',
            }}>
              创建新订单
            </h3>

            <div style={{ display: 'grid', gap: '16px' }}>
              <div>
                <label style={{
                  display: 'block',
                  'font-weight': '500',
                  'margin-bottom': '6px',
                  color: props.theme === 'dark' ? '#d1d5db' : '#374151',
                }}>
                  股票代码 *
                </label>
                <input
                  type="text"
                  placeholder="例如: 000001.SZ"
                  value={newOrder().symbol}
                  onInput={(e) => setNewOrder(prev => ({ ...prev, symbol: e.currentTarget.value.toUpperCase() }))}
                  style={{
                    width: '100%',
                    padding: '12px',
                    'border-radius': '6px',
                    border: `1px solid ${props.theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                    background: props.theme === 'dark' ? '#111827' : '#ffffff',
                    color: props.theme === 'dark' ? '#f9fafb' : '#374151',
                  }}
                />
              </div>

              <div style={{ display: 'grid', 'grid-template-columns': '1fr 1fr', gap: '12px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    'font-weight': '500',
                    'margin-bottom': '6px',
                    color: props.theme === 'dark' ? '#d1d5db' : '#374151',
                  }}>
                    交易方向 *
                  </label>
                  <select
                    value={newOrder().type}
                    onChange={(e) => setNewOrder(prev => ({ ...prev, type: e.currentTarget.value as any }))}
                    style={{
                      width: '100%',
                      padding: '12px',
                      'border-radius': '6px',
                      border: `1px solid ${props.theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                      background: props.theme === 'dark' ? '#111827' : '#ffffff',
                      color: props.theme === 'dark' ? '#f9fafb' : '#374151',
                    }}
                  >
                    <option value="buy">买入</option>
                    <option value="sell">卖出</option>
                  </select>
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    'font-weight': '500',
                    'margin-bottom': '6px',
                    color: props.theme === 'dark' ? '#d1d5db' : '#374151',
                  }}>
                    订单类型 *
                  </label>
                  <select
                    value={newOrder().orderType}
                    onChange={(e) => setNewOrder(prev => ({ ...prev, orderType: e.currentTarget.value as any }))}
                    style={{
                      width: '100%',
                      padding: '12px',
                      'border-radius': '6px',
                      border: `1px solid ${props.theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                      background: props.theme === 'dark' ? '#111827' : '#ffffff',
                      color: props.theme === 'dark' ? '#f9fafb' : '#374151',
                    }}
                  >
                    <option value="market">市价单</option>
                    <option value="limit">限价单</option>
                    <option value="stop">止损单</option>
                  </select>
                </div>
              </div>

              <div style={{ display: 'grid', 'grid-template-columns': '1fr 1fr', gap: '12px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    'font-weight': '500',
                    'margin-bottom': '6px',
                    color: props.theme === 'dark' ? '#d1d5db' : '#374151',
                  }}>
                    数量 (股) *
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="100"
                    value={newOrder().quantity}
                    onInput={(e) => setNewOrder(prev => ({ ...prev, quantity: parseInt(e.currentTarget.value) || 0 }))}
                    style={{
                      width: '100%',
                      padding: '12px',
                      'border-radius': '6px',
                      border: `1px solid ${props.theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                      background: props.theme === 'dark' ? '#111827' : '#ffffff',
                      color: props.theme === 'dark' ? '#f9fafb' : '#374151',
                    }}
                  />
                </div>

                <Show when={newOrder().orderType === 'limit' || newOrder().orderType === 'stop'}>
                  <div>
                    <label style={{
                      display: 'block',
                      'font-weight': '500',
                      'margin-bottom': '6px',
                      color: props.theme === 'dark' ? '#d1d5db' : '#374151',
                    }}>
                      {newOrder().orderType === 'limit' ? '限价' : '止损价'} (元) *
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newOrder().orderType === 'limit' ? newOrder().price : newOrder().stopPrice}
                      onInput={(e) => {
                        const value = parseFloat(e.currentTarget.value) || 0;
                        if (newOrder().orderType === 'limit') {
                          setNewOrder(prev => ({ ...prev, price: value }));
                        } else {
                          setNewOrder(prev => ({ ...prev, stopPrice: value }));
                        }
                      }}
                      style={{
                        width: '100%',
                        padding: '12px',
                        'border-radius': '6px',
                        border: `1px solid ${props.theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                        background: props.theme === 'dark' ? '#111827' : '#ffffff',
                        color: props.theme === 'dark' ? '#f9fafb' : '#374151',
                      }}
                    />
                  </div>
                </Show>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  'font-weight': '500',
                  'margin-bottom': '6px',
                  color: props.theme === 'dark' ? '#d1d5db' : '#374151',
                }}>
                  备注
                </label>
                <textarea
                  placeholder="订单备注信息..."
                  value={newOrder().notes}
                  onInput={(e) => setNewOrder(prev => ({ ...prev, notes: e.currentTarget.value }))}
                  rows="3"
                  style={{
                    width: '100%',
                    padding: '12px',
                    'border-radius': '6px',
                    border: `1px solid ${props.theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                    background: props.theme === 'dark' ? '#111827' : '#ffffff',
                    color: props.theme === 'dark' ? '#f9fafb' : '#374151',
                    resize: 'vertical',
                  }}
                />
              </div>

              <button
                onClick={handleSubmitOrder}
                disabled={isSubmitting() || !newOrder().symbol || newOrder().quantity <= 0}
                style={{
                  width: '100%',
                  padding: '12px',
                  'border-radius': '6px',
                  border: 'none',
                  background: isSubmitting() ? '#6b7280' : newOrder().type === 'buy' ? '#10b981' : '#ef4444',
                  color: 'white',
                  cursor: isSubmitting() ? 'not-allowed' : 'pointer',
                  'font-weight': '500',
                  'font-size': '16px',
                }}
              >
                {isSubmitting() ? '提交中...' : `${newOrder().type === 'buy' ? '买入' : '卖出'} ${newOrder().symbol}`}
              </button>
            </div>
          </div>
        </div>
      </Show>
    </div>
  );
}