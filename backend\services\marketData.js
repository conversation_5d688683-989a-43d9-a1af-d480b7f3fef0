/**
 * 市场数据服务
 * 提供实时行情、K线数据、股票搜索等功能
 */
import { EventEmitter } from 'events';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class MarketDataService extends EventEmitter {
  constructor() {
    super();
    this.stocks = new Map();
    this.realTimeData = new Map();
    this.klineCache = new Map();
    this.updateInterval = null;
    this.initialized = false;
  }

  /**
   * 初始化市场数据服务
   */
  async initialize() {
    try {
      console.log('📊 正在初始化市场数据服务...');
      
      // 初始化股票基础数据
      await this.initializeStocks();
      
      // 启动实时数据更新
      this.startRealTimeUpdates();
      
      this.initialized = true;
      console.log('✅ 市场数据服务初始化完成');
      
    } catch (error) {
      console.error('❌ 市场数据服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化股票基础数据
   */
  async initializeStocks() {
    // A股主要股票数据
    const stocksData = [
      // 银行股
      { symbol: '000001', name: '平安银行', sector: '银行', industry: '股份制银行', market: 'SZ' },
      { symbol: '000002', name: '万科A', sector: '房地产', industry: '住宅开发', market: 'SZ' },
      { symbol: '600000', name: '浦发银行', sector: '银行', industry: '股份制银行', market: 'SH' },
      { symbol: '600036', name: '招商银行', sector: '银行', industry: '股份制银行', market: 'SH' },
      { symbol: '000858', name: '五粮液', sector: '食品饮料', industry: '白酒', market: 'SZ' },
      
      // 科技股
      { symbol: '000063', name: '中兴通讯', sector: '通信', industry: '通信设备', market: 'SZ' },
      { symbol: '002415', name: '海康威视', sector: '电子', industry: '安防设备', market: 'SZ' },
      { symbol: '600519', name: '贵州茅台', sector: '食品饮料', industry: '白酒', market: 'SH' },
      { symbol: '600276', name: '恒瑞医药', sector: '医药生物', industry: '化学制药', market: 'SH' },
      { symbol: '000858', name: '五粮液', sector: '食品饮料', industry: '白酒', market: 'SZ' },
      
      // 新能源
      { symbol: '300750', name: '宁德时代', sector: '电气设备', industry: '电池', market: 'SZ' },
      { symbol: '002594', name: '比亚迪', sector: '汽车', industry: '新能源汽车', market: 'SZ' },
      { symbol: '688981', name: '中芯国际', sector: '电子', industry: '半导体', market: 'SH' },
      
      // 互联网科技
      { symbol: '00700', name: '腾讯控股', sector: '传媒', industry: '互联网服务', market: 'HK' },
      { symbol: '09988', name: '阿里巴巴-SW', sector: '传媒', industry: '电子商务', market: 'HK' },
      { symbol: '09618', name: '京东集团-SW', sector: '传媒', industry: '电子商务', market: 'HK' }
    ];

    // 为每只股票生成初始数据
    for (const stockInfo of stocksData) {
      const basePrice = this.generateBasePrice(stockInfo);
      const stock = {
        ...stockInfo,
        price: basePrice,
        prevClose: basePrice * (0.98 + Math.random() * 0.04), // 前收盘价
        open: basePrice * (0.99 + Math.random() * 0.02),      // 开盘价
        high: basePrice * (1.01 + Math.random() * 0.05),      // 最高价
        low: basePrice * (0.95 + Math.random() * 0.04),       // 最低价
        volume: Math.floor(Math.random() * 10000000) + 1000000, // 成交量
        turnover: 0, // 成交额，将在后面计算
        marketCap: 0, // 市值
        pe: 15 + Math.random() * 30, // 市盈率
        pb: 1 + Math.random() * 5,   // 市净率
        dividend: Math.random() * 0.05, // 股息率
        lastUpdate: new Date().toISOString()
      };
      
      // 计算衍生数据
      stock.change = stock.price - stock.prevClose;
      stock.changePercent = (stock.change / stock.prevClose) * 100;
      stock.turnover = stock.volume * stock.price;
      stock.marketCap = stock.price * 1000000000; // 假设10亿股本
      
      this.stocks.set(stock.symbol, stock);
      this.realTimeData.set(stock.symbol, stock);
    }

    console.log(`📈 已加载 ${this.stocks.size} 只股票的基础数据`);
  }

  /**
   * 根据股票信息生成合理的基价
   */
  generateBasePrice(stockInfo) {
    const priceRanges = {
      '银行': [8, 25],
      '房地产': [5, 40],
      '食品饮料': [50, 2000],
      '医药生物': [30, 200],
      '电气设备': [100, 500],
      '汽车': [80, 300],
      '电子': [20, 150],
      '传媒': [200, 800]
    };

    const range = priceRanges[stockInfo.sector] || [10, 100];
    return range[0] + Math.random() * (range[1] - range[0]);
  }

  /**
   * 启动实时数据更新
   */
  startRealTimeUpdates() {
    // 每3秒更新一次实时数据
    this.updateInterval = setInterval(() => {
      this.updateRealTimeData();
    }, 3000);

    console.log('📡 实时数据更新已启动 (3秒间隔)');
  }

  /**
   * 更新实时数据
   */
  updateRealTimeData() {
    for (const [symbol, stock] of this.realTimeData) {
      // 生成价格波动
      const volatility = 0.002; // 0.2% 波动率
      const priceChange = (Math.random() - 0.5) * 2 * volatility * stock.price;
      const newPrice = Math.max(0.01, stock.price + priceChange);
      
      // 更新股票数据
      const updatedStock = {
        ...stock,
        price: Number(newPrice.toFixed(2)),
        high: Math.max(stock.high, newPrice),
        low: Math.min(stock.low, newPrice),
        volume: stock.volume + Math.floor(Math.random() * 10000),
        lastUpdate: new Date().toISOString()
      };
      
      // 重新计算衍生数据
      updatedStock.change = updatedStock.price - updatedStock.prevClose;
      updatedStock.changePercent = Number(((updatedStock.change / updatedStock.prevClose) * 100).toFixed(2));
      updatedStock.turnover = Number((updatedStock.volume * updatedStock.price).toFixed(2));
      
      this.realTimeData.set(symbol, updatedStock);
      
      // 发送实时数据更新事件
      this.emit('priceUpdate', {
        symbol,
        data: updatedStock
      });
    }
  }

  /**
   * 获取实时行情
   */
  async getQuotes(symbols) {
    if (symbols && symbols.length > 0) {
      return symbols
        .map(symbol => this.realTimeData.get(symbol))
        .filter(Boolean);
    }
    
    return Array.from(this.realTimeData.values());
  }

  /**
   * 获取热门股票行情
   */
  async getTopQuotes(limit = 20) {
    const allStocks = Array.from(this.realTimeData.values());
    
    // 按成交额排序
    return allStocks
      .sort((a, b) => b.turnover - a.turnover)
      .slice(0, limit);
  }

  /**
   * 搜索股票
   */
  async searchStocks(query, limit = 10) {
    const allStocks = Array.from(this.stocks.values());
    const lowerQuery = query.toLowerCase();
    
    return allStocks
      .filter(stock => 
        stock.symbol.toLowerCase().includes(lowerQuery) ||
        stock.name.toLowerCase().includes(lowerQuery) ||
        stock.sector.includes(query) ||
        stock.industry.includes(query)
      )
      .slice(0, limit);
  }

  /**
   * 获取股票详细信息
   */
  async getStockDetail(symbol) {
    const stock = this.realTimeData.get(symbol);
    if (!stock) return null;

    // 添加更多详细信息
    return {
      ...stock,
      fundamentals: {
        totalShares: 1000000000, // 总股本
        floatShares: 800000000,  // 流通股本
        totalAssets: stock.marketCap * 2, // 总资产
        totalLiability: stock.marketCap * 0.6, // 总负债
        revenue: stock.marketCap * 0.3, // 营收
        netProfit: stock.marketCap * 0.05 // 净利润
      },
      technicalIndicators: {
        ma5: stock.price * (0.98 + Math.random() * 0.04),
        ma10: stock.price * (0.96 + Math.random() * 0.08),
        ma20: stock.price * (0.94 + Math.random() * 0.12),
        rsi: 30 + Math.random() * 40,
        macd: (Math.random() - 0.5) * 2,
        kdj: {
          k: Math.random() * 100,
          d: Math.random() * 100,
          j: Math.random() * 100
        }
      }
    };
  }

  /**
   * 获取K线数据
   */
  async getKlineData(symbol, period = '1d', limit = 100, startTime, endTime) {
    const stock = this.stocks.get(symbol);
    if (!stock) return [];

    const cacheKey = `${symbol}_${period}_${limit}`;
    
    // 检查缓存
    if (this.klineCache.has(cacheKey)) {
      return this.klineCache.get(cacheKey);
    }

    // 生成K线数据
    const klines = this.generateKlineData(stock, period, limit);
    
    // 缓存数据
    this.klineCache.set(cacheKey, klines);
    
    return klines;
  }

  /**
   * 生成K线数据
   */
  generateKlineData(stock, period, limit) {
    const klines = [];
    const basePrice = stock.prevClose;
    let currentPrice = basePrice;
    
    // 计算时间间隔
    const intervals = {
      '1m': 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '30m': 30 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000,
      '1w': 7 * 24 * 60 * 60 * 1000
    };
    
    const interval = intervals[period] || intervals['1d'];
    const now = new Date();
    
    for (let i = limit - 1; i >= 0; i--) {
      const timestamp = new Date(now.getTime() - i * interval);
      
      // 生成OHLCV数据
      const open = currentPrice;
      const volatility = 0.02; // 2% 日波动率
      const change = (Math.random() - 0.5) * 2 * volatility * open;
      const close = Math.max(0.01, open + change);
      
      const high = Math.max(open, close) * (1 + Math.random() * 0.03);
      const low = Math.min(open, close) * (1 - Math.random() * 0.03);
      const volume = Math.floor(Math.random() * 5000000) + 100000;
      
      klines.push({
        timestamp: timestamp.toISOString(),
        open: Number(open.toFixed(2)),
        high: Number(high.toFixed(2)),
        low: Number(low.toFixed(2)),
        close: Number(close.toFixed(2)),
        volume,
        turnover: Number((volume * close).toFixed(2))
      });
      
      currentPrice = close;
    }
    
    return klines;
  }

  /**
   * 获取市场概览
   */
  async getMarketOverview() {
    const allStocks = Array.from(this.realTimeData.values());
    
    const gainers = allStocks.filter(s => s.changePercent > 0).length;
    const losers = allStocks.filter(s => s.changePercent < 0).length;
    const unchanged = allStocks.length - gainers - losers;
    
    const totalTurnover = allStocks.reduce((sum, s) => sum + s.turnover, 0);
    const avgChange = allStocks.reduce((sum, s) => sum + s.changePercent, 0) / allStocks.length;
    
    return {
      indices: {
        'SH000001': {
          name: '上证指数',
          value: 3200 + Math.random() * 400,
          change: (Math.random() - 0.5) * 100,
          changePercent: (Math.random() - 0.5) * 3
        },
        'SZ399001': {
          name: '深证成指',
          value: 11000 + Math.random() * 2000,
          change: (Math.random() - 0.5) * 200,
          changePercent: (Math.random() - 0.5) * 3
        },
        'SZ399006': {
          name: '创业板指',
          value: 2400 + Math.random() * 400,
          change: (Math.random() - 0.5) * 80,
          changePercent: (Math.random() - 0.5) * 4
        }
      },
      summary: {
        totalStocks: allStocks.length,
        gainers,
        losers,
        unchanged,
        totalTurnover: Number(totalTurnover.toFixed(2)),
        avgChangePercent: Number(avgChange.toFixed(2))
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 获取板块数据
   */
  async getSectors() {
    const allStocks = Array.from(this.realTimeData.values());
    const sectorMap = new Map();
    
    // 按板块分组
    for (const stock of allStocks) {
      if (!sectorMap.has(stock.sector)) {
        sectorMap.set(stock.sector, []);
      }
      sectorMap.get(stock.sector).push(stock);
    }
    
    // 计算板块数据
    const sectors = [];
    for (const [sectorName, stocks] of sectorMap) {
      const totalTurnover = stocks.reduce((sum, s) => sum + s.turnover, 0);
      const avgChange = stocks.reduce((sum, s) => sum + s.changePercent, 0) / stocks.length;
      
      sectors.push({
        name: sectorName,
        stockCount: stocks.length,
        avgChange: Number(avgChange.toFixed(2)),
        totalTurnover: Number(totalTurnover.toFixed(2)),
        topStocks: stocks
          .sort((a, b) => b.turnover - a.turnover)
          .slice(0, 3)
          .map(s => ({ symbol: s.symbol, name: s.name, change: s.changePercent }))
      });
    }
    
    return sectors.sort((a, b) => b.totalTurnover - a.totalTurnover);
  }

  /**
   * 获取涨跌排行榜
   */
  async getRanking(type = 'gainers', limit = 20) {
    const allStocks = Array.from(this.realTimeData.values());
    
    let sortedStocks;
    switch (type) {
      case 'gainers':
        sortedStocks = allStocks.sort((a, b) => b.changePercent - a.changePercent);
        break;
      case 'losers':
        sortedStocks = allStocks.sort((a, b) => a.changePercent - b.changePercent);
        break;
      case 'volume':
        sortedStocks = allStocks.sort((a, b) => b.volume - a.volume);
        break;
      case 'turnover':
        sortedStocks = allStocks.sort((a, b) => b.turnover - a.turnover);
        break;
      default:
        sortedStocks = allStocks.sort((a, b) => b.changePercent - a.changePercent);
    }
    
    return sortedStocks.slice(0, limit);
  }

  /**
   * 获取深度数据
   */
  async getDepthData(symbol) {
    const stock = this.realTimeData.get(symbol);
    if (!stock) return null;

    // 生成买卖盘数据
    const bids = [];
    const asks = [];
    
    // 生成5档买盘
    for (let i = 0; i < 5; i++) {
      const price = stock.price * (1 - (i + 1) * 0.001);
      const volume = Math.floor(Math.random() * 50000) + 1000;
      bids.push({
        price: Number(price.toFixed(2)),
        volume,
        orders: Math.floor(Math.random() * 20) + 1
      });
    }
    
    // 生成5档卖盘
    for (let i = 0; i < 5; i++) {
      const price = stock.price * (1 + (i + 1) * 0.001);
      const volume = Math.floor(Math.random() * 50000) + 1000;
      asks.push({
        price: Number(price.toFixed(2)),
        volume,
        orders: Math.floor(Math.random() * 20) + 1
      });
    }
    
    return {
      symbol,
      timestamp: new Date().toISOString(),
      bids: bids.reverse(), // 买盘价格从高到低
      asks, // 卖盘价格从低到高
      spread: asks[0].price - bids[0].price,
      spreadPercent: ((asks[0].price - bids[0].price) / stock.price * 100).toFixed(3)
    };
  }

  /**
   * 停止服务
   */
  stop() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    console.log('🛑 市场数据服务已停止');
  }
}