import { createSignal, createEffect, Show, For } from 'solid-js';
import { useAtom, useAtomValue } from 'jotai';
import { 
  currentStrategyAtom, 
  addStrategyAtom, 
  updateStrategyAtom,
  strategyTemplatesAtom,
  createStrategyFromTemplateAtom
} from '@/stores/strategy';
import { css } from '../../styled-system/css';
import { FiPlay, FiSave, FiCode, FiZap, FiCopy, FiTrash2 } from 'solid-icons/fi';

export default function AIStrategyEditor() {
  const [currentStrategy, setCurrentStrategy] = useAtom(currentStrategyAtom);
  const [, addStrategy] = useAtom(addStrategyAtom);
  const [, updateStrategy] = useAtom(updateStrategyAtom);
  const [, createFromTemplate] = useAtom(createStrategyFromTemplateAtom);
  const templates = useAtomValue(strategyTemplatesAtom);
  
  const [code, setCode] = createSignal('');
  const [name, setName] = createSignal('');
  const [description, setDescription] = createSignal('');
  const [aiPrompt, setAiPrompt] = createSignal('');
  const [isGenerating, setIsGenerating] = createSignal(false);
  const [showTemplates, setShowTemplates] = createSignal(false);
  
  // 同步当前策略到编辑器
  createEffect(() => {
    const strategy = currentStrategy();
    if (strategy) {
      setCode(strategy.code);
      setName(strategy.name);
      setDescription(strategy.description);
    } else {
      setCode('');
      setName('');
      setDescription('');
    }
  });
  
  // AI策略生成（模拟）
  const generateAIStrategy = async () => {
    const prompt = aiPrompt();
    if (!prompt.trim()) return;
    
    setIsGenerating(true);
    
    try {
      // 模拟AI生成延迟
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 模拟生成的策略代码
      const generatedCode = `# AI生成的策略: ${prompt}
def initialize(context):
    # 基于您的描述: "${prompt}"
    context.symbol = 'AAPL'
    context.lookback_period = 20
    context.threshold = 0.02

def handle_data(context, data):
    # 获取历史价格数据
    prices = data.history(context.symbol, 'price', context.lookback_period, '1d')
    
    # 计算简单移动平均
    sma = prices.mean()
    current_price = data.current(context.symbol, 'price')
    
    # 获取当前持仓
    current_position = context.portfolio.positions[context.symbol].amount
    
    # 基于AI分析的交易逻辑
    if current_price > sma * (1 + context.threshold) and current_position <= 0:
        # 价格突破均线，买入
        order_target_percent(context.symbol, 1.0)
        log.info(f"AI买入信号: 价格({current_price:.2f}) > SMA({sma:.2f})")
    elif current_price < sma * (1 - context.threshold) and current_position > 0:
        # 价格跌破均线，卖出
        order_target_percent(context.symbol, 0.0)
        log.info(f"AI卖出信号: 价格({current_price:.2f}) < SMA({sma:.2f})")
`;
      
      setCode(generatedCode);
      setName(`AI策略 - ${prompt.slice(0, 20)}...`);
      setDescription(`基于AI分析生成的策略: ${prompt}`);
      
    } catch (error) {
      console.error('AI策略生成失败:', error);
    } finally {
      setIsGenerating(false);
    }
  };
  
  // 保存策略
  const saveStrategy = () => {
    const strategyData = {
      name: name() || '未命名策略',
      description: description() || '暂无描述',
      code: code(),
      language: 'python' as const,
      parameters: {},
      author: '当前用户',
      tags: ['自定义'],
      isPublic: false
    };
    
    if (currentStrategy()) {
      updateStrategy(currentStrategy()!.id, strategyData);
    } else {
      const newStrategy = addStrategy(strategyData);
      setCurrentStrategy(newStrategy);
    }
  };
  
  // 从模板创建策略
  const useTemplate = (templateId: string) => {
    const newStrategy = createFromTemplate(templateId);
    setCurrentStrategy(newStrategy);
    setShowTemplates(false);
  };
  
  // 运行策略（模拟）
  const runStrategy = () => {
    console.log('运行策略:', { name: name(), code: code() });
    // 这里会连接到回测引擎
  };
  
  return (
    <div class={css({
      display: 'flex',
      flexDirection: 'column',
      h: 'full',
      bg: 'white',
      rounded: 'xl',
      border: '1px solid',
      borderColor: 'gray.200',
      overflow: 'hidden',
      _dark: {
        bg: 'gray.800',
        borderColor: 'gray.700'
      }
    })}>
      {/* 编辑器头部 */}
      <div class={css({
        px: 4,
        py: 3,
        borderBottom: '1px solid',
        borderColor: 'gray.200',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        _dark: {
          borderColor: 'gray.700'
        }
      })}>
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          gap: 3
        })}>
          <FiCode size={20} class={css({ color: 'primary.500' })} />
          <h3 class={css({
            fontSize: 'lg',
            fontWeight: 'semibold',
            color: 'gray.900',
            _dark: { color: 'gray.100' }
          })}>
            AI策略编辑器
          </h3>
        </div>
        
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          gap: 2
        })}>
          <button
            onClick={() => setShowTemplates(!showTemplates())}
            class={css({
              px: 3,
              py: 1.5,
              fontSize: 'sm',
              bg: 'gray.100',
              color: 'gray.700',
              rounded: 'md',
              transition: 'all 0.2s ease',
              _hover: {
                bg: 'gray.200'
              },
              _dark: {
                bg: 'gray.700',
                color: 'gray.300',
                _hover: {
                  bg: 'gray.600'
                }
              }
            })}
          >
            <FiCopy size={14} class={css({ mr: 1 })} />
            模板
          </button>
          
          <button
            onClick={saveStrategy}
            class={css({
              px: 3,
              py: 1.5,
              fontSize: 'sm',
              bg: 'success.500',
              color: 'white',
              rounded: 'md',
              transition: 'all 0.2s ease',
              _hover: {
                bg: 'success.600'
              }
            })}
          >
            <FiSave size={14} class={css({ mr: 1 })} />
            保存
          </button>
          
          <button
            onClick={runStrategy}
            class={css({
              px: 3,
              py: 1.5,
              fontSize: 'sm',
              bg: 'primary.500',
              color: 'white',
              rounded: 'md',
              transition: 'all 0.2s ease',
              _hover: {
                bg: 'primary.600'
              }
            })}
          >
            <FiPlay size={14} class={css({ mr: 1 })} />
            运行
          </button>
        </div>
      </div>
      
      {/* 策略模板面板 */}
      <Show when={showTemplates()}>
        <div class={css({
          p: 4,
          bg: 'gray.50',
          borderBottom: '1px solid',
          borderColor: 'gray.200',
          _dark: {
            bg: 'gray.900',
            borderColor: 'gray.700'
          }
        })}>
          <h4 class={css({
            fontSize: 'sm',
            fontWeight: 'medium',
            color: 'gray.900',
            mb: 3,
            _dark: { color: 'gray.100' }
          })}>
            策略模板
          </h4>
          
          <div class={css({
            display: 'grid',
            gridTemplateColumns: { base: '1fr', md: 'repeat(2, 1fr)', lg: 'repeat(3, 1fr)' },
            gap: 3
          })}>
            <For each={templates}>
              {(template) => (
                <div
                  class={css({
                    p: 3,
                    bg: 'white',
                    rounded: 'lg',
                    border: '1px solid',
                    borderColor: 'gray.200',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    _hover: {
                      borderColor: 'primary.300',
                      shadow: 'sm'
                    },
                    _dark: {
                      bg: 'gray.800',
                      borderColor: 'gray.700'
                    }
                  })}
                  onClick={() => useTemplate(template.id)}
                >
                  <div class={css({
                    fontWeight: 'medium',
                    fontSize: 'sm',
                    color: 'gray.900',
                    mb: 1,
                    _dark: { color: 'gray.100' }
                  })}>
                    {template.name}
                  </div>
                  <div class={css({
                    fontSize: 'xs',
                    color: 'gray.600',
                    mb: 2,
                    _dark: { color: 'gray.400' }
                  })}>
                    {template.description}
                  </div>
                  <div class={css({
                    fontSize: 'xs',
                    color: 'primary.600',
                    fontWeight: 'medium'
                  })}>
                    {template.category}
                  </div>
                </div>
              )}
            </For>
          </div>
        </div>
      </Show>
      
      {/* AI助手面板 */}
      <div class={css({
        p: 4,
        bg: 'gradient-to-r',
        gradientFrom: 'primary.50',
        gradientTo: 'purple.50',
        borderBottom: '1px solid',
        borderColor: 'gray.200',
        _dark: {
          gradientFrom: 'primary.900/20',
          gradientTo: 'purple.900/20',
          borderColor: 'gray.700'
        }
      })}>
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          mb: 3
        })}>
          <FiZap size={16} class={css({ color: 'primary.500' })} />
          <span class={css({
            fontSize: 'sm',
            fontWeight: 'medium',
            color: 'gray.900',
            _dark: { color: 'gray.100' }
          })}>
            AI策略助手
          </span>
        </div>
        
        <div class={css({
          display: 'flex',
          gap: 2
        })}>
          <input
            type="text"
            placeholder="描述您想要的交易策略，例如：基于RSI的反转策略"
            value={aiPrompt()}
            onInput={(e) => setAiPrompt(e.currentTarget.value)}
            class={css({
              flex: 1,
              px: 3,
              py: 2,
              border: '1px solid',
              borderColor: 'gray.300',
              rounded: 'md',
              fontSize: 'sm',
              _focus: {
                outline: 'none',
                borderColor: 'primary.500',
                ring: '2px',
                ringColor: 'primary.200'
              },
              _dark: {
                bg: 'gray.700',
                borderColor: 'gray.600',
                color: 'gray.100'
              }
            })}
          />
          
          <button
            onClick={generateAIStrategy}
            disabled={isGenerating() || !aiPrompt().trim()}
            class={css({
              px: 4,
              py: 2,
              bg: 'primary.500',
              color: 'white',
              rounded: 'md',
              fontSize: 'sm',
              fontWeight: 'medium',
              transition: 'all 0.2s ease',
              _hover: {
                bg: 'primary.600'
              },
              _disabled: {
                opacity: 0.5,
                cursor: 'not-allowed'
              }
            })}
          >
            {isGenerating() ? '生成中...' : '生成策略'}
          </button>
        </div>
      </div>
      
      {/* 策略信息 */}
      <div class={css({
        p: 4,
        borderBottom: '1px solid',
        borderColor: 'gray.200',
        _dark: {
          borderColor: 'gray.700'
        }
      })}>
        <div class={css({
          display: 'grid',
          gridTemplateColumns: { base: '1fr', md: '1fr 2fr' },
          gap: 4
        })}>
          <div>
            <label class={css({
              display: 'block',
              fontSize: 'sm',
              fontWeight: 'medium',
              color: 'gray.700',
              mb: 1,
              _dark: { color: 'gray.300' }
            })}>
              策略名称
            </label>
            <input
              type="text"
              value={name()}
              onInput={(e) => setName(e.currentTarget.value)}
              placeholder="输入策略名称"
              class={css({
                w: 'full',
                px: 3,
                py: 2,
                border: '1px solid',
                borderColor: 'gray.300',
                rounded: 'md',
                fontSize: 'sm',
                _focus: {
                  outline: 'none',
                  borderColor: 'primary.500',
                  ring: '2px',
                  ringColor: 'primary.200'
                },
                _dark: {
                  bg: 'gray.700',
                  borderColor: 'gray.600',
                  color: 'gray.100'
                }
              })}
            />
          </div>
          
          <div>
            <label class={css({
              display: 'block',
              fontSize: 'sm',
              fontWeight: 'medium',
              color: 'gray.700',
              mb: 1,
              _dark: { color: 'gray.300' }
            })}>
              策略描述
            </label>
            <input
              type="text"
              value={description()}
              onInput={(e) => setDescription(e.currentTarget.value)}
              placeholder="输入策略描述"
              class={css({
                w: 'full',
                px: 3,
                py: 2,
                border: '1px solid',
                borderColor: 'gray.300',
                rounded: 'md',
                fontSize: 'sm',
                _focus: {
                  outline: 'none',
                  borderColor: 'primary.500',
                  ring: '2px',
                  ringColor: 'primary.200'
                },
                _dark: {
                  bg: 'gray.700',
                  borderColor: 'gray.600',
                  color: 'gray.100'
                }
              })}
            />
          </div>
        </div>
      </div>
      
      {/* 代码编辑器 */}
      <div class={css({
        flex: 1,
        display: 'flex',
        flexDirection: 'column'
      })}>
        <textarea
          value={code()}
          onInput={(e) => setCode(e.currentTarget.value)}
          placeholder="在此输入您的策略代码..."
          class={css({
            flex: 1,
            p: 4,
            border: 'none',
            outline: 'none',
            fontFamily: 'mono',
            fontSize: 'sm',
            lineHeight: 1.5,
            resize: 'none',
            bg: 'gray.50',
            color: 'gray.900',
            _dark: {
              bg: 'gray.900',
              color: 'gray.100'
            }
          })}
        />
      </div>
    </div>
  );
}
