## 6.0.2 (2025-06-19)

### Bug fixes

Make sure to include a .d.cts file to make TypeScript happy.
## 6.0.1 (2022-06-30)

### Bug fixes

Work around limitations in tree-shaking software that prevented `basicSetup` from being removed when unused.

## 6.0.0 (2022-06-08)

### Breaking changes

Change the package name from `@codemirror/example-setup` to just `codemirror`.

The package no longer exports `EditorState` (since that is no longer necessary to set up a basic editor).

### New features

The new `minimalSetup` export provides a minimal set of editor extensions.

## 0.20.0 (2022-04-20)

### Breaking changes

Update dependencies to 0.20.0

## 0.19.3 (2022-03-30)

### New features

Add the extension that shows a crosshair cursor when Alt is held down to the basic setup.

## 0.19.1 (2021-12-13)

### New features

The basic setup now includes the `dropCursor` extension.

## 0.19.0 (2021-08-11)

### Breaking changes

Update dependencies to 0.19.0

## 0.18.2 (2021-05-25)

### Bug fixes

Fix too-low dependency on @codemirror/gutter that could cause broken upgrades.

## 0.18.1 (2021-05-15)

### New features

The basic setup now includes `highlightActiveLineGutter` from @codemirror/gutter.

## 0.18.0 (2021-03-03)

### Breaking changes

Update dependencies to 0.18.

## 0.17.1 (2021-01-06)

### Bug fixes

Putting a theme after the basic setup no longer causes the default highlighter to be used with the theme's editor styling.

### New features

The package now also exports a CommonJS module.

## 0.17.0 (2020-12-29)

### Breaking changes

First numbered release.

