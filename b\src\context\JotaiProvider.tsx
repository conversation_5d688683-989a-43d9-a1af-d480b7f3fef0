/**
 * SolidJS 状态管理 Provider
 * 由于 Jotai 主要为 React 设计，这里提供一个 SolidJS 兼容的状态管理方案
 */

import { JSX, createContext, useContext } from 'solid-js';

interface StateProviderProps {
  children: JSX.Element;
}

// 创建一个简单的状态上下文
const StateContext = createContext();

export default function JotaiProvider(props: StateProviderProps) {
  // 在 SolidJS 中，我们使用现有的 stores 系统
  // 这个 Provider 主要是为了保持 API 一致性
  return (
    <StateContext.Provider value={{}}>
      {props.children}
    </StateContext.Provider>
  );
}

export function useStateContext() {
  return useContext(StateContext);
}
