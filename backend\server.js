/**
 * 量化投资平台后端API服务
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs-extra';

// 路由模块
import authRoutes from './routes/auth.js';
import marketRoutes from './routes/market.js';
import tradingRoutes from './routes/trading.js';
import strategyRoutes from './routes/strategy.js';
import backtestRoutes from './routes/backtest.js';
import portfolioRoutes from './routes/portfolio.js';
import riskRoutes from './routes/risk.js';
import userRoutes from './routes/user.js';

// 服务模块
import { MarketDataService } from './services/marketData.js';
import { WebSocketService } from './services/websocket.js';
import { DataLoader } from './services/dataLoader.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 8000;
const HOST = process.env.HOST || '127.0.0.1';

// 创建HTTP服务器
const server = createServer(app);

// 安全中间件
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "ws://localhost:*", "wss://localhost:*"]
    }
  }
}));

// 基础中间件
app.use(compression());
app.use(cors({
  origin: ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:3000'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 初始化服务
const marketDataService = new MarketDataService();
const dataLoader = new DataLoader();

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    services: {
      api: 'running',
      websocket: 'running',
      database: 'mock'
    }
  });
});

// API路由
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/market', marketRoutes(marketDataService));
app.use('/api/v1/trading', tradingRoutes);
app.use('/api/v1/strategy', strategyRoutes);
app.use('/api/v1/backtest', backtestRoutes(dataLoader));
app.use('/api/v1/portfolio', portfolioRoutes);
app.use('/api/v1/risk', riskRoutes);
app.use('/api/v1/user', userRoutes);

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `API端点 ${req.originalUrl} 未找到`,
    availableEndpoints: [
      '/health',
      '/api/v1/auth',
      '/api/v1/market',
      '/api/v1/trading',
      '/api/v1/strategy',
      '/api/v1/backtest',
      '/api/v1/portfolio',
      '/api/v1/risk',
      '/api/v1/user'
    ]
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  
  res.status(error.status || 500).json({
    success: false,
    message: error.message || '服务器内部错误',
    timestamp: new Date().toISOString(),
    requestId: req.headers['x-request-id'] || 'unknown'
  });
});

// 启动服务器
async function startServer() {
  try {
    console.log('🚀 正在启动量化投资平台后端服务...');
    
    // 初始化数据加载器
    console.log('📊 正在初始化数据服务...');
    await dataLoader.initialize();
    
    // 初始化市场数据服务
    console.log('📈 正在启动市场数据服务...');
    await marketDataService.initialize();
    
    // 启动HTTP服务器
    server.listen(PORT, HOST, () => {
      console.log(`✅ HTTP服务器已启动: http://${HOST}:${PORT}`);
      console.log(`📊 API文档: http://${HOST}:${PORT}/health`);
      console.log('🔗 可用的API端点:');
      console.log('   - GET  /health                    - 健康检查');
      console.log('   - POST /api/v1/auth/login         - 用户登录');
      console.log('   - GET  /api/v1/market/quotes      - 实时行情');
      console.log('   - GET  /api/v1/market/kline/{symbol} - K线数据');
      console.log('   - GET  /api/v1/trading/positions  - 持仓查询');
      console.log('   - POST /api/v1/trading/order      - 下单交易');
      console.log('   - GET  /api/v1/backtest          - 回测列表');
      console.log('   - POST /api/v1/backtest          - 创建回测');
    });
    
    // 启动WebSocket服务
    const wsService = new WebSocketService(server, marketDataService);
    await wsService.initialize();
    
    console.log('🌐 WebSocket服务已启动');
    console.log('📡 实时数据推送已开启');
    console.log('');
    console.log('🎉 量化投资平台后端服务启动成功！');
    console.log('💡 现在可以启动前端应用进行测试');
    
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('🛑 收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已优雅关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已优雅关闭');
    process.exit(0);
  });
});

// 启动服务器
startServer();