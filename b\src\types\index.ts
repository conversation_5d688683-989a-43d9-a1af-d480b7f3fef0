// 通用类型定义

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 市场数据相关类型
export interface MarketQuote {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap?: number;
  pe?: number;
  high52w?: number;
  low52w?: number;
}

export interface OrderBookEntry {
  price: number;
  size: number;
  total: number;
}

export interface OrderBook {
  symbol: string;
  bids: OrderBookEntry[];
  asks: OrderBookEntry[];
  timestamp: number;
}

// 图表相关类型
export interface ChartConfig {
  symbol: string;
  interval: '1m' | '5m' | '15m' | '30m' | '1h' | '4h' | '1d' | '1w' | '1M';
  indicators: string[];
  theme: 'light' | 'dark';
}

export interface TechnicalIndicator {
  name: string;
  type: 'overlay' | 'oscillator';
  parameters: Record<string, number>;
  visible: boolean;
  color: string;
}

// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  role: 'user' | 'admin';
  preferences: UserPreferences;
  createdAt: number;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  defaultSymbol: string;
  notifications: {
    priceAlerts: boolean;
    strategyUpdates: boolean;
    systemMessages: boolean;
  };
  trading: {
    confirmOrders: boolean;
    defaultQuantity: number;
    riskLevel: 'conservative' | 'moderate' | 'aggressive';
  };
}

// 通知相关类型
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: number;
  read: boolean;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  type: 'primary' | 'secondary';
}

// 错误处理类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
  stack?: string;
}

// 性能监控类型
export interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  networkLatency: number;
  errorCount: number;
  timestamp: number;
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
}

// 导出所有市场和策略相关类型
// 简化版本不需要这些导出
// export * from '../stores/market';
// export * from '../stores/strategy';
