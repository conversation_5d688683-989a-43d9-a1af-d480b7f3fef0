/**
 * 风险管理路由
 */
import express from 'express';
import { authenticateToken } from './auth.js';

const router = express.Router();

/**
 * 获取风险概览
 */
router.get('/overview', authenticateToken, async (req, res) => {
  try {
    const riskOverview = {
      riskLevel: 'medium',
      riskScore: 65,
      var: {
        oneDay: 0.025,
        fiveDay: 0.056,
        tenDay: 0.089
      },
      concentration: {
        topStock: 0.149,
        topSector: 0.256,
        topIndustry: 0.178
      },
      leverage: 0.0,
      beta: 1.15,
      tracking_error: 0.045,
      information_ratio: 1.23,
      alerts: [
        {
          id: 'alert_001',
          level: 'warning',
          type: 'concentration',
          message: '单一股票持仓比例较高',
          details: '招商银行持仓占比14.9%，建议控制在15%以内',
          timestamp: new Date().toISOString()
        },
        {
          id: 'alert_002',
          level: 'info',
          type: 'volatility',
          message: '组合波动率上升',
          details: '近期组合波动率上升至18%，高于历史平均值',
          timestamp: new Date().toISOString()
        }
      ]
    };
    
    res.json({
      success: true,
      data: riskOverview
    });
    
  } catch (error) {
    console.error('获取风险概览错误:', error);
    res.status(500).json({
      success: false,
      message: '获取风险概览失败',
      code: 'RISK_OVERVIEW_ERROR'
    });
  }
});

/**
 * 获取风险指标历史
 */
router.get('/metrics', authenticateToken, async (req, res) => {
  try {
    const { period = '1M' } = req.query;
    
    // 生成风险指标历史数据
    const now = new Date();
    const data = [];
    let days;
    
    switch (period) {
      case '1W': days = 7; break;
      case '1M': days = 30; break;
      case '3M': days = 90; break;
      default: days = 30;
    }
    
    for (let i = days; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      
      data.push({
        date: date.toISOString().split('T')[0],
        var: Number((0.02 + Math.random() * 0.03).toFixed(4)),
        beta: Number((1.0 + Math.random() * 0.4).toFixed(2)),
        volatility: Number((0.15 + Math.random() * 0.1).toFixed(4)),
        sharpe: Number((0.5 + Math.random() * 1.5).toFixed(2))
      });
    }
    
    res.json({
      success: true,
      data: {
        period,
        metrics: data
      }
    });
    
  } catch (error) {
    console.error('获取风险指标错误:', error);
    res.status(500).json({
      success: false,
      message: '获取风险指标失败',
      code: 'RISK_METRICS_ERROR'
    });
  }
});

export default router;