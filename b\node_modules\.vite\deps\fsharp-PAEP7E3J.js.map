{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/fsharp/fsharp.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/fsharp/fsharp.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"(*\", \"*)\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*//\\\\s*#region\\\\b|^\\\\s*\\\\(\\\\*\\\\s*#region(.*)\\\\*\\\\)\"),\n      end: new RegExp(\"^\\\\s*//\\\\s*#endregion\\\\b|^\\\\s*\\\\(\\\\*\\\\s*#endregion\\\\s*\\\\*\\\\)\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".fs\",\n  keywords: [\n    \"abstract\",\n    \"and\",\n    \"atomic\",\n    \"as\",\n    \"assert\",\n    \"asr\",\n    \"base\",\n    \"begin\",\n    \"break\",\n    \"checked\",\n    \"component\",\n    \"const\",\n    \"constraint\",\n    \"constructor\",\n    \"continue\",\n    \"class\",\n    \"default\",\n    \"delegate\",\n    \"do\",\n    \"done\",\n    \"downcast\",\n    \"downto\",\n    \"elif\",\n    \"else\",\n    \"end\",\n    \"exception\",\n    \"eager\",\n    \"event\",\n    \"external\",\n    \"extern\",\n    \"false\",\n    \"finally\",\n    \"for\",\n    \"fun\",\n    \"function\",\n    \"fixed\",\n    \"functor\",\n    \"global\",\n    \"if\",\n    \"in\",\n    \"include\",\n    \"inherit\",\n    \"inline\",\n    \"interface\",\n    \"internal\",\n    \"land\",\n    \"lor\",\n    \"lsl\",\n    \"lsr\",\n    \"lxor\",\n    \"lazy\",\n    \"let\",\n    \"match\",\n    \"member\",\n    \"mod\",\n    \"module\",\n    \"mutable\",\n    \"namespace\",\n    \"method\",\n    \"mixin\",\n    \"new\",\n    \"not\",\n    \"null\",\n    \"of\",\n    \"open\",\n    \"or\",\n    \"object\",\n    \"override\",\n    \"private\",\n    \"parallel\",\n    \"process\",\n    \"protected\",\n    \"pure\",\n    \"public\",\n    \"rec\",\n    \"return\",\n    \"static\",\n    \"sealed\",\n    \"struct\",\n    \"sig\",\n    \"then\",\n    \"to\",\n    \"true\",\n    \"tailcall\",\n    \"trait\",\n    \"try\",\n    \"type\",\n    \"upcast\",\n    \"use\",\n    \"val\",\n    \"void\",\n    \"virtual\",\n    \"volatile\",\n    \"when\",\n    \"while\",\n    \"with\",\n    \"yield\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\^%;\\.,\\/]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  integersuffix: /[uU]?[yslnLI]?/,\n  floatsuffix: /[fFmM]?/,\n  tokenizer: {\n    root: [\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      [/\\[<.*>\\]/, \"annotation\"],\n      [/^#(if|else|endif)/, \"keyword\"],\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      [/\\d*\\d+[eE]([\\-+]?\\d+)?(@floatsuffix)/, \"number.float\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?(@floatsuffix)/, \"number.float\"],\n      [/0x[0-9a-fA-F]+LF/, \"number.float\"],\n      [/0x[0-9a-fA-F]+(@integersuffix)/, \"number.hex\"],\n      [/0b[0-1]+(@integersuffix)/, \"number.bin\"],\n      [/\\d+(@integersuffix)/, \"number\"],\n      [/[;,.]/, \"delimiter\"],\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/\"\"\"/, \"string\", '@string.\"\"\"'],\n      [/\"/, \"string\", '@string.\"'],\n      [/\\@\"/, { token: \"string.quote\", next: \"@litstring\" }],\n      [/'[^\\\\']'B?/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\(\\*(?!\\))/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^*(]+/, \"comment\"],\n      [/\\*\\)/, \"comment\", \"@pop\"],\n      [/\\*/, \"comment\"],\n      [/\\(\\*\\)/, \"comment\"],\n      [/\\(/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [\n        /(\"\"\"|\"B?)/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    litstring: [\n      [/[^\"]+/, \"string\"],\n      [/\"\"/, \"string.escape\"],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,wDAAwD;AAAA,MAC1E,KAAK,IAAI,OAAO,8DAA8D;AAAA,IAChF;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AAAA,EACT,SAAS;AAAA,EACT,eAAe;AAAA,EACf,aAAa;AAAA,EACb,WAAW;AAAA,IACT,MAAM;AAAA,MACJ;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,YAAY,YAAY;AAAA,MACzB,CAAC,qBAAqB,SAAS;AAAA,MAC/B,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC,CAAC,YAAY,WAAW;AAAA,MACxB,CAAC,wCAAwC,cAAc;AAAA,MACvD,CAAC,0CAA0C,cAAc;AAAA,MACzD,CAAC,oBAAoB,cAAc;AAAA,MACnC,CAAC,kCAAkC,YAAY;AAAA,MAC/C,CAAC,4BAA4B,YAAY;AAAA,MACzC,CAAC,uBAAuB,QAAQ;AAAA,MAChC,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,OAAO,UAAU,aAAa;AAAA,MAC/B,CAAC,KAAK,UAAU,WAAW;AAAA,MAC3B,CAAC,OAAO,EAAE,OAAO,gBAAgB,MAAM,aAAa,CAAC;AAAA,MACrD,CAAC,cAAc,QAAQ;AAAA,MACvB,CAAC,oBAAoB,CAAC,UAAU,iBAAiB,QAAQ,CAAC;AAAA,MAC1D,CAAC,KAAK,gBAAgB;AAAA,IACxB;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,cAAc,WAAW,UAAU;AAAA,MACpC,CAAC,WAAW,SAAS;AAAA,IACvB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,UAAU,SAAS;AAAA,MACpB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,MAAM,SAAS;AAAA,MAChB,CAAC,UAAU,SAAS;AAAA,MACpB,CAAC,MAAM,SAAS;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,OAAO,CAAC;AAAA,IAC/C;AAAA,EACF;AACF;", "names": []}