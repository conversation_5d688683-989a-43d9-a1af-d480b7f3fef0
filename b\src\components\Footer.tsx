import { css } from '../../styled-system/css';

export default function Footer() {
  return (
    <footer class={css({
      bg: 'white',
      borderTop: '1px solid',
      borderColor: 'gray.200',
      py: 6,
      _dark: {
        bg: 'gray.800',
        borderColor: 'gray.700'
      }
    })}>
      <div class={css({
        container: 'xl',
        mx: 'auto',
        px: 4,
        textAlign: 'center'
      })}>
        <div class={css({
          display: 'flex',
          flexDirection: { base: 'column', md: 'row' },
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: 4
        })}>
          {/* 版权信息 */}
          <div class={css({
            color: 'gray.600',
            fontSize: 'sm',
            _dark: {
              color: 'gray.400'
            }
          })}>
            © 2024 量化交易前端平台. 基于 SolidJS 构建，专注性能与体验.
          </div>
          
          {/* 技术栈信息 */}
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            gap: 4,
            fontSize: 'xs',
            color: 'gray.500',
            _dark: {
              color: 'gray.500'
            }
          })}>
            <span class={css({
              display: 'flex',
              alignItems: 'center',
              gap: 1
            })}>
              <div class={css({
                w: 2,
                h: 2,
                bg: 'primary.500',
                rounded: 'full'
              })} />
              SolidJS
            </span>
            <span class={css({
              display: 'flex',
              alignItems: 'center',
              gap: 1
            })}>
              <div class={css({
                w: 2,
                h: 2,
                bg: 'success.500',
                rounded: 'full'
              })} />
              Jotai
            </span>
            <span class={css({
              display: 'flex',
              alignItems: 'center',
              gap: 1
            })}>
              <div class={css({
                w: 2,
                h: 2,
                bg: 'warning.500',
                rounded: 'full'
              })} />
              Panda CSS
            </span>
          </div>
        </div>
      </div>
    </footer>
  );
}
