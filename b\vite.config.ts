import { defineConfig } from 'vite';
import solidPlugin from 'vite-plugin-solid';
import { visualizer } from 'rollup-plugin-visualizer';
import path from 'path';

export default defineConfig({
  plugins: [
    solidPlugin(),
    visualizer({
      open: false,
      filename: 'dist/bundle-analysis.html',
      gzipSize: true,
      brotliSize: true,
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/pages': path.resolve(__dirname, './src/pages'),
      '@/stores': path.resolve(__dirname, './src/stores'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/context': path.resolve(__dirname, './src/context'),
      '@/workers': path.resolve(__dirname, './src/workers'),
    },
  },
  build: {
    target: 'esnext',
    minify: 'terser',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          // 核心框架
          'solid-core': ['solid-js', '@solidjs/router'],
          // 状态管理
          'state-management': ['jotai'],
          // 图表库
          'charts': ['lightweight-charts', '@observablehq/plot'],
          // 代码编辑器
          'code-editor': [
            'codemirror',
            '@codemirror/lang-javascript',
            '@codemirror/lang-python',
            '@codemirror/theme-one-dark',
            '@codemirror/view',
            '@codemirror/state'
          ],
          // AI功能
          'ai-features': ['@xenova/transformers'],
          // 工具库
          'utilities': ['decimal.js', 'date-fns', 'solid-icons'],
          // Solid原语
          'solid-primitives': ['@solid-primitives/event-bus', '@solid-primitives/storage'],
        },
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || [];
          const extType = info[info.length - 1];
          
          if (/\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(assetInfo.name || '')) {
            return 'assets/images/[name]-[hash].[ext]';
          }
          
          if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name || '')) {
            return 'assets/fonts/[name]-[hash].[ext]';
          }
          
          return `assets/${extType}/[name]-[hash].[ext]`;
        },
      },
    },
    // 构建性能优化
    chunkSizeWarningLimit: 500,
    reportCompressedSize: false,
  },
  server: {
    port: 3000,
    host: '0.0.0.0',
    open: true,
    cors: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '/api/v1'),
      },
    },
  },
  optimizeDeps: {
    include: [
      'solid-js',
      '@solidjs/router',
      'jotai',
      'lightweight-charts',
      'decimal.js',
      'date-fns',
    ],
    exclude: [
      '@xenova/transformers', // AI模型按需加载
    ],
  },
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
  },
});
