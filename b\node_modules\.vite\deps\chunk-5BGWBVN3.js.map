{"version": 3, "sources": ["../../style-mod/src/style-mod.js", "../../w3c-keyname/index.js", "../../crelt/index.js"], "sourcesContent": ["const C = \"\\u037c\"\nconst COUNT = typeof Symbol == \"undefined\" ? \"__\" + C : Symbol.for(C)\nconst SET = typeof Symbol == \"undefined\" ? \"__styleSet\" + Math.floor(Math.random() * 1e8) : Symbol(\"styleSet\")\nconst top = typeof globalThis != \"undefined\" ? globalThis : typeof window != \"undefined\" ? window : {}\n\n// :: - Style modules encapsulate a set of CSS rules defined from\n// JavaScript. Their definitions are only available in a given DOM\n// root after it has been _mounted_ there with `StyleModule.mount`.\n//\n// Style modules should be created once and stored somewhere, as\n// opposed to re-creating them every time you need them. The amount of\n// CSS rules generated for a given DOM root is bounded by the amount\n// of style modules that were used. So to avoid leaking rules, don't\n// create these dynamically, but treat them as one-time allocations.\nexport class StyleModule {\n  // :: (Object<Style>, ?{finish: ?(string) → string})\n  // Create a style module from the given spec.\n  //\n  // When `finish` is given, it is called on regular (non-`@`)\n  // selectors (after `&` expansion) to compute the final selector.\n  constructor(spec, options) {\n    this.rules = []\n    let {finish} = options || {}\n\n    function splitSelector(selector) {\n      return /^@/.test(selector) ? [selector] : selector.split(/,\\s*/)\n    }\n\n    function render(selectors, spec, target, isKeyframes) {\n      let local = [], isAt = /^@(\\w+)\\b/.exec(selectors[0]), keyframes = isAt && isAt[1] == \"keyframes\"\n      if (isAt && spec == null) return target.push(selectors[0] + \";\")\n      for (let prop in spec) {\n        let value = spec[prop]\n        if (/&/.test(prop)) {\n          render(prop.split(/,\\s*/).map(part => selectors.map(sel => part.replace(/&/, sel))).reduce((a, b) => a.concat(b)),\n                 value, target)\n        } else if (value && typeof value == \"object\") {\n          if (!isAt) throw new RangeError(\"The value of a property (\" + prop + \") should be a primitive value.\")\n          render(splitSelector(prop), value, local, keyframes)\n        } else if (value != null) {\n          local.push(prop.replace(/_.*/, \"\").replace(/[A-Z]/g, l => \"-\" + l.toLowerCase()) + \": \" + value + \";\")\n        }\n      }\n      if (local.length || keyframes) {\n        target.push((finish && !isAt && !isKeyframes ? selectors.map(finish) : selectors).join(\", \") +\n                    \" {\" + local.join(\" \") + \"}\")\n      }\n    }\n\n    for (let prop in spec) render(splitSelector(prop), spec[prop], this.rules)\n  }\n\n  // :: () → string\n  // Returns a string containing the module's CSS rules.\n  getRules() { return this.rules.join(\"\\n\") }\n\n  // :: () → string\n  // Generate a new unique CSS class name.\n  static newName() {\n    let id = top[COUNT] || 1\n    top[COUNT] = id + 1\n    return C + id.toString(36)\n  }\n\n  // :: (union<Document, ShadowRoot>, union<[StyleModule], StyleModule>, ?{nonce: ?string})\n  //\n  // Mount the given set of modules in the given DOM root, which ensures\n  // that the CSS rules defined by the module are available in that\n  // context.\n  //\n  // Rules are only added to the document once per root.\n  //\n  // Rule order will follow the order of the modules, so that rules from\n  // modules later in the array take precedence of those from earlier\n  // modules. If you call this function multiple times for the same root\n  // in a way that changes the order of already mounted modules, the old\n  // order will be changed.\n  //\n  // If a Content Security Policy nonce is provided, it is added to\n  // the `<style>` tag generated by the library.\n  static mount(root, modules, options) {\n    let set = root[SET], nonce = options && options.nonce\n    if (!set) set = new StyleSet(root, nonce)\n    else if (nonce) set.setNonce(nonce)\n    set.mount(Array.isArray(modules) ? modules : [modules], root)\n  }\n}\n\nlet adoptedSet = new Map //<Document, StyleSet>\n\nclass StyleSet {\n  constructor(root, nonce) {\n    let doc = root.ownerDocument || root, win = doc.defaultView\n    if (!root.head && root.adoptedStyleSheets && win.CSSStyleSheet) {\n      let adopted = adoptedSet.get(doc)\n      if (adopted) return root[SET] = adopted\n      this.sheet = new win.CSSStyleSheet\n      adoptedSet.set(doc, this)\n    } else {\n      this.styleTag = doc.createElement(\"style\")\n      if (nonce) this.styleTag.setAttribute(\"nonce\", nonce)\n    }\n    this.modules = []\n    root[SET] = this\n  }\n\n  mount(modules, root) {\n    let sheet = this.sheet\n    let pos = 0 /* Current rule offset */, j = 0 /* Index into this.modules */\n    for (let i = 0; i < modules.length; i++) {\n      let mod = modules[i], index = this.modules.indexOf(mod)\n      if (index < j && index > -1) { // Ordering conflict\n        this.modules.splice(index, 1)\n        j--\n        index = -1\n      }\n      if (index == -1) {\n        this.modules.splice(j++, 0, mod)\n        if (sheet) for (let k = 0; k < mod.rules.length; k++)\n          sheet.insertRule(mod.rules[k], pos++)\n      } else {\n        while (j < index) pos += this.modules[j++].rules.length\n        pos += mod.rules.length\n        j++\n      }\n    }\n\n    if (sheet) {\n      if (root.adoptedStyleSheets.indexOf(this.sheet) < 0)\n        root.adoptedStyleSheets = [this.sheet, ...root.adoptedStyleSheets]\n    } else {\n      let text = \"\"\n      for (let i = 0; i < this.modules.length; i++)\n        text += this.modules[i].getRules() + \"\\n\"\n      this.styleTag.textContent = text\n      let target = root.head || root\n      if (this.styleTag.parentNode != target)\n        target.insertBefore(this.styleTag, target.firstChild)\n    }\n  }\n\n  setNonce(nonce) {\n    if (this.styleTag && this.styleTag.getAttribute(\"nonce\") != nonce)\n      this.styleTag.setAttribute(\"nonce\", nonce)\n  }\n}\n\n// Style::Object<union<Style,string>>\n//\n// A style is an object that, in the simple case, maps CSS property\n// names to strings holding their values, as in `{color: \"red\",\n// fontWeight: \"bold\"}`. The property names can be given in\n// camel-case—the library will insert a dash before capital letters\n// when converting them to CSS.\n//\n// If you include an underscore in a property name, it and everything\n// after it will be removed from the output, which can be useful when\n// providing a property multiple times, for browser compatibility\n// reasons.\n//\n// A property in a style object can also be a sub-selector, which\n// extends the current context to add a pseudo-selector or a child\n// selector. Such a property should contain a `&` character, which\n// will be replaced by the current selector. For example `{\"&:before\":\n// {content: '\"hi\"'}}`. Sub-selectors and regular properties can\n// freely be mixed in a given object. Any property containing a `&` is\n// assumed to be a sub-selector.\n//\n// Finally, a property can specify an @-block to be wrapped around the\n// styles defined inside the object that's the property's value. For\n// example to create a media query you can do `{\"@media screen and\n// (min-width: 400px)\": {...}}`.\n", "export var base = {\n  8: \"Backspace\",\n  9: \"Tab\",\n  10: \"Enter\",\n  12: \"NumLock\",\n  13: \"Enter\",\n  16: \"Shift\",\n  17: \"Control\",\n  18: \"Alt\",\n  20: \"CapsLock\",\n  27: \"Escape\",\n  32: \" \",\n  33: \"PageUp\",\n  34: \"PageDown\",\n  35: \"End\",\n  36: \"Home\",\n  37: \"ArrowLeft\",\n  38: \"ArrowUp\",\n  39: \"ArrowRight\",\n  40: \"ArrowDown\",\n  44: \"PrintScreen\",\n  45: \"Insert\",\n  46: \"Delete\",\n  59: \";\",\n  61: \"=\",\n  91: \"Meta\",\n  92: \"Meta\",\n  106: \"*\",\n  107: \"+\",\n  108: \",\",\n  109: \"-\",\n  110: \".\",\n  111: \"/\",\n  144: \"NumLock\",\n  145: \"ScrollLock\",\n  160: \"Shift\",\n  161: \"Shift\",\n  162: \"Control\",\n  163: \"Control\",\n  164: \"Alt\",\n  165: \"Alt\",\n  173: \"-\",\n  186: \";\",\n  187: \"=\",\n  188: \",\",\n  189: \"-\",\n  190: \".\",\n  191: \"/\",\n  192: \"`\",\n  219: \"[\",\n  220: \"\\\\\",\n  221: \"]\",\n  222: \"'\"\n}\n\nexport var shift = {\n  48: \")\",\n  49: \"!\",\n  50: \"@\",\n  51: \"#\",\n  52: \"$\",\n  53: \"%\",\n  54: \"^\",\n  55: \"&\",\n  56: \"*\",\n  57: \"(\",\n  59: \":\",\n  61: \"+\",\n  173: \"_\",\n  186: \":\",\n  187: \"+\",\n  188: \"<\",\n  189: \"_\",\n  190: \">\",\n  191: \"?\",\n  192: \"~\",\n  219: \"{\",\n  220: \"|\",\n  221: \"}\",\n  222: \"\\\"\"\n}\n\nvar mac = typeof navigator != \"undefined\" && /Mac/.test(navigator.platform)\nvar ie = typeof navigator != \"undefined\" && /MSIE \\d|Trident\\/(?:[7-9]|\\d{2,})\\..*rv:(\\d+)/.exec(navigator.userAgent)\n\n// Fill in the digit keys\nfor (var i = 0; i < 10; i++) base[48 + i] = base[96 + i] = String(i)\n\n// The function keys\nfor (var i = 1; i <= 24; i++) base[i + 111] = \"F\" + i\n\n// And the alphabetic keys\nfor (var i = 65; i <= 90; i++) {\n  base[i] = String.fromCharCode(i + 32)\n  shift[i] = String.fromCharCode(i)\n}\n\n// For each code that doesn't have a shift-equivalent, copy the base name\nfor (var code in base) if (!shift.hasOwnProperty(code)) shift[code] = base[code]\n\nexport function keyName(event) {\n  // On macOS, keys held with Shift and Cmd don't reflect the effect of Shift in `.key`.\n  // On IE, shift effect is never included in `.key`.\n  var ignoreKey = mac && event.metaKey && event.shiftKey && !event.ctrlKey && !event.altKey ||\n      ie && event.shiftKey && event.key && event.key.length == 1 ||\n      event.key == \"Unidentified\"\n  var name = (!ignoreKey && event.key) ||\n    (event.shiftKey ? shift : base)[event.keyCode] ||\n    event.key || \"Unidentified\"\n  // Edge sometimes produces wrong names (Issue #3)\n  if (name == \"Esc\") name = \"Escape\"\n  if (name == \"Del\") name = \"Delete\"\n  // https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/8860571/\n  if (name == \"Left\") name = \"ArrowLeft\"\n  if (name == \"Up\") name = \"ArrowUp\"\n  if (name == \"Right\") name = \"ArrowRight\"\n  if (name == \"Down\") name = \"ArrowDown\"\n  return name\n}\n", "export default function crelt() {\n  var elt = arguments[0]\n  if (typeof elt == \"string\") elt = document.createElement(elt)\n  var i = 1, next = arguments[1]\n  if (next && typeof next == \"object\" && next.nodeType == null && !Array.isArray(next)) {\n    for (var name in next) if (Object.prototype.hasOwnProperty.call(next, name)) {\n      var value = next[name]\n      if (typeof value == \"string\") elt.setAttribute(name, value)\n      else if (value != null) elt[name] = value\n    }\n    i++\n  }\n  for (; i < arguments.length; i++) add(elt, arguments[i])\n  return elt\n}\n\nfunction add(elt, child) {\n  if (typeof child == \"string\") {\n    elt.appendChild(document.createTextNode(child))\n  } else if (child == null) {\n  } else if (child.nodeType != null) {\n    elt.appendChild(child)\n  } else if (Array.isArray(child)) {\n    for (var i = 0; i < child.length; i++) add(elt, child[i])\n  } else {\n    throw new RangeError(\"Unsupported child node: \" + child)\n  }\n}\n"], "mappings": ";AAAA,IAAM,IAAI;AACV,IAAM,QAAQ,OAAO,UAAU,cAAc,OAAO,IAAI,OAAO,IAAI,CAAC;AACpE,IAAM,MAAM,OAAO,UAAU,cAAc,eAAe,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG,IAAI,OAAO,UAAU;AAC7G,IAAM,MAAM,OAAO,cAAc,cAAc,aAAa,OAAO,UAAU,cAAc,SAAS,CAAC;AAW9F,IAAM,cAAN,MAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB,YAAY,MAAM,SAAS;AACzB,SAAK,QAAQ,CAAC;AACd,QAAI,EAAC,OAAM,IAAI,WAAW,CAAC;AAE3B,aAAS,cAAc,UAAU;AAC/B,aAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,QAAQ,IAAI,SAAS,MAAM,MAAM;AAAA,IACjE;AAEA,aAAS,OAAO,WAAWA,OAAM,QAAQ,aAAa;AACpD,UAAI,QAAQ,CAAC,GAAG,OAAO,YAAY,KAAK,UAAU,CAAC,CAAC,GAAG,YAAY,QAAQ,KAAK,CAAC,KAAK;AACtF,UAAI,QAAQA,SAAQ,KAAM,QAAO,OAAO,KAAK,UAAU,CAAC,IAAI,GAAG;AAC/D,eAAS,QAAQA,OAAM;AACrB,YAAI,QAAQA,MAAK,IAAI;AACrB,YAAI,IAAI,KAAK,IAAI,GAAG;AAClB;AAAA,YAAO,KAAK,MAAM,MAAM,EAAE,IAAI,UAAQ,UAAU,IAAI,SAAO,KAAK,QAAQ,KAAK,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,CAAC;AAAA,YACzG;AAAA,YAAO;AAAA,UAAM;AAAA,QACtB,WAAW,SAAS,OAAO,SAAS,UAAU;AAC5C,cAAI,CAAC,KAAM,OAAM,IAAI,WAAW,8BAA8B,OAAO,gCAAgC;AACrG,iBAAO,cAAc,IAAI,GAAG,OAAO,OAAO,SAAS;AAAA,QACrD,WAAW,SAAS,MAAM;AACxB,gBAAM,KAAK,KAAK,QAAQ,OAAO,EAAE,EAAE,QAAQ,UAAU,OAAK,MAAM,EAAE,YAAY,CAAC,IAAI,OAAO,QAAQ,GAAG;AAAA,QACvG;AAAA,MACF;AACA,UAAI,MAAM,UAAU,WAAW;AAC7B,eAAO,MAAM,UAAU,CAAC,QAAQ,CAAC,cAAc,UAAU,IAAI,MAAM,IAAI,WAAW,KAAK,IAAI,IAC/E,OAAO,MAAM,KAAK,GAAG,IAAI,GAAG;AAAA,MAC1C;AAAA,IACF;AAEA,aAAS,QAAQ,KAAM,QAAO,cAAc,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK;AAAA,EAC3E;AAAA;AAAA;AAAA,EAIA,WAAW;AAAE,WAAO,KAAK,MAAM,KAAK,IAAI;AAAA,EAAE;AAAA;AAAA;AAAA,EAI1C,OAAO,UAAU;AACf,QAAI,KAAK,IAAI,KAAK,KAAK;AACvB,QAAI,KAAK,IAAI,KAAK;AAClB,WAAO,IAAI,GAAG,SAAS,EAAE;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,OAAO,MAAM,MAAM,SAAS,SAAS;AACnC,QAAI,MAAM,KAAK,GAAG,GAAG,QAAQ,WAAW,QAAQ;AAChD,QAAI,CAAC,IAAK,OAAM,IAAI,SAAS,MAAM,KAAK;AAAA,aAC/B,MAAO,KAAI,SAAS,KAAK;AAClC,QAAI,MAAM,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO,GAAG,IAAI;AAAA,EAC9D;AACF;AAEA,IAAI,aAAa,oBAAI;AAErB,IAAM,WAAN,MAAe;AAAA,EACb,YAAY,MAAM,OAAO;AACvB,QAAI,MAAM,KAAK,iBAAiB,MAAM,MAAM,IAAI;AAChD,QAAI,CAAC,KAAK,QAAQ,KAAK,sBAAsB,IAAI,eAAe;AAC9D,UAAI,UAAU,WAAW,IAAI,GAAG;AAChC,UAAI,QAAS,QAAO,KAAK,GAAG,IAAI;AAChC,WAAK,QAAQ,IAAI,IAAI;AACrB,iBAAW,IAAI,KAAK,IAAI;AAAA,IAC1B,OAAO;AACL,WAAK,WAAW,IAAI,cAAc,OAAO;AACzC,UAAI,MAAO,MAAK,SAAS,aAAa,SAAS,KAAK;AAAA,IACtD;AACA,SAAK,UAAU,CAAC;AAChB,SAAK,GAAG,IAAI;AAAA,EACd;AAAA,EAEA,MAAM,SAAS,MAAM;AACnB,QAAI,QAAQ,KAAK;AACjB,QAAI,MAAM,GAA6B,IAAI;AAC3C,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAI,MAAM,QAAQ,CAAC,GAAG,QAAQ,KAAK,QAAQ,QAAQ,GAAG;AACtD,UAAI,QAAQ,KAAK,QAAQ,IAAI;AAC3B,aAAK,QAAQ,OAAO,OAAO,CAAC;AAC5B;AACA,gBAAQ;AAAA,MACV;AACA,UAAI,SAAS,IAAI;AACf,aAAK,QAAQ,OAAO,KAAK,GAAG,GAAG;AAC/B,YAAI,MAAO,UAAS,IAAI,GAAG,IAAI,IAAI,MAAM,QAAQ;AAC/C,gBAAM,WAAW,IAAI,MAAM,CAAC,GAAG,KAAK;AAAA,MACxC,OAAO;AACL,eAAO,IAAI,MAAO,QAAO,KAAK,QAAQ,GAAG,EAAE,MAAM;AACjD,eAAO,IAAI,MAAM;AACjB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,OAAO;AACT,UAAI,KAAK,mBAAmB,QAAQ,KAAK,KAAK,IAAI;AAChD,aAAK,qBAAqB,CAAC,KAAK,OAAO,GAAG,KAAK,kBAAkB;AAAA,IACrE,OAAO;AACL,UAAI,OAAO;AACX,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ;AACvC,gBAAQ,KAAK,QAAQ,CAAC,EAAE,SAAS,IAAI;AACvC,WAAK,SAAS,cAAc;AAC5B,UAAI,SAAS,KAAK,QAAQ;AAC1B,UAAI,KAAK,SAAS,cAAc;AAC9B,eAAO,aAAa,KAAK,UAAU,OAAO,UAAU;AAAA,IACxD;AAAA,EACF;AAAA,EAEA,SAAS,OAAO;AACd,QAAI,KAAK,YAAY,KAAK,SAAS,aAAa,OAAO,KAAK;AAC1D,WAAK,SAAS,aAAa,SAAS,KAAK;AAAA,EAC7C;AACF;;;ACjJO,IAAI,OAAO;AAAA,EAChB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AAEO,IAAI,QAAQ;AAAA,EACjB,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AAEA,IAAI,MAAM,OAAO,aAAa,eAAe,MAAM,KAAK,UAAU,QAAQ;AAC1E,IAAI,KAAK,OAAO,aAAa,eAAe,gDAAgD,KAAK,UAAU,SAAS;AAGpH,KAAS,IAAI,GAAG,IAAI,IAAI,IAAK,MAAK,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC;AAA1D;AAGT,KAAS,IAAI,GAAG,KAAK,IAAI,IAAK,MAAK,IAAI,GAAG,IAAI,MAAM;AAA3C;AAGT,KAAS,IAAI,IAAI,KAAK,IAAI,KAAK;AAC7B,OAAK,CAAC,IAAI,OAAO,aAAa,IAAI,EAAE;AACpC,QAAM,CAAC,IAAI,OAAO,aAAa,CAAC;AAClC;AAHS;AAMT,KAAS,QAAQ,KAAM,KAAI,CAAC,MAAM,eAAe,IAAI,EAAG,OAAM,IAAI,IAAI,KAAK,IAAI;AAAtE;AAEF,SAAS,QAAQ,OAAO;AAG7B,MAAI,YAAY,OAAO,MAAM,WAAW,MAAM,YAAY,CAAC,MAAM,WAAW,CAAC,MAAM,UAC/E,MAAM,MAAM,YAAY,MAAM,OAAO,MAAM,IAAI,UAAU,KACzD,MAAM,OAAO;AACjB,MAAI,OAAQ,CAAC,aAAa,MAAM,QAC7B,MAAM,WAAW,QAAQ,MAAM,MAAM,OAAO,KAC7C,MAAM,OAAO;AAEf,MAAI,QAAQ,MAAO,QAAO;AAC1B,MAAI,QAAQ,MAAO,QAAO;AAE1B,MAAI,QAAQ,OAAQ,QAAO;AAC3B,MAAI,QAAQ,KAAM,QAAO;AACzB,MAAI,QAAQ,QAAS,QAAO;AAC5B,MAAI,QAAQ,OAAQ,QAAO;AAC3B,SAAO;AACT;;;ACtHe,SAAR,QAAyB;AAC9B,MAAI,MAAM,UAAU,CAAC;AACrB,MAAI,OAAO,OAAO,SAAU,OAAM,SAAS,cAAc,GAAG;AAC5D,MAAI,IAAI,GAAG,OAAO,UAAU,CAAC;AAC7B,MAAI,QAAQ,OAAO,QAAQ,YAAY,KAAK,YAAY,QAAQ,CAAC,MAAM,QAAQ,IAAI,GAAG;AACpF,aAAS,QAAQ,KAAM,KAAI,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AAC3E,UAAI,QAAQ,KAAK,IAAI;AACrB,UAAI,OAAO,SAAS,SAAU,KAAI,aAAa,MAAM,KAAK;AAAA,eACjD,SAAS,KAAM,KAAI,IAAI,IAAI;AAAA,IACtC;AACA;AAAA,EACF;AACA,SAAO,IAAI,UAAU,QAAQ,IAAK,KAAI,KAAK,UAAU,CAAC,CAAC;AACvD,SAAO;AACT;AAEA,SAAS,IAAI,KAAK,OAAO;AACvB,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,YAAY,SAAS,eAAe,KAAK,CAAC;AAAA,EAChD,WAAW,SAAS,MAAM;AAAA,EAC1B,WAAW,MAAM,YAAY,MAAM;AACjC,QAAI,YAAY,KAAK;AAAA,EACvB,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAK,KAAI,KAAK,MAAM,CAAC,CAAC;AAAA,EAC1D,OAAO;AACL,UAAM,IAAI,WAAW,6BAA6B,KAAK;AAAA,EACzD;AACF;", "names": ["spec"]}