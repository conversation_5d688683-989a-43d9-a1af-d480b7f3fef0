/* Panda CSS Generated Styles */

/* Reset and base styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
}

body {
  background-color: var(--colors-gray-50);
  color: var(--colors-gray-900);
  transition: background-color 0.2s ease, color 0.2s ease;
}

body.dark {
  background-color: var(--colors-gray-900);
  color: var(--colors-gray-100);
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: var(--colors-gray-100);
}

::-webkit-scrollbar-thumb {
  background-color: var(--colors-gray-400);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--colors-gray-500);
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    transform: translateY(10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceSubtle {
  0%, 100% {
    transform: translateY(-2px);
  }
  50% {
    transform: translateY(0);
  }
}

/* Utility classes */
.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce-subtle {
  animation: bounceSubtle 1s ease-in-out infinite;
}

/* CSS Variables for colors */
:root {
  --colors-primary-50: #eff6ff;
  --colors-primary-100: #dbeafe;
  --colors-primary-200: #bfdbfe;
  --colors-primary-300: #93c5fd;
  --colors-primary-400: #60a5fa;
  --colors-primary-500: #3b82f6;
  --colors-primary-600: #2563eb;
  --colors-primary-700: #1d4ed8;
  --colors-primary-800: #1e40af;
  --colors-primary-900: #1e3a8a;
  
  --colors-success-50: #f0fdf4;
  --colors-success-100: #dcfce7;
  --colors-success-200: #bbf7d0;
  --colors-success-300: #86efac;
  --colors-success-400: #4ade80;
  --colors-success-500: #22c55e;
  --colors-success-600: #16a34a;
  --colors-success-700: #15803d;
  --colors-success-800: #166534;
  --colors-success-900: #14532d;
  
  --colors-danger-50: #fef2f2;
  --colors-danger-100: #fee2e2;
  --colors-danger-200: #fecaca;
  --colors-danger-300: #fca5a5;
  --colors-danger-400: #f87171;
  --colors-danger-500: #ef4444;
  --colors-danger-600: #dc2626;
  --colors-danger-700: #b91c1c;
  --colors-danger-800: #991b1b;
  --colors-danger-900: #7f1d1d;
  
  --colors-warning-50: #fffbeb;
  --colors-warning-100: #fef3c7;
  --colors-warning-200: #fde68a;
  --colors-warning-300: #fcd34d;
  --colors-warning-400: #fbbf24;
  --colors-warning-500: #f59e0b;
  --colors-warning-600: #d97706;
  --colors-warning-700: #b45309;
  --colors-warning-800: #92400e;
  --colors-warning-900: #78350f;
  
  --colors-gray-50: #f9fafb;
  --colors-gray-100: #f3f4f6;
  --colors-gray-200: #e5e7eb;
  --colors-gray-300: #d1d5db;
  --colors-gray-400: #9ca3af;
  --colors-gray-500: #6b7280;
  --colors-gray-600: #4b5563;
  --colors-gray-700: #374151;
  --colors-gray-800: #1f2937;
  --colors-gray-900: #111827;
  
  --colors-bull: #26a69a;
  --colors-bear: #ef5350;
  --colors-neutral: #78909c;
}
