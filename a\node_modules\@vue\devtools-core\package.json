{"name": "@vue/devtools-core", "type": "module", "version": "7.7.7", "author": "webfansplz", "license": "MIT", "repository": {"directory": "packages/core", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "files": ["dist", "server.d.ts"], "peerDependencies": {"vue": "^3.0.0"}, "dependencies": {"mitt": "^3.0.1", "nanoid": "^5.1.0", "pathe": "^2.0.3", "vite-hot-client": "^2.0.4", "@vue/devtools-kit": "^7.7.7", "@vue/devtools-shared": "^7.7.7"}, "devDependencies": {"vue": "^3.5.13"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}}