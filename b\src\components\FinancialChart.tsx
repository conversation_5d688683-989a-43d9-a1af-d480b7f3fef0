import { createEffect, onCleanup, createSignal, onMount } from 'solid-js';
// import { createChart, ColorType, LineStyle, type IChartApi, type ISeriesApi } from 'lightweight-charts';
import { useAtomValue } from 'jotai';
import { selectedSymbolAtom, selectedTickerAtom } from '@/stores/market';
import { useTheme } from '@/context/ThemeContext';
import { css } from '../../styled-system/css';

interface FinancialChartProps {
  height?: number;
  showVolume?: boolean;
}

export default function FinancialChart(props: FinancialChartProps) {
  const { theme } = useTheme();
  const selectedSymbol = useAtomValue(selectedSymbolAtom);
  const selectedTicker = useAtomValue(selectedTickerAtom);
  
  const [container, setContainer] = createSignal<HTMLDivElement | null>(null);
  const [chart, setChart] = createSignal<IChartApi | null>(null);
  const [candlestickSeries, setCandlestickSeries] = createSignal<ISeriesApi<'Candlestick'> | null>(null);
  const [volumeSeries, setVolumeSeries] = createSignal<ISeriesApi<'Histogram'> | null>(null);
  
  const height = () => props.height || 500;
  const showVolume = () => props.showVolume !== false;
  
  // 生成模拟K线数据
  const generateMockCandleData = (symbol: string, days: number = 100) => {
    const data = [];
    const basePrice = 100 + Math.random() * 400;
    let currentPrice = basePrice;
    
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - (days - i));
      
      const open = currentPrice;
      const change = (Math.random() - 0.5) * 10;
      const close = open + change;
      const high = Math.max(open, close) + Math.random() * 5;
      const low = Math.min(open, close) - Math.random() * 5;
      const volume = Math.floor(Math.random() * 10000000);
      
      data.push({
        time: Math.floor(date.getTime() / 1000) as any,
        open,
        high,
        low,
        close,
        volume
      });
      
      currentPrice = close;
    }
    
    return data;
  };
  
  // 初始化图表
  createEffect(() => {
    const containerEl = container();
    if (!containerEl) return;
    
    // 创建图表
    const chartOptions = {
      layout: {
        background: { 
          type: ColorType.Solid, 
          color: theme() === 'dark' ? '#111827' : '#ffffff' 
        },
        textColor: theme() === 'dark' ? '#d1d5db' : '#374151',
      },
      grid: {
        vertLines: { 
          color: theme() === 'dark' ? '#1f2937' : '#e5e7eb',
          style: LineStyle.Dotted
        },
        horzLines: { 
          color: theme() === 'dark' ? '#1f2937' : '#e5e7eb',
          style: LineStyle.Dotted
        },
      },
      crosshair: {
        mode: 1, // Normal crosshair mode
      },
      rightPriceScale: {
        borderColor: theme() === 'dark' ? '#374151' : '#d1d5db',
      },
      timeScale: {
        borderColor: theme() === 'dark' ? '#374151' : '#d1d5db',
        timeVisible: true,
        secondsVisible: false,
      },
      width: containerEl.clientWidth,
      height: height(),
    };
    
    const newChart = createChart(containerEl, chartOptions);
    setChart(newChart);
    
    // 添加K线图
    const candlestick = newChart.addCandlestickSeries({
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderVisible: false,
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350',
      priceFormat: {
        type: 'price',
        precision: 2,
        minMove: 0.01,
      },
    });
    setCandlestickSeries(candlestick);
    
    // 添加成交量图（如果启用）
    if (showVolume()) {
      const volume = newChart.addHistogramSeries({
        color: theme() === 'dark' ? '#4b5563' : '#9ca3af',
        priceFormat: {
          type: 'volume',
        },
        priceScaleId: '',
        scaleMargins: {
          top: 0.8,
          bottom: 0,
        },
      });
      setVolumeSeries(volume);
    }
    
    // 响应式处理
    const resizeHandler = () => {
      newChart.applyOptions({ 
        width: containerEl.clientWidth,
        height: height()
      });
    };
    
    window.addEventListener('resize', resizeHandler);
    
    onCleanup(() => {
      window.removeEventListener('resize', resizeHandler);
      newChart.remove();
      setChart(null);
      setCandlestickSeries(null);
      setVolumeSeries(null);
    });
  });
  
  // 更新主题
  createEffect(() => {
    const chartInstance = chart();
    if (!chartInstance) return;
    
    chartInstance.applyOptions({
      layout: {
        background: { 
          type: ColorType.Solid, 
          color: theme() === 'dark' ? '#111827' : '#ffffff' 
        },
        textColor: theme() === 'dark' ? '#d1d5db' : '#374151',
      },
      grid: {
        vertLines: { 
          color: theme() === 'dark' ? '#1f2937' : '#e5e7eb' 
        },
        horzLines: { 
          color: theme() === 'dark' ? '#1f2937' : '#e5e7eb' 
        },
      },
      rightPriceScale: {
        borderColor: theme() === 'dark' ? '#374151' : '#d1d5db',
      },
      timeScale: {
        borderColor: theme() === 'dark' ? '#374151' : '#d1d5db',
      },
    });
    
    // 更新成交量颜色
    const volumeSeriesInstance = volumeSeries();
    if (volumeSeriesInstance) {
      volumeSeriesInstance.applyOptions({
        color: theme() === 'dark' ? '#4b5563' : '#9ca3af',
      });
    }
  });
  
  // 更新数据
  createEffect(() => {
    const symbol = selectedSymbol();
    const candlestickSeriesInstance = candlestickSeries();
    const volumeSeriesInstance = volumeSeries();
    
    if (!candlestickSeriesInstance || !symbol) return;
    
    // 生成模拟数据
    const mockData = generateMockCandleData(symbol);
    
    // 设置K线数据
    candlestickSeriesInstance.setData(mockData.map(d => ({
      time: d.time,
      open: d.open,
      high: d.high,
      low: d.low,
      close: d.close
    })));
    
    // 设置成交量数据
    if (volumeSeriesInstance && showVolume()) {
      volumeSeriesInstance.setData(mockData.map(d => ({
        time: d.time,
        value: d.volume,
        color: d.close >= d.open ? '#26a69a80' : '#ef535080'
      })));
    }
  });
  
  return (
    <div class={css({
      w: 'full',
      bg: 'white',
      rounded: 'xl',
      border: '1px solid',
      borderColor: 'gray.200',
      overflow: 'hidden',
      _dark: {
        bg: 'gray.800',
        borderColor: 'gray.700'
      }
    })}>
      {/* 图表标题 */}
      <div class={css({
        px: 4,
        py: 3,
        borderBottom: '1px solid',
        borderColor: 'gray.200',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        _dark: {
          borderColor: 'gray.700'
        }
      })}>
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          gap: 3
        })}>
          <h3 class={css({
            fontSize: 'lg',
            fontWeight: 'semibold',
            color: 'gray.900',
            _dark: { color: 'gray.100' }
          })}>
            {selectedSymbol()} K线图
          </h3>
          
          {selectedTicker() && (
            <div class={css({
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              fontSize: 'sm'
            })}>
              <span class={css({
                fontFamily: 'mono',
                fontWeight: 'medium',
                color: 'gray.900',
                _dark: { color: 'gray.100' }
              })}>
                ${selectedTicker()!.price.toFixed(2)}
              </span>
              <span class={css({
                color: selectedTicker()!.changePercent >= 0 ? 'success.600' : 'danger.600',
                fontWeight: 'medium'
              })}>
                {selectedTicker()!.changePercent >= 0 ? '+' : ''}
                {selectedTicker()!.changePercent.toFixed(2)}%
              </span>
            </div>
          )}
        </div>
        
        {/* 图表控制按钮 */}
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          gap: 2
        })}>
          <button class={css({
            px: 2,
            py: 1,
            fontSize: 'xs',
            bg: 'gray.100',
            color: 'gray.700',
            rounded: 'md',
            transition: 'all 0.2s ease',
            _hover: {
              bg: 'gray.200'
            },
            _dark: {
              bg: 'gray.700',
              color: 'gray.300',
              _hover: {
                bg: 'gray.600'
              }
            }
          })}>
            1D
          </button>
          <button class={css({
            px: 2,
            py: 1,
            fontSize: 'xs',
            bg: 'primary.500',
            color: 'white',
            rounded: 'md',
            transition: 'all 0.2s ease',
            _hover: {
              bg: 'primary.600'
            }
          })}>
            1W
          </button>
          <button class={css({
            px: 2,
            py: 1,
            fontSize: 'xs',
            bg: 'gray.100',
            color: 'gray.700',
            rounded: 'md',
            transition: 'all 0.2s ease',
            _hover: {
              bg: 'gray.200'
            },
            _dark: {
              bg: 'gray.700',
              color: 'gray.300',
              _hover: {
                bg: 'gray.600'
              }
            }
          })}>
            1M
          </button>
        </div>
      </div>
      
      {/* 图表容器 */}
      <div 
        ref={setContainer} 
        class={css({
          w: 'full',
          h: `${height()}px`
        })}
      />
    </div>
  );
}
