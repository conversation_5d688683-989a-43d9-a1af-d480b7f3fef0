import{c as b,o as Q,a as U,t as B,s as e,i as m,m as S,b as Z,e as ee}from"./index-B8TYkGLu.js";var te=B("<div><div><div></div><div>🧩</div><div></div></div><div><div></div><div></div><div></div><button title=刷新验证码>↻"),ne=B("<div>");function oe(g){const[l,$]=b(!1),[v,y]=b(!1),[p,E]=b(0),[h,I]=b(!1),[G,H]=b(0),[L,J]=b(0),k=()=>g.width||320,K=()=>g.height||160,N=()=>g.sliderText||"向右滑动验证",z=()=>{const n=Math.random()*(k()-120)+60;return J(n),n},X=()=>{$(!1),y(!1),E(0),I(!1),z()},O=()=>{Math.abs(p()-L())<10?($(!0),y(!1),g.onSuccess?.()):(y(!0),$(!1),g.onFail?.(),setTimeout(X,1e3))},R=n=>{if(l())return;I(!0);const s="touches"in n?n.touches[0].clientX:n.clientX;H(s-p())},w=n=>{if(!h()||l())return;n.preventDefault();const s="touches"in n?n.touches[0].clientX:n.clientX,u=Math.max(0,Math.min(s-G(),k()-60));E(u)},C=()=>{!h()||l()||(I(!1),O())};return Q(()=>{z(),document.addEventListener("mousemove",w),document.addEventListener("mouseup",C),document.addEventListener("touchmove",w,{passive:!1}),document.addEventListener("touchend",C)}),U(()=>{document.removeEventListener("mousemove",w),document.removeEventListener("mouseup",C),document.removeEventListener("touchmove",w),document.removeEventListener("touchend",C)}),(()=>{var n=te(),s=n.firstChild,u=s.firstChild,d=u.nextSibling,o=d.nextSibling,x=s.nextSibling,i=x.firstChild,c=i.nextSibling,f=c.nextSibling,r=f.nextSibling;return e(n,"border","1px solid #e1e5e9"),e(n,"borderRadius","8px"),e(n,"backgroundColor","#f7f9fa"),e(n,"overflow","hidden"),e(n,"userSelect","none"),e(s,"background","linear-gradient(135deg, #667eea 0%, #764ba2 100%)"),e(s,"position","relative"),e(s,"display","flex"),e(s,"alignItems","center"),e(s,"justifyContent","center"),e(u,"position","absolute"),e(u,"width","100%"),e(u,"height","100%"),e(u,"backgroundImage",`
            radial-gradient(circle at 20% 20%, rgba(255,255,255,0.2) 2px, transparent 2px),
            radial-gradient(circle at 80% 80%, rgba(255,255,255,0.2) 2px, transparent 2px),
            radial-gradient(circle at 40% 60%, rgba(255,255,255,0.1) 1px, transparent 1px)
          `),e(u,"backgroundSize","50px 50px, 30px 30px, 20px 20px"),e(d,"position","absolute"),e(d,"top","50%"),e(d,"transform","translateY(-50%)"),e(d,"width","50px"),e(d,"height","50px"),e(d,"border","2px dashed rgba(255,255,255,0.6)"),e(d,"borderRadius","8px"),e(d,"display","flex"),e(d,"alignItems","center"),e(d,"justifyContent","center"),e(d,"color","rgba(255,255,255,0.8)"),e(d,"fontSize","20px"),e(o,"position","absolute"),e(o,"top","50%"),e(o,"transform","translateY(-50%)"),e(o,"width","50px"),e(o,"height","50px"),e(o,"borderRadius","8px"),e(o,"display","flex"),e(o,"alignItems","center"),e(o,"justifyContent","center"),e(o,"color","white"),e(o,"fontSize","20px"),e(o,"boxShadow","0 4px 8px rgba(0,0,0,0.2)"),m(o,(()=>{var t=S(()=>!!l());return()=>t()?"✓":v()?"✗":"🧩"})()),m(s,(()=>{var t=S(()=>!!(l()||v()));return()=>t()&&(()=>{var a=ne();return e(a,"position","absolute"),e(a,"top","50%"),e(a,"left","50%"),e(a,"transform","translate(-50%, -50%)"),e(a,"backgroundColor","rgba(0,0,0,0.8)"),e(a,"color","white"),e(a,"padding","8px 16px"),e(a,"borderRadius","20px"),e(a,"fontSize","14px"),e(a,"fontWeight","500"),m(a,()=>l()?"✅ 验证成功":"❌ 验证失败"),a})()})(),null),e(x,"height","50px"),e(x,"backgroundColor","#f7f9fa"),e(x,"borderTop","1px solid #e1e5e9"),e(x,"position","relative"),e(x,"display","flex"),e(x,"alignItems","center"),i.$$touchstart=R,i.$$mousedown=R,e(i,"position","absolute"),e(i,"width","60px"),e(i,"height","40px"),e(i,"border","1px solid #d9d9d9"),e(i,"borderRadius","6px"),e(i,"display","flex"),e(i,"alignItems","center"),e(i,"justifyContent","center"),e(i,"color","white"),e(i,"fontSize","18px"),e(i,"boxShadow","0 2px 4px rgba(0,0,0,0.1)"),e(i,"zIndex","10"),m(i,(()=>{var t=S(()=>!!l());return()=>t()?"✓":v()?"✗":"→"})()),e(c,"position","absolute"),e(c,"left","0"),e(c,"top","50%"),e(c,"transform","translateY(-50%)"),e(c,"height","40px"),e(c,"borderRadius","6px"),e(c,"transition","all 0.3s ease"),e(f,"width","100%"),e(f,"textAlign","center"),e(f,"color","#999"),e(f,"fontSize","14px"),e(f,"pointerEvents","none"),e(f,"zIndex","5"),m(f,(()=>{var t=S(()=>!!l());return()=>t()?"验证成功":S(()=>!!v())()?"验证失败，请重试":N()})()),r.$$click=()=>{X(),g.onRefresh?.()},e(r,"position","absolute"),e(r,"right","10px"),e(r,"width","30px"),e(r,"height","30px"),e(r,"border","none"),e(r,"backgroundColor","transparent"),e(r,"cursor","pointer"),e(r,"fontSize","16px"),e(r,"color","#999"),e(r,"borderRadius","50%"),e(r,"display","flex"),e(r,"alignItems","center"),e(r,"justifyContent","center"),e(r,"zIndex","10"),Z(t=>{var a=`${k()}px`,M=`${K()}px`,T=`${L()}px`,j=`${p()}px`,_=l()?"#52c41a":v()?"#ff4d4f":"#1890ff",D=h()?"none":"all 0.3s ease",F=h()?"grabbing":"grab",V=`${p()}px`,Y=l()?"#52c41a":v()?"#ff4d4f":"#1890ff",A=h()?"grabbing":"grab",P=h()?"none":"all 0.3s ease",W=`${p()+30}px`,q=l()?"#f6ffed":v()?"#fff2f0":"#e6f7ff";return a!==t.e&&e(n,"width",t.e=a),M!==t.t&&e(s,"height",t.t=M),T!==t.a&&e(d,"left",t.a=T),j!==t.o&&e(o,"left",t.o=j),_!==t.i&&e(o,"backgroundColor",t.i=_),D!==t.n&&e(o,"transition",t.n=D),F!==t.s&&e(o,"cursor",t.s=F),V!==t.h&&e(i,"left",t.h=V),Y!==t.r&&e(i,"backgroundColor",t.r=Y),A!==t.d&&e(i,"cursor",t.d=A),P!==t.l&&e(i,"transition",t.l=P),W!==t.u&&e(c,"width",t.u=W),q!==t.c&&e(c,"backgroundColor",t.c=q),t},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0}),n})()}ee(["mousedown","touchstart","click"]);export{oe as S};
