/**
 * 策略管理路由
 */
import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { authenticateToken } from './auth.js';

const router = express.Router();

// 模拟策略数据
const strategies = new Map();

// 初始化示例策略
const sampleStrategies = [
  {
    id: 'strategy_001',
    userId: 1,
    name: '双均线突破策略',
    description: '基于5日和20日移动平均线交叉的经典趋势跟踪策略',
    category: '趋势跟踪',
    type: 'built-in',
    code: `
def strategy(data):
    # 双均线策略逻辑
    ma5 = data['close'].rolling(5).mean()
    ma20 = data['close'].rolling(20).mean()
    
    signals = []
    for i in range(len(data)):
        if ma5[i] > ma20[i] and ma5[i-1] <= ma20[i-1]:
            signals.append('buy')
        elif ma5[i] < ma20[i] and ma5[i-1] >= ma20[i-1]:
            signals.append('sell')
        else:
            signals.append('hold')
    
    return signals
    `,
    params: {
      shortPeriod: 5,
      longPeriod: 20,
      stopLoss: 0.05,
      takeProfit: 0.15
    },
    performance: {
      totalReturn: 0.125,
      sharpeRatio: 1.45,
      maxDrawdown: 0.08,
      winRate: 0.62
    },
    isActive: true,
    isPublic: true,
    tags: ['均线', '趋势', '经典'],
    createTime: '2024-01-10T10:00:00Z',
    updateTime: '2024-01-15T14:30:00Z'
  },
  {
    id: 'strategy_002',
    userId: 1,
    name: 'RSI均值回归策略',
    description: 'RSI指标超买超卖的逆向交易策略',
    category: '均值回归',
    type: 'custom',
    code: `
def strategy(data):
    # RSI策略逻辑
    rsi = calculate_rsi(data['close'], period=14)
    
    signals = []
    for i in range(len(data)):
        if rsi[i] < 30:
            signals.append('buy')
        elif rsi[i] > 70:
            signals.append('sell')
        else:
            signals.append('hold')
    
    return signals
    `,
    params: {
      rsiPeriod: 14,
      overbought: 70,
      oversold: 30
    },
    performance: {
      totalReturn: -0.035,
      sharpeRatio: -0.28,
      maxDrawdown: 0.12,
      winRate: 0.48
    },
    isActive: false,
    isPublic: false,
    tags: ['RSI', '逆向', '震荡'],
    createTime: '2024-01-12T16:20:00Z',
    updateTime: '2024-01-18T09:15:00Z'
  },
  {
    id: 'strategy_003',
    userId: 1,
    name: '动量突破策略',
    description: '基于价格动量的突破交易策略',
    category: '动量策略',
    type: 'custom',
    code: `
def strategy(data):
    # 动量策略逻辑
    returns = data['close'].pct_change(20)  # 20日收益率
    
    signals = []
    for i in range(len(data)):
        if returns[i] > 0.1:  # 20日涨幅超过10%
            signals.append('buy')
        elif returns[i] < -0.1:  # 20日跌幅超过10%
            signals.append('sell')
        else:
            signals.append('hold')
    
    return signals
    `,
    params: {
      lookbackPeriod: 20,
      buyThreshold: 0.1,
      sellThreshold: -0.1
    },
    performance: {
      totalReturn: 0.089,
      sharpeRatio: 0.95,
      maxDrawdown: 0.15,
      winRate: 0.55
    },
    isActive: true,
    isPublic: true,
    tags: ['动量', '突破', '趋势'],
    createTime: '2024-01-20T11:45:00Z',
    updateTime: '2024-01-22T13:20:00Z'
  }
];

// 初始化示例数据
for (const strategy of sampleStrategies) {
  strategies.set(strategy.id, strategy);
}

/**
 * 获取策略列表
 */
router.get('/list', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { category, type, isActive, isPublic, page = 1, limit = 20 } = req.query;
    
    let allStrategies = Array.from(strategies.values());
    
    // 权限过滤：只能看到自己的私有策略和所有公开策略
    allStrategies = allStrategies.filter(strategy => 
      strategy.isPublic || strategy.userId === userId
    );
    
    // 分类筛选
    if (category) {
      allStrategies = allStrategies.filter(strategy => strategy.category === category);
    }
    
    // 类型筛选
    if (type) {
      allStrategies = allStrategies.filter(strategy => strategy.type === type);
    }
    
    // 状态筛选
    if (isActive !== undefined) {
      allStrategies = allStrategies.filter(strategy => 
        strategy.isActive === (isActive === 'true')
      );
    }
    
    // 公开性筛选
    if (isPublic !== undefined) {
      allStrategies = allStrategies.filter(strategy => 
        strategy.isPublic === (isPublic === 'true')
      );
    }
    
    // 排序
    allStrategies.sort((a, b) => new Date(b.updateTime) - new Date(a.updateTime));
    
    // 分页
    const offset = (parseInt(page) - 1) * parseInt(limit);
    const paginatedStrategies = allStrategies.slice(offset, offset + parseInt(limit));
    
    res.json({
      success: true,
      data: {
        strategies: paginatedStrategies,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: allStrategies.length,
          pages: Math.ceil(allStrategies.length / parseInt(limit))
        }
      }
    });
    
  } catch (error) {
    console.error('获取策略列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取策略列表失败',
      code: 'STRATEGY_LIST_ERROR'
    });
  }
});

/**
 * 获取策略详情
 */
router.get('/:strategyId', authenticateToken, async (req, res) => {
  try {
    const { strategyId } = req.params;
    const userId = req.user.id;
    
    const strategy = strategies.get(strategyId);
    
    if (!strategy) {
      return res.status(404).json({
        success: false,
        message: '策略不存在',
        code: 'STRATEGY_NOT_FOUND'
      });
    }
    
    // 权限检查
    if (!strategy.isPublic && strategy.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: '无权访问此策略',
        code: 'INSUFFICIENT_PERMISSION'
      });
    }
    
    res.json({
      success: true,
      data: {
        strategy
      }
    });
    
  } catch (error) {
    console.error('获取策略详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取策略详情失败',
      code: 'STRATEGY_DETAIL_ERROR'
    });
  }
});

/**
 * 创建策略
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { 
      name, 
      description, 
      category, 
      code, 
      params = {}, 
      isPublic = false,
      tags = []
    } = req.body;
    
    // 验证必要参数
    if (!name || !description || !category) {
      return res.status(400).json({
        success: false,
        message: '策略名称、描述和分类不能为空',
        code: 'INVALID_STRATEGY_PARAMS'
      });
    }
    
    // 创建策略
    const strategyId = `strategy_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    const strategy = {
      id: strategyId,
      userId,
      name,
      description,
      category,
      type: 'custom',
      code: code || '',
      params,
      performance: {
        totalReturn: 0,
        sharpeRatio: 0,
        maxDrawdown: 0,
        winRate: 0
      },
      isActive: false,
      isPublic: Boolean(isPublic),
      tags: Array.isArray(tags) ? tags : [],
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    };
    
    strategies.set(strategyId, strategy);
    
    res.status(201).json({
      success: true,
      message: '策略创建成功',
      data: {
        strategy
      }
    });
    
  } catch (error) {
    console.error('创建策略错误:', error);
    res.status(500).json({
      success: false,
      message: '创建策略失败',
      code: 'CREATE_STRATEGY_ERROR'
    });
  }
});

/**
 * 更新策略
 */
router.put('/:strategyId', authenticateToken, async (req, res) => {
  try {
    const { strategyId } = req.params;
    const userId = req.user.id;
    const updateData = req.body;
    
    const strategy = strategies.get(strategyId);
    
    if (!strategy) {
      return res.status(404).json({
        success: false,
        message: '策略不存在',
        code: 'STRATEGY_NOT_FOUND'
      });
    }
    
    if (strategy.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: '无权修改此策略',
        code: 'INSUFFICIENT_PERMISSION'
      });
    }
    
    // 更新策略
    const updatedStrategy = {
      ...strategy,
      ...updateData,
      id: strategyId, // 确保ID不被修改
      userId, // 确保用户ID不被修改
      updateTime: new Date().toISOString()
    };
    
    strategies.set(strategyId, updatedStrategy);
    
    res.json({
      success: true,
      message: '策略更新成功',
      data: {
        strategy: updatedStrategy
      }
    });
    
  } catch (error) {
    console.error('更新策略错误:', error);
    res.status(500).json({
      success: false,
      message: '更新策略失败',
      code: 'UPDATE_STRATEGY_ERROR'
    });
  }
});

/**
 * 删除策略
 */
router.delete('/:strategyId', authenticateToken, async (req, res) => {
  try {
    const { strategyId } = req.params;
    const userId = req.user.id;
    
    const strategy = strategies.get(strategyId);
    
    if (!strategy) {
      return res.status(404).json({
        success: false,
        message: '策略不存在',
        code: 'STRATEGY_NOT_FOUND'
      });
    }
    
    if (strategy.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: '无权删除此策略',
        code: 'INSUFFICIENT_PERMISSION'
      });
    }
    
    if (strategy.isActive) {
      return res.status(400).json({
        success: false,
        message: '不能删除正在运行的策略',
        code: 'CANNOT_DELETE_ACTIVE'
      });
    }
    
    strategies.delete(strategyId);
    
    res.json({
      success: true,
      message: '策略删除成功'
    });
    
  } catch (error) {
    console.error('删除策略错误:', error);
    res.status(500).json({
      success: false,
      message: '删除策略失败',
      code: 'DELETE_STRATEGY_ERROR'
    });
  }
});

/**
 * 启动/停止策略
 */
router.post('/:strategyId/toggle', authenticateToken, async (req, res) => {
  try {
    const { strategyId } = req.params;
    const userId = req.user.id;
    
    const strategy = strategies.get(strategyId);
    
    if (!strategy) {
      return res.status(404).json({
        success: false,
        message: '策略不存在',
        code: 'STRATEGY_NOT_FOUND'
      });
    }
    
    if (strategy.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: '无权操作此策略',
        code: 'INSUFFICIENT_PERMISSION'
      });
    }
    
    // 切换策略状态
    strategy.isActive = !strategy.isActive;
    strategy.updateTime = new Date().toISOString();
    
    strategies.set(strategyId, strategy);
    
    res.json({
      success: true,
      message: strategy.isActive ? '策略启动成功' : '策略停止成功',
      data: {
        strategy: {
          id: strategy.id,
          name: strategy.name,
          isActive: strategy.isActive
        }
      }
    });
    
  } catch (error) {
    console.error('切换策略状态错误:', error);
    res.status(500).json({
      success: false,
      message: '策略状态切换失败',
      code: 'TOGGLE_STRATEGY_ERROR'
    });
  }
});

/**
 * 复制策略
 */
router.post('/:strategyId/copy', authenticateToken, async (req, res) => {
  try {
    const { strategyId } = req.params;
    const userId = req.user.id;
    const { name } = req.body;
    
    const originalStrategy = strategies.get(strategyId);
    
    if (!originalStrategy) {
      return res.status(404).json({
        success: false,
        message: '策略不存在',
        code: 'STRATEGY_NOT_FOUND'
      });
    }
    
    // 权限检查
    if (!originalStrategy.isPublic && originalStrategy.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: '无权复制此策略',
        code: 'INSUFFICIENT_PERMISSION'
      });
    }
    
    // 创建复制的策略
    const newStrategyId = `strategy_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    const copiedStrategy = {
      ...originalStrategy,
      id: newStrategyId,
      userId,
      name: name || `${originalStrategy.name} (副本)`,
      isActive: false,
      isPublic: false,
      performance: {
        totalReturn: 0,
        sharpeRatio: 0,
        maxDrawdown: 0,
        winRate: 0
      },
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    };
    
    strategies.set(newStrategyId, copiedStrategy);
    
    res.status(201).json({
      success: true,
      message: '策略复制成功',
      data: {
        strategy: copiedStrategy
      }
    });
    
  } catch (error) {
    console.error('复制策略错误:', error);
    res.status(500).json({
      success: false,
      message: '复制策略失败',
      code: 'COPY_STRATEGY_ERROR'
    });
  }
});

/**
 * 获取策略分类
 */
router.get('/meta/categories', async (req, res) => {
  try {
    const categories = [
      { id: 'trend', name: '趋势跟踪', description: '跟随市场趋势的策略' },
      { id: 'mean_reversion', name: '均值回归', description: '基于价格回归均值的策略' },
      { id: 'momentum', name: '动量策略', description: '基于价格动量的策略' },
      { id: 'arbitrage', name: '套利策略', description: '利用价格差异进行套利' },
      { id: 'market_making', name: '做市策略', description: '提供流动性的做市商策略' },
      { id: 'statistical', name: '统计套利', description: '基于统计模型的套利策略' }
    ];
    
    res.json({
      success: true,
      data: {
        categories,
        count: categories.length
      }
    });
    
  } catch (error) {
    console.error('获取策略分类错误:', error);
    res.status(500).json({
      success: false,
      message: '获取策略分类失败',
      code: 'CATEGORIES_ERROR'
    });
  }
});

export default router;