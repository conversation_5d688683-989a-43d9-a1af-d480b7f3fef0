{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/dart/dart.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/dart/dart.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"`\", close: \"`\", notIn: [\"string\", \"comment\"] },\n    { open: \"/**\", close: \" */\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"`\", close: \"`\" }\n  ],\n  folding: {\n    markers: {\n      start: /^\\s*\\s*#?region\\b/,\n      end: /^\\s*\\s*#?endregion\\b/\n    }\n  }\n};\nvar language = {\n  defaultToken: \"invalid\",\n  tokenPostfix: \".dart\",\n  keywords: [\n    \"abstract\",\n    \"dynamic\",\n    \"implements\",\n    \"show\",\n    \"as\",\n    \"else\",\n    \"import\",\n    \"static\",\n    \"assert\",\n    \"enum\",\n    \"in\",\n    \"super\",\n    \"async\",\n    \"export\",\n    \"interface\",\n    \"switch\",\n    \"await\",\n    \"extends\",\n    \"is\",\n    \"sync\",\n    \"break\",\n    \"external\",\n    \"library\",\n    \"this\",\n    \"case\",\n    \"factory\",\n    \"mixin\",\n    \"throw\",\n    \"catch\",\n    \"false\",\n    \"new\",\n    \"true\",\n    \"class\",\n    \"final\",\n    \"null\",\n    \"try\",\n    \"const\",\n    \"finally\",\n    \"on\",\n    \"typedef\",\n    \"continue\",\n    \"for\",\n    \"operator\",\n    \"var\",\n    \"covariant\",\n    \"Function\",\n    \"part\",\n    \"void\",\n    \"default\",\n    \"get\",\n    \"rethrow\",\n    \"while\",\n    \"deferred\",\n    \"hide\",\n    \"return\",\n    \"with\",\n    \"do\",\n    \"if\",\n    \"set\",\n    \"yield\"\n  ],\n  typeKeywords: [\"int\", \"double\", \"String\", \"bool\"],\n  operators: [\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"~/\",\n    \"%\",\n    \"++\",\n    \"--\",\n    \"==\",\n    \"!=\",\n    \">\",\n    \"<\",\n    \">=\",\n    \"<=\",\n    \"=\",\n    \"-=\",\n    \"/=\",\n    \"%=\",\n    \">>=\",\n    \"^=\",\n    \"+=\",\n    \"*=\",\n    \"~/=\",\n    \"<<=\",\n    \"&=\",\n    \"!=\",\n    \"||\",\n    \"&&\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \"~\",\n    \"<<\",\n    \">>\",\n    \"!\",\n    \">>>\",\n    \"??\",\n    \"?\",\n    \":\",\n    \"|=\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+(_+\\d+)*/,\n  octaldigits: /[0-7]+(_+[0-7]+)*/,\n  binarydigits: /[0-1]+(_+[0-1]+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  regexpctl: /[(){}\\[\\]\\$\\^|\\-*+?\\.]/,\n  regexpesc: /\\\\(?:[bBdDfnrstvwWn0\\\\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})/,\n  tokenizer: {\n    root: [[/[{}]/, \"delimiter.bracket\"], { include: \"common\" }],\n    common: [\n      [\n        /[a-z_$][\\w$]*/,\n        {\n          cases: {\n            \"@typeKeywords\": \"type.identifier\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[A-Z_$][\\w\\$]*/, \"type.identifier\"],\n      { include: \"@whitespace\" },\n      [\n        /\\/(?=([^\\\\\\/]|\\\\.)+\\/([gimsuy]*)(\\s*)(\\.|;|,|\\)|\\]|\\}|$))/,\n        { token: \"regexp\", bracket: \"@open\", next: \"@regexp\" }\n      ],\n      [/@[a-zA-Z]+/, \"annotation\"],\n      [/[()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/!(?=([^=]|$))/, \"delimiter\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/(@digits)[eE]([\\-+]?(@digits))?/, \"number.float\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?/, \"number.float\"],\n      [/0[xX](@hexdigits)n?/, \"number.hex\"],\n      [/0[oO]?(@octaldigits)n?/, \"number.octal\"],\n      [/0[bB](@binarydigits)n?/, \"number.binary\"],\n      [/(@digits)n?/, \"number\"],\n      [/[;,.]/, \"delimiter\"],\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/\"/, \"string\", \"@string_double\"],\n      [/'/, \"string\", \"@string_single\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@jsdoc\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/\\/.*$/, \"comment.doc\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    jsdoc: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    regexp: [\n      [\n        /(\\{)(\\d+(?:,\\d*)?)(\\})/,\n        [\"regexp.escape.control\", \"regexp.escape.control\", \"regexp.escape.control\"]\n      ],\n      [\n        /(\\[)(\\^?)(?=(?:[^\\]\\\\\\/]|\\\\.)+)/,\n        [\"regexp.escape.control\", { token: \"regexp.escape.control\", next: \"@regexrange\" }]\n      ],\n      [/(\\()(\\?:|\\?=|\\?!)/, [\"regexp.escape.control\", \"regexp.escape.control\"]],\n      [/[()]/, \"regexp.escape.control\"],\n      [/@regexpctl/, \"regexp.escape.control\"],\n      [/[^\\\\\\/]/, \"regexp\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/\\\\\\./, \"regexp.invalid\"],\n      [/(\\/)([gimsuy]*)/, [{ token: \"regexp\", bracket: \"@close\", next: \"@pop\" }, \"keyword.other\"]]\n    ],\n    regexrange: [\n      [/-/, \"regexp.escape.control\"],\n      [/\\^/, \"regexp.invalid\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/[^\\]]/, \"regexp\"],\n      [\n        /\\]/,\n        {\n          token: \"regexp.escape.control\",\n          next: \"@pop\",\n          bracket: \"@close\"\n        }\n      ]\n    ],\n    string_double: [\n      [/[^\\\\\"\\$]+/, \"string\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"],\n      [/\\$\\w+/, \"identifier\"]\n    ],\n    string_single: [\n      [/[^\\\\'\\$]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, \"string\", \"@pop\"],\n      [/\\$\\w+/, \"identifier\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,QAAQ,EAAE;AAAA,EACjD;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,cAAc,CAAC,OAAO,UAAU,UAAU,MAAM;AAAA,EAChD,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,IACT,MAAM,CAAC,CAAC,QAAQ,mBAAmB,GAAG,EAAE,SAAS,SAAS,CAAC;AAAA,IAC3D,QAAQ;AAAA,MACN;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,iBAAiB;AAAA,YACjB,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,kBAAkB,iBAAiB;AAAA,MACpC,EAAE,SAAS,cAAc;AAAA,MACzB;AAAA,QACE;AAAA,QACA,EAAE,OAAO,UAAU,SAAS,SAAS,MAAM,UAAU;AAAA,MACvD;AAAA,MACA,CAAC,cAAc,YAAY;AAAA,MAC3B,CAAC,YAAY,WAAW;AAAA,MACxB,CAAC,oBAAoB,WAAW;AAAA,MAChC,CAAC,iBAAiB,WAAW;AAAA,MAC7B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,mCAAmC,cAAc;AAAA,MAClD,CAAC,8CAA8C,cAAc;AAAA,MAC7D,CAAC,uBAAuB,YAAY;AAAA,MACpC,CAAC,0BAA0B,cAAc;AAAA,MACzC,CAAC,0BAA0B,eAAe;AAAA,MAC1C,CAAC,eAAe,QAAQ;AAAA,MACxB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,KAAK,UAAU,gBAAgB;AAAA,MAChC,CAAC,KAAK,UAAU,gBAAgB;AAAA,IAClC;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,gBAAgB,eAAe,QAAQ;AAAA,MACxC,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,aAAa,aAAa;AAAA,MAC3B,CAAC,WAAW,SAAS;AAAA,IACvB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACrB;AAAA,IACA,OAAO;AAAA,MACL,CAAC,WAAW,aAAa;AAAA,MACzB,CAAC,QAAQ,eAAe,MAAM;AAAA,MAC9B,CAAC,SAAS,aAAa;AAAA,IACzB;AAAA,IACA,QAAQ;AAAA,MACN;AAAA,QACE;AAAA,QACA,CAAC,yBAAyB,yBAAyB,uBAAuB;AAAA,MAC5E;AAAA,MACA;AAAA,QACE;AAAA,QACA,CAAC,yBAAyB,EAAE,OAAO,yBAAyB,MAAM,cAAc,CAAC;AAAA,MACnF;AAAA,MACA,CAAC,qBAAqB,CAAC,yBAAyB,uBAAuB,CAAC;AAAA,MACxE,CAAC,QAAQ,uBAAuB;AAAA,MAChC,CAAC,cAAc,uBAAuB;AAAA,MACtC,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,cAAc,eAAe;AAAA,MAC9B,CAAC,QAAQ,gBAAgB;AAAA,MACzB,CAAC,mBAAmB,CAAC,EAAE,OAAO,UAAU,SAAS,UAAU,MAAM,OAAO,GAAG,eAAe,CAAC;AAAA,IAC7F;AAAA,IACA,YAAY;AAAA,MACV,CAAC,KAAK,uBAAuB;AAAA,MAC7B,CAAC,MAAM,gBAAgB;AAAA,MACvB,CAAC,cAAc,eAAe;AAAA,MAC9B,CAAC,SAAS,QAAQ;AAAA,MAClB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,MACtB,CAAC,SAAS,YAAY;AAAA,IACxB;AAAA,IACA,eAAe;AAAA,MACb,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,MACtB,CAAC,SAAS,YAAY;AAAA,IACxB;AAAA,EACF;AACF;", "names": []}