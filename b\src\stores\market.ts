/**
 * 市场数据状态管理
 */
import { createSignal, createEffect, onCleanup } from 'solid-js'
import { marketApi, marketWS, type QuoteData, type MarketOverview } from '../api'

// 市场数据状态
export interface MarketState {
  quotes: Record<string, QuoteData>
  overview: MarketOverview | null
  watchlist: string[]
  isLoading: boolean
  error: string | null
  lastUpdate: number
}

// 创建响应式状态
const [marketState, setMarketState] = createSignal<MarketState>({
  quotes: {},
  overview: null,
  watchlist: ['000001', '000002', '399001', '399006'],
  isLoading: false,
  error: null,
  lastUpdate: 0
})

/**
 * 市场数据Store
 */
export class MarketStore {
  // 获取状态
  get state() {
    return marketState()
  }

  // 设置加载状态
  setLoading(loading: boolean) {
    setMarketState(prev => ({ ...prev, isLoading: loading }))
  }

  // 设置错误
  setError(error: string | null) {
    setMarketState(prev => ({ ...prev, error }))
  }

  // 更新行情数据
  updateQuotes(quotes: QuoteData[]) {
    const quotesMap: Record<string, QuoteData> = {}
    quotes.forEach(quote => {
      quotesMap[quote.symbol] = quote
    })

    setMarketState(prev => ({
      ...prev,
      quotes: { ...prev.quotes, ...quotesMap },
      lastUpdate: Date.now(),
      error: null
    }))
  }

  // 更新市场概览
  updateOverview(overview: MarketOverview) {
    setMarketState(prev => ({
      ...prev,
      overview,
      lastUpdate: Date.now(),
      error: null
    }))
  }

  // 添加到监控列表
  addToWatchlist(symbol: string) {
    setMarketState(prev => {
      if (!prev.watchlist.includes(symbol)) {
        const newWatchlist = [...prev.watchlist, symbol]
        this.subscribeQuotes([symbol]) // 订阅新股票
        return { ...prev, watchlist: newWatchlist }
      }
      return prev
    })
  }

  // 从监控列表移除
  removeFromWatchlist(symbol: string) {
    setMarketState(prev => {
      const newWatchlist = prev.watchlist.filter(s => s !== symbol)
      marketWS.unsubscribeQuote([symbol]) // 取消订阅
      return { ...prev, watchlist: newWatchlist }
    })
  }

  // 获取实时行情
  async fetchQuotes(symbols?: string[]) {
    const targetSymbols = symbols || this.state.watchlist
    if (targetSymbols.length === 0) return

    try {
      this.setLoading(true)
      const quotes = await marketApi.getQuote(targetSymbols)
      this.updateQuotes(quotes)
    } catch (error) {
      console.error('获取行情失败:', error)
      this.setError(error instanceof Error ? error.message : '获取行情失败')
    } finally {
      this.setLoading(false)
    }
  }

  // 获取市场概览
  async fetchOverview() {
    try {
      this.setLoading(true)
      const overview = await marketApi.getOverview()
      this.updateOverview(overview)
    } catch (error) {
      console.error('获取市场概览失败:', error)
      this.setError(error instanceof Error ? error.message : '获取市场概览失败')
    } finally {
      this.setLoading(false)
    }
  }

  // 订阅实时行情
  subscribeQuotes(symbols: string[]) {
    if (marketWS.getState() === 'connected') {
      marketWS.subscribeQuote(symbols)
    }
  }

  // 取消订阅行情
  unsubscribeQuotes(symbols: string[]) {
    if (marketWS.getState() === 'connected') {
      marketWS.unsubscribeQuote(symbols)
    }
  }

  // 初始化WebSocket连接
  initializeWebSocket() {
    // 监听WebSocket连接状态
    marketWS.on('open', () => {
      console.log('市场数据WebSocket已连接')
      // 连接成功后订阅监控列表
      if (this.state.watchlist.length > 0) {
        this.subscribeQuotes(this.state.watchlist)
      }
    })

    // 监听实时数据
    marketWS.on('message', (message) => {
      if (message.type === 'quote') {
        this.updateQuotes([message.data])
      } else if (message.type === 'overview') {
        this.updateOverview(message.data)
      }
    })

    // 监听连接错误
    marketWS.on('error', (error) => {
      console.error('市场数据WebSocket错误:', error)
      this.setError('实时数据连接异常')
    })

    // 监听连接关闭
    marketWS.on('close', () => {
      console.log('市场数据WebSocket连接已关闭')
    })
  }

  // 清理资源
  cleanup() {
    marketWS.disconnect()
  }
}

// 创建store实例
export const marketStore = new MarketStore()

// 导出响应式状态访问器
export const useMarketState = () => marketState()
export const useQuotes = () => marketState().quotes
export const useOverview = () => marketState().overview
export const useWatchlist = () => marketState().watchlist
export const useMarketLoading = () => marketState().isLoading
export const useMarketError = () => marketState().error

// 导出便捷函数
export const getQuote = (symbol: string) => marketState().quotes[symbol]
export const getQuotes = (symbols: string[]) => symbols.map(symbol => marketState().quotes[symbol]).filter(Boolean)

// 初始化效果
createEffect(() => {
  // 初始化WebSocket
  marketStore.initializeWebSocket()

  // 定期获取市场概览
  const overviewInterval = setInterval(() => {
    marketStore.fetchOverview()
  }, 30000) // 30秒更新一次

  // 定期获取行情数据（作为WebSocket的备用）
  const quotesInterval = setInterval(() => {
    if (marketWS.getState() !== 'connected') {
      marketStore.fetchQuotes()
    }
  }, 5000) // 5秒更新一次

  // 清理函数
  onCleanup(() => {
    clearInterval(overviewInterval)
    clearInterval(quotesInterval)
    marketStore.cleanup()
  })
})

// 初始数据加载
marketStore.fetchOverview()
marketStore.fetchQuotes()

// 导出默认实例
export default marketStore
