import { atom } from 'jotai';
import { createEventBus } from '@solid-primitives/event-bus';

// 市场数据类型定义
export interface TickerData {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

export interface CandleData {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface MarketEvents {
  'price-update': TickerData;
  'volume-spike': { symbol: string; volume: number; avgVolume: number };
  'connection-status': { connected: boolean };
}

// 事件总线
export const marketEventBus = createEventBus<MarketEvents>();

// 基础原子状态
export const tickersAtom = atom<Record<string, TickerData>>({});
export const selectedSymbolAtom = atom<string>('AAPL');
export const connectionStatusAtom = atom<boolean>(false);

// 监控列表
export const watchlistAtom = atom<string[]>([
  'AAPL',   // 苹果
  'MSFT',   // 微软
  'GOOGL',  // 谷歌
  'AMZN',   // 亚马逊
  'TSLA',   // 特斯拉
  'BTC-USD', // 比特币
  'ETH-USD', // 以太坊
  'EUR/USD', // 欧元美元
  'GC=F',   // 黄金期货
  'CL=F'    // 原油期货
]);

// 衍生状态 - 热门股票
export const topMoversAtom = atom((get) => {
  const tickers = get(tickersAtom);
  const watchlist = get(watchlistAtom);
  
  return watchlist
    .map(symbol => tickers[symbol])
    .filter(Boolean)
    .sort((a, b) => Math.abs(b.changePercent) - Math.abs(a.changePercent))
    .slice(0, 5);
});

// 衍生状态 - 当前选中股票数据
export const selectedTickerAtom = atom((get) => {
  const tickers = get(tickersAtom);
  const selectedSymbol = get(selectedSymbolAtom);
  return tickers[selectedSymbol] || null;
});

// 衍生状态 - 市场概览统计
export const marketOverviewAtom = atom((get) => {
  const tickers = get(tickersAtom);
  const watchlist = get(watchlistAtom);
  
  const watchlistTickers = watchlist
    .map(symbol => tickers[symbol])
    .filter(Boolean);
  
  if (watchlistTickers.length === 0) {
    return {
      totalSymbols: 0,
      gainers: 0,
      losers: 0,
      unchanged: 0,
      avgChange: 0,
      totalVolume: 0
    };
  }
  
  const gainers = watchlistTickers.filter(t => t.changePercent > 0).length;
  const losers = watchlistTickers.filter(t => t.changePercent < 0).length;
  const unchanged = watchlistTickers.filter(t => t.changePercent === 0).length;
  
  const avgChange = watchlistTickers.reduce((sum, t) => sum + t.changePercent, 0) / watchlistTickers.length;
  const totalVolume = watchlistTickers.reduce((sum, t) => sum + t.volume, 0);
  
  return {
    totalSymbols: watchlistTickers.length,
    gainers,
    losers,
    unchanged,
    avgChange,
    totalVolume
  };
});

// K线数据原子
export const candleDataAtom = atom<Record<string, CandleData[]>>({});

// 获取指定股票的K线数据
export const getCandleDataAtom = (symbol: string) => atom((get) => {
  const candleData = get(candleDataAtom);
  return candleData[symbol] || [];
});

// 操作函数
export const updateTickerAtom = atom(
  null,
  (get, set, ticker: TickerData) => {
    const tickers = get(tickersAtom);
    set(tickersAtom, { ...tickers, [ticker.symbol]: ticker });
    
    // 发布价格更新事件
    marketEventBus.emit('price-update', ticker);
    
    // 检测成交量异动
    const avgVolume = ticker.volume * 0.8; // 简化的平均成交量计算
    if (ticker.volume > avgVolume * 2) {
      marketEventBus.emit('volume-spike', {
        symbol: ticker.symbol,
        volume: ticker.volume,
        avgVolume
      });
    }
  }
);

export const updateCandleDataAtom = atom(
  null,
  (get, set, symbol: string, candles: CandleData[]) => {
    const candleData = get(candleDataAtom);
    set(candleDataAtom, { ...candleData, [symbol]: candles });
  }
);

export const setConnectionStatusAtom = atom(
  null,
  (get, set, connected: boolean) => {
    set(connectionStatusAtom, connected);
    marketEventBus.emit('connection-status', { connected });
  }
);

// 添加到监控列表
export const addToWatchlistAtom = atom(
  null,
  (get, set, symbol: string) => {
    const watchlist = get(watchlistAtom);
    if (!watchlist.includes(symbol)) {
      set(watchlistAtom, [...watchlist, symbol]);
    }
  }
);

// 从监控列表移除
export const removeFromWatchlistAtom = atom(
  null,
  (get, set, symbol: string) => {
    const watchlist = get(watchlistAtom);
    set(watchlistAtom, watchlist.filter(s => s !== symbol));
  }
);

// 模拟数据生成器（开发用）
export const generateMockTickerData = (symbol: string): TickerData => {
  const basePrice = Math.random() * 1000 + 50;
  const change = (Math.random() - 0.5) * 20;
  const changePercent = (change / basePrice) * 100;
  
  return {
    symbol,
    price: basePrice + change,
    change,
    changePercent,
    volume: Math.floor(Math.random() * 10000000),
    high: basePrice + Math.abs(change) + Math.random() * 10,
    low: basePrice - Math.abs(change) - Math.random() * 10,
    open: basePrice,
    close: basePrice + change,
    timestamp: Date.now()
  };
};
