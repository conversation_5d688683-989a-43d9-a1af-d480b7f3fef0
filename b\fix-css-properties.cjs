const fs = require('fs');
const path = require('path');

// CSS属性映射表
const cssPropertyMap = {
  'backgroundColor': 'background-color',
  'borderRadius': 'border-radius',
  'fontSize': 'font-size',
  'fontWeight': 'font-weight',
  'textAlign': 'text-align',
  'marginTop': 'margin-top',
  'marginBottom': 'margin-bottom',
  'marginLeft': 'margin-left',
  'marginRight': 'margin-right',
  'paddingTop': 'padding-top',
  'paddingBottom': 'padding-bottom',
  'paddingLeft': 'padding-left',
  'paddingRight': 'padding-right',
  'borderBottom': 'border-bottom',
  'borderTop': 'border-top',
  'borderLeft': 'border-left',
  'borderRight': 'border-right',
  'overflowY': 'overflow-y',
  'overflowX': 'overflow-x',
  'textDecoration': 'text-decoration',
  'flexDirection': 'flex-direction',
  'alignItems': 'align-items',
  'justifyContent': 'justify-content',
  'flexWrap': 'flex-wrap',
  'gridTemplateColumns': 'grid-template-columns',
  'gridGap': 'grid-gap',
  'lineHeight': 'line-height',
  'boxShadow': 'box-shadow',
  'borderColor': 'border-color',
  'borderStyle': 'border-style',
  'borderWidth': 'border-width',
  'maxWidth': 'max-width',
  'minWidth': 'min-width',
  'maxHeight': 'max-height',
  'minHeight': 'min-height',
  'whiteSpace': 'white-space',
  'wordBreak': 'word-break',
  'alignSelf': 'align-self',
  'justifySelf': 'justify-self',
  'pointerEvents': 'pointer-events',
  'userSelect': 'user-select',
  'objectFit': 'object-fit',
  'textTransform': 'text-transform',
  'letterSpacing': 'letter-spacing',
  'zIndex': 'z-index'
};

function fixCssPropertiesInFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // 只处理style对象中的属性
  const styleRegex = /style\s*=\s*\{\{([^}]+)\}\}/g;
  
  content = content.replace(styleRegex, (match, styleContent) => {
    let newStyleContent = styleContent;
    
    for (const [camelCase, kebabCase] of Object.entries(cssPropertyMap)) {
      const regex = new RegExp(`(['"\s])${camelCase}(['":])`, 'g');
      if (regex.test(newStyleContent)) {
        newStyleContent = newStyleContent.replace(regex, `$1'${kebabCase}'$2`);
        modified = true;
      }
    }
    
    return `style={{${newStyleContent}}}`;
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed CSS properties in: ${path.relative(process.cwd(), filePath)}`);
    return true;
  }
  
  return false;
}

function processDirectory(dir) {
  let fixedCount = 0;
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      fixedCount += processDirectory(filePath);
    } else if (stat.isFile() && (file.endsWith('.tsx') || file.endsWith('.jsx'))) {
      if (fixCssPropertiesInFile(filePath)) {
        fixedCount++;
      }
    }
  }
  
  return fixedCount;
}

// 执行修复
console.log('🔧 Fixing CSS property names in TSX/JSX files...\n');
const srcDir = path.join(__dirname, 'src');
const fixedCount = processDirectory(srcDir);
console.log(`\n✨ Fixed ${fixedCount} files with CSS property issues.`);