{"version": 3, "sources": ["../../@codemirror/lint/dist/index.js", "../../codemirror/dist/index.js"], "sourcesContent": ["import { Decoration, showPanel, Editor<PERSON>iew, ViewPlugin, gutter, showTooltip, hoverTooltip, getPanel, logException, WidgetType, GutterMarker } from '@codemirror/view';\nimport { StateEffect, StateField, Facet, combineConfig, RangeSet, RangeSetBuilder } from '@codemirror/state';\nimport elt from 'crelt';\n\nclass SelectedDiagnostic {\n    constructor(from, to, diagnostic) {\n        this.from = from;\n        this.to = to;\n        this.diagnostic = diagnostic;\n    }\n}\nclass LintState {\n    constructor(diagnostics, panel, selected) {\n        this.diagnostics = diagnostics;\n        this.panel = panel;\n        this.selected = selected;\n    }\n    static init(diagnostics, panel, state) {\n        // Filter the list of diagnostics for which to create markers\n        let diagnosticFilter = state.facet(lintConfig).markerFilter;\n        if (diagnosticFilter)\n            diagnostics = diagnosticFilter(diagnostics, state);\n        let sorted = diagnostics.slice().sort((a, b) => a.from - b.from || a.to - b.to);\n        let deco = new RangeSetBuilder(), active = [], pos = 0;\n        for (let i = 0;;) {\n            let next = i == sorted.length ? null : sorted[i];\n            if (!next && !active.length)\n                break;\n            let from, to;\n            if (active.length) {\n                from = pos;\n                to = active.reduce((p, d) => Math.min(p, d.to), next && next.from > from ? next.from : 1e8);\n            }\n            else {\n                from = next.from;\n                to = next.to;\n                active.push(next);\n                i++;\n            }\n            while (i < sorted.length) {\n                let next = sorted[i];\n                if (next.from == from && (next.to > next.from || next.to == from)) {\n                    active.push(next);\n                    i++;\n                    to = Math.min(next.to, to);\n                }\n                else {\n                    to = Math.min(next.from, to);\n                    break;\n                }\n            }\n            let sev = maxSeverity(active);\n            if (active.some(d => d.from == d.to || (d.from == d.to - 1 && state.doc.lineAt(d.from).to == d.from))) {\n                deco.add(from, from, Decoration.widget({\n                    widget: new DiagnosticWidget(sev),\n                    diagnostics: active.slice()\n                }));\n            }\n            else {\n                let markClass = active.reduce((c, d) => d.markClass ? c + \" \" + d.markClass : c, \"\");\n                deco.add(from, to, Decoration.mark({\n                    class: \"cm-lintRange cm-lintRange-\" + sev + markClass,\n                    diagnostics: active.slice(),\n                    inclusiveEnd: active.some(a => a.to > to)\n                }));\n            }\n            pos = to;\n            for (let i = 0; i < active.length; i++)\n                if (active[i].to <= pos)\n                    active.splice(i--, 1);\n        }\n        let set = deco.finish();\n        return new LintState(set, panel, findDiagnostic(set));\n    }\n}\nfunction findDiagnostic(diagnostics, diagnostic = null, after = 0) {\n    let found = null;\n    diagnostics.between(after, 1e9, (from, to, { spec }) => {\n        if (diagnostic && spec.diagnostics.indexOf(diagnostic) < 0)\n            return;\n        if (!found)\n            found = new SelectedDiagnostic(from, to, diagnostic || spec.diagnostics[0]);\n        else if (spec.diagnostics.indexOf(found.diagnostic) < 0)\n            return false;\n        else\n            found = new SelectedDiagnostic(found.from, to, found.diagnostic);\n    });\n    return found;\n}\nfunction hideTooltip(tr, tooltip) {\n    let from = tooltip.pos, to = tooltip.end || from;\n    let result = tr.state.facet(lintConfig).hideOn(tr, from, to);\n    if (result != null)\n        return result;\n    let line = tr.startState.doc.lineAt(tooltip.pos);\n    return !!(tr.effects.some(e => e.is(setDiagnosticsEffect)) || tr.changes.touchesRange(line.from, Math.max(line.to, to)));\n}\nfunction maybeEnableLint(state, effects) {\n    return state.field(lintState, false) ? effects : effects.concat(StateEffect.appendConfig.of(lintExtensions));\n}\n/**\nReturns a transaction spec which updates the current set of\ndiagnostics, and enables the lint extension if if wasn't already\nactive.\n*/\nfunction setDiagnostics(state, diagnostics) {\n    return {\n        effects: maybeEnableLint(state, [setDiagnosticsEffect.of(diagnostics)])\n    };\n}\n/**\nThe state effect that updates the set of active diagnostics. Can\nbe useful when writing an extension that needs to track these.\n*/\nconst setDiagnosticsEffect = /*@__PURE__*/StateEffect.define();\nconst togglePanel = /*@__PURE__*/StateEffect.define();\nconst movePanelSelection = /*@__PURE__*/StateEffect.define();\nconst lintState = /*@__PURE__*/StateField.define({\n    create() {\n        return new LintState(Decoration.none, null, null);\n    },\n    update(value, tr) {\n        if (tr.docChanged && value.diagnostics.size) {\n            let mapped = value.diagnostics.map(tr.changes), selected = null, panel = value.panel;\n            if (value.selected) {\n                let selPos = tr.changes.mapPos(value.selected.from, 1);\n                selected = findDiagnostic(mapped, value.selected.diagnostic, selPos) || findDiagnostic(mapped, null, selPos);\n            }\n            if (!mapped.size && panel && tr.state.facet(lintConfig).autoPanel)\n                panel = null;\n            value = new LintState(mapped, panel, selected);\n        }\n        for (let effect of tr.effects) {\n            if (effect.is(setDiagnosticsEffect)) {\n                let panel = !tr.state.facet(lintConfig).autoPanel ? value.panel : effect.value.length ? LintPanel.open : null;\n                value = LintState.init(effect.value, panel, tr.state);\n            }\n            else if (effect.is(togglePanel)) {\n                value = new LintState(value.diagnostics, effect.value ? LintPanel.open : null, value.selected);\n            }\n            else if (effect.is(movePanelSelection)) {\n                value = new LintState(value.diagnostics, value.panel, effect.value);\n            }\n        }\n        return value;\n    },\n    provide: f => [showPanel.from(f, val => val.panel),\n        EditorView.decorations.from(f, s => s.diagnostics)]\n});\n/**\nReturns the number of active lint diagnostics in the given state.\n*/\nfunction diagnosticCount(state) {\n    let lint = state.field(lintState, false);\n    return lint ? lint.diagnostics.size : 0;\n}\nconst activeMark = /*@__PURE__*/Decoration.mark({ class: \"cm-lintRange cm-lintRange-active\" });\nfunction lintTooltip(view, pos, side) {\n    let { diagnostics } = view.state.field(lintState);\n    let found, start = -1, end = -1;\n    diagnostics.between(pos - (side < 0 ? 1 : 0), pos + (side > 0 ? 1 : 0), (from, to, { spec }) => {\n        if (pos >= from && pos <= to &&\n            (from == to || ((pos > from || side > 0) && (pos < to || side < 0)))) {\n            found = spec.diagnostics;\n            start = from;\n            end = to;\n            return false;\n        }\n    });\n    let diagnosticFilter = view.state.facet(lintConfig).tooltipFilter;\n    if (found && diagnosticFilter)\n        found = diagnosticFilter(found, view.state);\n    if (!found)\n        return null;\n    return {\n        pos: start,\n        end: end,\n        above: view.state.doc.lineAt(start).to < end,\n        create() {\n            return { dom: diagnosticsTooltip(view, found) };\n        }\n    };\n}\nfunction diagnosticsTooltip(view, diagnostics) {\n    return elt(\"ul\", { class: \"cm-tooltip-lint\" }, diagnostics.map(d => renderDiagnostic(view, d, false)));\n}\n/**\nCommand to open and focus the lint panel.\n*/\nconst openLintPanel = (view) => {\n    let field = view.state.field(lintState, false);\n    if (!field || !field.panel)\n        view.dispatch({ effects: maybeEnableLint(view.state, [togglePanel.of(true)]) });\n    let panel = getPanel(view, LintPanel.open);\n    if (panel)\n        panel.dom.querySelector(\".cm-panel-lint ul\").focus();\n    return true;\n};\n/**\nCommand to close the lint panel, when open.\n*/\nconst closeLintPanel = (view) => {\n    let field = view.state.field(lintState, false);\n    if (!field || !field.panel)\n        return false;\n    view.dispatch({ effects: togglePanel.of(false) });\n    return true;\n};\n/**\nMove the selection to the next diagnostic.\n*/\nconst nextDiagnostic = (view) => {\n    let field = view.state.field(lintState, false);\n    if (!field)\n        return false;\n    let sel = view.state.selection.main, next = field.diagnostics.iter(sel.to + 1);\n    if (!next.value) {\n        next = field.diagnostics.iter(0);\n        if (!next.value || next.from == sel.from && next.to == sel.to)\n            return false;\n    }\n    view.dispatch({ selection: { anchor: next.from, head: next.to }, scrollIntoView: true });\n    return true;\n};\n/**\nMove the selection to the previous diagnostic.\n*/\nconst previousDiagnostic = (view) => {\n    let { state } = view, field = state.field(lintState, false);\n    if (!field)\n        return false;\n    let sel = state.selection.main;\n    let prevFrom, prevTo, lastFrom, lastTo;\n    field.diagnostics.between(0, state.doc.length, (from, to) => {\n        if (to < sel.to && (prevFrom == null || prevFrom < from)) {\n            prevFrom = from;\n            prevTo = to;\n        }\n        if (lastFrom == null || from > lastFrom) {\n            lastFrom = from;\n            lastTo = to;\n        }\n    });\n    if (lastFrom == null || prevFrom == null && lastFrom == sel.from)\n        return false;\n    view.dispatch({ selection: { anchor: prevFrom !== null && prevFrom !== void 0 ? prevFrom : lastFrom, head: prevTo !== null && prevTo !== void 0 ? prevTo : lastTo }, scrollIntoView: true });\n    return true;\n};\n/**\nA set of default key bindings for the lint functionality.\n\n- Ctrl-Shift-m (Cmd-Shift-m on macOS): [`openLintPanel`](https://codemirror.net/6/docs/ref/#lint.openLintPanel)\n- F8: [`nextDiagnostic`](https://codemirror.net/6/docs/ref/#lint.nextDiagnostic)\n*/\nconst lintKeymap = [\n    { key: \"Mod-Shift-m\", run: openLintPanel, preventDefault: true },\n    { key: \"F8\", run: nextDiagnostic }\n];\nconst lintPlugin = /*@__PURE__*/ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.view = view;\n        this.timeout = -1;\n        this.set = true;\n        let { delay } = view.state.facet(lintConfig);\n        this.lintTime = Date.now() + delay;\n        this.run = this.run.bind(this);\n        this.timeout = setTimeout(this.run, delay);\n    }\n    run() {\n        clearTimeout(this.timeout);\n        let now = Date.now();\n        if (now < this.lintTime - 10) {\n            this.timeout = setTimeout(this.run, this.lintTime - now);\n        }\n        else {\n            this.set = false;\n            let { state } = this.view, { sources } = state.facet(lintConfig);\n            if (sources.length)\n                batchResults(sources.map(s => Promise.resolve(s(this.view))), annotations => {\n                    if (this.view.state.doc == state.doc)\n                        this.view.dispatch(setDiagnostics(this.view.state, annotations.reduce((a, b) => a.concat(b))));\n                }, error => { logException(this.view.state, error); });\n        }\n    }\n    update(update) {\n        let config = update.state.facet(lintConfig);\n        if (update.docChanged || config != update.startState.facet(lintConfig) ||\n            config.needsRefresh && config.needsRefresh(update)) {\n            this.lintTime = Date.now() + config.delay;\n            if (!this.set) {\n                this.set = true;\n                this.timeout = setTimeout(this.run, config.delay);\n            }\n        }\n    }\n    force() {\n        if (this.set) {\n            this.lintTime = Date.now();\n            this.run();\n        }\n    }\n    destroy() {\n        clearTimeout(this.timeout);\n    }\n});\nfunction batchResults(promises, sink, error) {\n    let collected = [], timeout = -1;\n    for (let p of promises)\n        p.then(value => {\n            collected.push(value);\n            clearTimeout(timeout);\n            if (collected.length == promises.length)\n                sink(collected);\n            else\n                timeout = setTimeout(() => sink(collected), 200);\n        }, error);\n}\nconst lintConfig = /*@__PURE__*/Facet.define({\n    combine(input) {\n        return Object.assign({ sources: input.map(i => i.source).filter(x => x != null) }, combineConfig(input.map(i => i.config), {\n            delay: 750,\n            markerFilter: null,\n            tooltipFilter: null,\n            needsRefresh: null,\n            hideOn: () => null,\n        }, {\n            needsRefresh: (a, b) => !a ? b : !b ? a : u => a(u) || b(u)\n        }));\n    }\n});\n/**\nGiven a diagnostic source, this function returns an extension that\nenables linting with that source. It will be called whenever the\neditor is idle (after its content changed). If `null` is given as\nsource, this only configures the lint extension.\n*/\nfunction linter(source, config = {}) {\n    return [\n        lintConfig.of({ source, config }),\n        lintPlugin,\n        lintExtensions\n    ];\n}\n/**\nForces any linters [configured](https://codemirror.net/6/docs/ref/#lint.linter) to run when the\neditor is idle to run right away.\n*/\nfunction forceLinting(view) {\n    let plugin = view.plugin(lintPlugin);\n    if (plugin)\n        plugin.force();\n}\nfunction assignKeys(actions) {\n    let assigned = [];\n    if (actions)\n        actions: for (let { name } of actions) {\n            for (let i = 0; i < name.length; i++) {\n                let ch = name[i];\n                if (/[a-zA-Z]/.test(ch) && !assigned.some(c => c.toLowerCase() == ch.toLowerCase())) {\n                    assigned.push(ch);\n                    continue actions;\n                }\n            }\n            assigned.push(\"\");\n        }\n    return assigned;\n}\nfunction renderDiagnostic(view, diagnostic, inPanel) {\n    var _a;\n    let keys = inPanel ? assignKeys(diagnostic.actions) : [];\n    return elt(\"li\", { class: \"cm-diagnostic cm-diagnostic-\" + diagnostic.severity }, elt(\"span\", { class: \"cm-diagnosticText\" }, diagnostic.renderMessage ? diagnostic.renderMessage(view) : diagnostic.message), (_a = diagnostic.actions) === null || _a === void 0 ? void 0 : _a.map((action, i) => {\n        let fired = false, click = (e) => {\n            e.preventDefault();\n            if (fired)\n                return;\n            fired = true;\n            let found = findDiagnostic(view.state.field(lintState).diagnostics, diagnostic);\n            if (found)\n                action.apply(view, found.from, found.to);\n        };\n        let { name } = action, keyIndex = keys[i] ? name.indexOf(keys[i]) : -1;\n        let nameElt = keyIndex < 0 ? name : [name.slice(0, keyIndex),\n            elt(\"u\", name.slice(keyIndex, keyIndex + 1)),\n            name.slice(keyIndex + 1)];\n        return elt(\"button\", {\n            type: \"button\",\n            class: \"cm-diagnosticAction\",\n            onclick: click,\n            onmousedown: click,\n            \"aria-label\": ` Action: ${name}${keyIndex < 0 ? \"\" : ` (access key \"${keys[i]})\"`}.`\n        }, nameElt);\n    }), diagnostic.source && elt(\"div\", { class: \"cm-diagnosticSource\" }, diagnostic.source));\n}\nclass DiagnosticWidget extends WidgetType {\n    constructor(sev) {\n        super();\n        this.sev = sev;\n    }\n    eq(other) { return other.sev == this.sev; }\n    toDOM() {\n        return elt(\"span\", { class: \"cm-lintPoint cm-lintPoint-\" + this.sev });\n    }\n}\nclass PanelItem {\n    constructor(view, diagnostic) {\n        this.diagnostic = diagnostic;\n        this.id = \"item_\" + Math.floor(Math.random() * 0xffffffff).toString(16);\n        this.dom = renderDiagnostic(view, diagnostic, true);\n        this.dom.id = this.id;\n        this.dom.setAttribute(\"role\", \"option\");\n    }\n}\nclass LintPanel {\n    constructor(view) {\n        this.view = view;\n        this.items = [];\n        let onkeydown = (event) => {\n            if (event.keyCode == 27) { // Escape\n                closeLintPanel(this.view);\n                this.view.focus();\n            }\n            else if (event.keyCode == 38 || event.keyCode == 33) { // ArrowUp, PageUp\n                this.moveSelection((this.selectedIndex - 1 + this.items.length) % this.items.length);\n            }\n            else if (event.keyCode == 40 || event.keyCode == 34) { // ArrowDown, PageDown\n                this.moveSelection((this.selectedIndex + 1) % this.items.length);\n            }\n            else if (event.keyCode == 36) { // Home\n                this.moveSelection(0);\n            }\n            else if (event.keyCode == 35) { // End\n                this.moveSelection(this.items.length - 1);\n            }\n            else if (event.keyCode == 13) { // Enter\n                this.view.focus();\n            }\n            else if (event.keyCode >= 65 && event.keyCode <= 90 && this.selectedIndex >= 0) { // A-Z\n                let { diagnostic } = this.items[this.selectedIndex], keys = assignKeys(diagnostic.actions);\n                for (let i = 0; i < keys.length; i++)\n                    if (keys[i].toUpperCase().charCodeAt(0) == event.keyCode) {\n                        let found = findDiagnostic(this.view.state.field(lintState).diagnostics, diagnostic);\n                        if (found)\n                            diagnostic.actions[i].apply(view, found.from, found.to);\n                    }\n            }\n            else {\n                return;\n            }\n            event.preventDefault();\n        };\n        let onclick = (event) => {\n            for (let i = 0; i < this.items.length; i++) {\n                if (this.items[i].dom.contains(event.target))\n                    this.moveSelection(i);\n            }\n        };\n        this.list = elt(\"ul\", {\n            tabIndex: 0,\n            role: \"listbox\",\n            \"aria-label\": this.view.state.phrase(\"Diagnostics\"),\n            onkeydown,\n            onclick\n        });\n        this.dom = elt(\"div\", { class: \"cm-panel-lint\" }, this.list, elt(\"button\", {\n            type: \"button\",\n            name: \"close\",\n            \"aria-label\": this.view.state.phrase(\"close\"),\n            onclick: () => closeLintPanel(this.view)\n        }, \"×\"));\n        this.update();\n    }\n    get selectedIndex() {\n        let selected = this.view.state.field(lintState).selected;\n        if (!selected)\n            return -1;\n        for (let i = 0; i < this.items.length; i++)\n            if (this.items[i].diagnostic == selected.diagnostic)\n                return i;\n        return -1;\n    }\n    update() {\n        let { diagnostics, selected } = this.view.state.field(lintState);\n        let i = 0, needsSync = false, newSelectedItem = null;\n        let seen = new Set();\n        diagnostics.between(0, this.view.state.doc.length, (_start, _end, { spec }) => {\n            for (let diagnostic of spec.diagnostics) {\n                if (seen.has(diagnostic))\n                    continue;\n                seen.add(diagnostic);\n                let found = -1, item;\n                for (let j = i; j < this.items.length; j++)\n                    if (this.items[j].diagnostic == diagnostic) {\n                        found = j;\n                        break;\n                    }\n                if (found < 0) {\n                    item = new PanelItem(this.view, diagnostic);\n                    this.items.splice(i, 0, item);\n                    needsSync = true;\n                }\n                else {\n                    item = this.items[found];\n                    if (found > i) {\n                        this.items.splice(i, found - i);\n                        needsSync = true;\n                    }\n                }\n                if (selected && item.diagnostic == selected.diagnostic) {\n                    if (!item.dom.hasAttribute(\"aria-selected\")) {\n                        item.dom.setAttribute(\"aria-selected\", \"true\");\n                        newSelectedItem = item;\n                    }\n                }\n                else if (item.dom.hasAttribute(\"aria-selected\")) {\n                    item.dom.removeAttribute(\"aria-selected\");\n                }\n                i++;\n            }\n        });\n        while (i < this.items.length && !(this.items.length == 1 && this.items[0].diagnostic.from < 0)) {\n            needsSync = true;\n            this.items.pop();\n        }\n        if (this.items.length == 0) {\n            this.items.push(new PanelItem(this.view, {\n                from: -1, to: -1,\n                severity: \"info\",\n                message: this.view.state.phrase(\"No diagnostics\")\n            }));\n            needsSync = true;\n        }\n        if (newSelectedItem) {\n            this.list.setAttribute(\"aria-activedescendant\", newSelectedItem.id);\n            this.view.requestMeasure({\n                key: this,\n                read: () => ({ sel: newSelectedItem.dom.getBoundingClientRect(), panel: this.list.getBoundingClientRect() }),\n                write: ({ sel, panel }) => {\n                    let scaleY = panel.height / this.list.offsetHeight;\n                    if (sel.top < panel.top)\n                        this.list.scrollTop -= (panel.top - sel.top) / scaleY;\n                    else if (sel.bottom > panel.bottom)\n                        this.list.scrollTop += (sel.bottom - panel.bottom) / scaleY;\n                }\n            });\n        }\n        else if (this.selectedIndex < 0) {\n            this.list.removeAttribute(\"aria-activedescendant\");\n        }\n        if (needsSync)\n            this.sync();\n    }\n    sync() {\n        let domPos = this.list.firstChild;\n        function rm() {\n            let prev = domPos;\n            domPos = prev.nextSibling;\n            prev.remove();\n        }\n        for (let item of this.items) {\n            if (item.dom.parentNode == this.list) {\n                while (domPos != item.dom)\n                    rm();\n                domPos = item.dom.nextSibling;\n            }\n            else {\n                this.list.insertBefore(item.dom, domPos);\n            }\n        }\n        while (domPos)\n            rm();\n    }\n    moveSelection(selectedIndex) {\n        if (this.selectedIndex < 0)\n            return;\n        let field = this.view.state.field(lintState);\n        let selection = findDiagnostic(field.diagnostics, this.items[selectedIndex].diagnostic);\n        if (!selection)\n            return;\n        this.view.dispatch({\n            selection: { anchor: selection.from, head: selection.to },\n            scrollIntoView: true,\n            effects: movePanelSelection.of(selection)\n        });\n    }\n    static open(view) { return new LintPanel(view); }\n}\nfunction svg(content, attrs = `viewBox=\"0 0 40 40\"`) {\n    return `url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" ${attrs}>${encodeURIComponent(content)}</svg>')`;\n}\nfunction underline(color) {\n    return svg(`<path d=\"m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0\" stroke=\"${color}\" fill=\"none\" stroke-width=\".7\"/>`, `width=\"6\" height=\"3\"`);\n}\nconst baseTheme = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-diagnostic\": {\n        padding: \"3px 6px 3px 8px\",\n        marginLeft: \"-1px\",\n        display: \"block\",\n        whiteSpace: \"pre-wrap\"\n    },\n    \".cm-diagnostic-error\": { borderLeft: \"5px solid #d11\" },\n    \".cm-diagnostic-warning\": { borderLeft: \"5px solid orange\" },\n    \".cm-diagnostic-info\": { borderLeft: \"5px solid #999\" },\n    \".cm-diagnostic-hint\": { borderLeft: \"5px solid #66d\" },\n    \".cm-diagnosticAction\": {\n        font: \"inherit\",\n        border: \"none\",\n        padding: \"2px 4px\",\n        backgroundColor: \"#444\",\n        color: \"white\",\n        borderRadius: \"3px\",\n        marginLeft: \"8px\",\n        cursor: \"pointer\"\n    },\n    \".cm-diagnosticSource\": {\n        fontSize: \"70%\",\n        opacity: .7\n    },\n    \".cm-lintRange\": {\n        backgroundPosition: \"left bottom\",\n        backgroundRepeat: \"repeat-x\",\n        paddingBottom: \"0.7px\",\n    },\n    \".cm-lintRange-error\": { backgroundImage: /*@__PURE__*/underline(\"#d11\") },\n    \".cm-lintRange-warning\": { backgroundImage: /*@__PURE__*/underline(\"orange\") },\n    \".cm-lintRange-info\": { backgroundImage: /*@__PURE__*/underline(\"#999\") },\n    \".cm-lintRange-hint\": { backgroundImage: /*@__PURE__*/underline(\"#66d\") },\n    \".cm-lintRange-active\": { backgroundColor: \"#ffdd9980\" },\n    \".cm-tooltip-lint\": {\n        padding: 0,\n        margin: 0\n    },\n    \".cm-lintPoint\": {\n        position: \"relative\",\n        \"&:after\": {\n            content: '\"\"',\n            position: \"absolute\",\n            bottom: 0,\n            left: \"-2px\",\n            borderLeft: \"3px solid transparent\",\n            borderRight: \"3px solid transparent\",\n            borderBottom: \"4px solid #d11\"\n        }\n    },\n    \".cm-lintPoint-warning\": {\n        \"&:after\": { borderBottomColor: \"orange\" }\n    },\n    \".cm-lintPoint-info\": {\n        \"&:after\": { borderBottomColor: \"#999\" }\n    },\n    \".cm-lintPoint-hint\": {\n        \"&:after\": { borderBottomColor: \"#66d\" }\n    },\n    \".cm-panel.cm-panel-lint\": {\n        position: \"relative\",\n        \"& ul\": {\n            maxHeight: \"100px\",\n            overflowY: \"auto\",\n            \"& [aria-selected]\": {\n                backgroundColor: \"#ddd\",\n                \"& u\": { textDecoration: \"underline\" }\n            },\n            \"&:focus [aria-selected]\": {\n                background_fallback: \"#bdf\",\n                backgroundColor: \"Highlight\",\n                color_fallback: \"white\",\n                color: \"HighlightText\"\n            },\n            \"& u\": { textDecoration: \"none\" },\n            padding: 0,\n            margin: 0\n        },\n        \"& [name=close]\": {\n            position: \"absolute\",\n            top: \"0\",\n            right: \"2px\",\n            background: \"inherit\",\n            border: \"none\",\n            font: \"inherit\",\n            padding: 0,\n            margin: 0\n        }\n    }\n});\nfunction severityWeight(sev) {\n    return sev == \"error\" ? 4 : sev == \"warning\" ? 3 : sev == \"info\" ? 2 : 1;\n}\nfunction maxSeverity(diagnostics) {\n    let sev = \"hint\", weight = 1;\n    for (let d of diagnostics) {\n        let w = severityWeight(d.severity);\n        if (w > weight) {\n            weight = w;\n            sev = d.severity;\n        }\n    }\n    return sev;\n}\nclass LintGutterMarker extends GutterMarker {\n    constructor(diagnostics) {\n        super();\n        this.diagnostics = diagnostics;\n        this.severity = maxSeverity(diagnostics);\n    }\n    toDOM(view) {\n        let elt = document.createElement(\"div\");\n        elt.className = \"cm-lint-marker cm-lint-marker-\" + this.severity;\n        let diagnostics = this.diagnostics;\n        let diagnosticsFilter = view.state.facet(lintGutterConfig).tooltipFilter;\n        if (diagnosticsFilter)\n            diagnostics = diagnosticsFilter(diagnostics, view.state);\n        if (diagnostics.length)\n            elt.onmouseover = () => gutterMarkerMouseOver(view, elt, diagnostics);\n        return elt;\n    }\n}\nfunction trackHoverOn(view, marker) {\n    let mousemove = (event) => {\n        let rect = marker.getBoundingClientRect();\n        if (event.clientX > rect.left - 10 /* Hover.Margin */ && event.clientX < rect.right + 10 /* Hover.Margin */ &&\n            event.clientY > rect.top - 10 /* Hover.Margin */ && event.clientY < rect.bottom + 10 /* Hover.Margin */)\n            return;\n        for (let target = event.target; target; target = target.parentNode) {\n            if (target.nodeType == 1 && target.classList.contains(\"cm-tooltip-lint\"))\n                return;\n        }\n        window.removeEventListener(\"mousemove\", mousemove);\n        if (view.state.field(lintGutterTooltip))\n            view.dispatch({ effects: setLintGutterTooltip.of(null) });\n    };\n    window.addEventListener(\"mousemove\", mousemove);\n}\nfunction gutterMarkerMouseOver(view, marker, diagnostics) {\n    function hovered() {\n        let line = view.elementAtHeight(marker.getBoundingClientRect().top + 5 - view.documentTop);\n        const linePos = view.coordsAtPos(line.from);\n        if (linePos) {\n            view.dispatch({ effects: setLintGutterTooltip.of({\n                    pos: line.from,\n                    above: false,\n                    clip: false,\n                    create() {\n                        return {\n                            dom: diagnosticsTooltip(view, diagnostics),\n                            getCoords: () => marker.getBoundingClientRect()\n                        };\n                    }\n                }) });\n        }\n        marker.onmouseout = marker.onmousemove = null;\n        trackHoverOn(view, marker);\n    }\n    let { hoverTime } = view.state.facet(lintGutterConfig);\n    let hoverTimeout = setTimeout(hovered, hoverTime);\n    marker.onmouseout = () => {\n        clearTimeout(hoverTimeout);\n        marker.onmouseout = marker.onmousemove = null;\n    };\n    marker.onmousemove = () => {\n        clearTimeout(hoverTimeout);\n        hoverTimeout = setTimeout(hovered, hoverTime);\n    };\n}\nfunction markersForDiagnostics(doc, diagnostics) {\n    let byLine = Object.create(null);\n    for (let diagnostic of diagnostics) {\n        let line = doc.lineAt(diagnostic.from);\n        (byLine[line.from] || (byLine[line.from] = [])).push(diagnostic);\n    }\n    let markers = [];\n    for (let line in byLine) {\n        markers.push(new LintGutterMarker(byLine[line]).range(+line));\n    }\n    return RangeSet.of(markers, true);\n}\nconst lintGutterExtension = /*@__PURE__*/gutter({\n    class: \"cm-gutter-lint\",\n    markers: view => view.state.field(lintGutterMarkers),\n    widgetMarker: (view, widget, block) => {\n        let diagnostics = [];\n        view.state.field(lintGutterMarkers).between(block.from, block.to, (from, to, value) => {\n            if (from > block.from && from < block.to)\n                diagnostics.push(...value.diagnostics);\n        });\n        return diagnostics.length ? new LintGutterMarker(diagnostics) : null;\n    }\n});\nconst lintGutterMarkers = /*@__PURE__*/StateField.define({\n    create() {\n        return RangeSet.empty;\n    },\n    update(markers, tr) {\n        markers = markers.map(tr.changes);\n        let diagnosticFilter = tr.state.facet(lintGutterConfig).markerFilter;\n        for (let effect of tr.effects) {\n            if (effect.is(setDiagnosticsEffect)) {\n                let diagnostics = effect.value;\n                if (diagnosticFilter)\n                    diagnostics = diagnosticFilter(diagnostics || [], tr.state);\n                markers = markersForDiagnostics(tr.state.doc, diagnostics.slice(0));\n            }\n        }\n        return markers;\n    }\n});\nconst setLintGutterTooltip = /*@__PURE__*/StateEffect.define();\nconst lintGutterTooltip = /*@__PURE__*/StateField.define({\n    create() { return null; },\n    update(tooltip, tr) {\n        if (tooltip && tr.docChanged)\n            tooltip = hideTooltip(tr, tooltip) ? null : Object.assign(Object.assign({}, tooltip), { pos: tr.changes.mapPos(tooltip.pos) });\n        return tr.effects.reduce((t, e) => e.is(setLintGutterTooltip) ? e.value : t, tooltip);\n    },\n    provide: field => showTooltip.from(field)\n});\nconst lintGutterTheme = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-gutter-lint\": {\n        width: \"1.4em\",\n        \"& .cm-gutterElement\": {\n            padding: \".2em\"\n        }\n    },\n    \".cm-lint-marker\": {\n        width: \"1em\",\n        height: \"1em\"\n    },\n    \".cm-lint-marker-info\": {\n        content: /*@__PURE__*/svg(`<path fill=\"#aaf\" stroke=\"#77e\" stroke-width=\"6\" stroke-linejoin=\"round\" d=\"M5 5L35 5L35 35L5 35Z\"/>`)\n    },\n    \".cm-lint-marker-warning\": {\n        content: /*@__PURE__*/svg(`<path fill=\"#fe8\" stroke=\"#fd7\" stroke-width=\"6\" stroke-linejoin=\"round\" d=\"M20 6L37 35L3 35Z\"/>`),\n    },\n    \".cm-lint-marker-error\": {\n        content: /*@__PURE__*/svg(`<circle cx=\"20\" cy=\"20\" r=\"15\" fill=\"#f87\" stroke=\"#f43\" stroke-width=\"6\"/>`)\n    },\n});\nconst lintExtensions = [\n    lintState,\n    /*@__PURE__*/EditorView.decorations.compute([lintState], state => {\n        let { selected, panel } = state.field(lintState);\n        return !selected || !panel || selected.from == selected.to ? Decoration.none : Decoration.set([\n            activeMark.range(selected.from, selected.to)\n        ]);\n    }),\n    /*@__PURE__*/hoverTooltip(lintTooltip, { hideOn: hideTooltip }),\n    baseTheme\n];\nconst lintGutterConfig = /*@__PURE__*/Facet.define({\n    combine(configs) {\n        return combineConfig(configs, {\n            hoverTime: 300 /* Hover.Time */,\n            markerFilter: null,\n            tooltipFilter: null\n        });\n    }\n});\n/**\nReturns an extension that installs a gutter showing markers for\neach line that has diagnostics, which can be hovered over to see\nthe diagnostics.\n*/\nfunction lintGutter(config = {}) {\n    return [lintGutterConfig.of(config), lintGutterMarkers, lintGutterExtension, lintGutterTheme, lintGutterTooltip];\n}\n/**\nIterate over the marked diagnostics for the given editor state,\ncalling `f` for each of them. Note that, if the document changed\nsince the diagnostics were created, the `Diagnostic` object will\nhold the original outdated position, whereas the `to` and `from`\narguments hold the diagnostic's current position.\n*/\nfunction forEachDiagnostic(state, f) {\n    let lState = state.field(lintState, false);\n    if (lState && lState.diagnostics.size) {\n        let pending = [], pendingStart = [], lastEnd = -1;\n        for (let iter = RangeSet.iter([lState.diagnostics]);; iter.next()) {\n            for (let i = 0; i < pending.length; i++)\n                if (!iter.value || iter.value.spec.diagnostics.indexOf(pending[i]) < 0) {\n                    f(pending[i], pendingStart[i], lastEnd);\n                    pending.splice(i, 1);\n                    pendingStart.splice(i--, 1);\n                }\n            if (!iter.value)\n                break;\n            for (let d of iter.value.spec.diagnostics)\n                if (pending.indexOf(d) < 0) {\n                    pending.push(d);\n                    pendingStart.push(iter.from);\n                }\n            lastEnd = iter.to;\n        }\n    }\n}\n\nexport { closeLintPanel, diagnosticCount, forEachDiagnostic, forceLinting, lintGutter, lintKeymap, linter, nextDiagnostic, openLintPanel, previousDiagnostic, setDiagnostics, setDiagnosticsEffect };\n", "import { lineNumbers, highlightActiveLineGutter, highlightSpecialChars, drawSelection, dropCursor, rectangularSelection, crosshairCursor, highlightActiveLine, keymap } from '@codemirror/view';\nexport { EditorView } from '@codemirror/view';\nimport { EditorState } from '@codemirror/state';\nimport { foldGutter, indentOnInput, syntaxHighlighting, defaultHighlightStyle, bracketMatching, foldKeymap } from '@codemirror/language';\nimport { history, defaultKeymap, historyKeymap } from '@codemirror/commands';\nimport { highlightSelectionMatches, searchKeymap } from '@codemirror/search';\nimport { closeBrackets, autocompletion, closeBracketsKeymap, completionKeymap } from '@codemirror/autocomplete';\nimport { lintKeymap } from '@codemirror/lint';\n\n// (The superfluous function calls around the list of extensions work\n// around current limitations in tree-shaking software.)\n/**\nThis is an extension value that just pulls together a number of\nextensions that you might want in a basic editor. It is meant as a\nconvenient helper to quickly set up CodeMirror without installing\nand importing a lot of separate packages.\n\nSpecifically, it includes...\n\n - [the default command bindings](https://codemirror.net/6/docs/ref/#commands.defaultKeymap)\n - [line numbers](https://codemirror.net/6/docs/ref/#view.lineNumbers)\n - [special character highlighting](https://codemirror.net/6/docs/ref/#view.highlightSpecialChars)\n - [the undo history](https://codemirror.net/6/docs/ref/#commands.history)\n - [a fold gutter](https://codemirror.net/6/docs/ref/#language.foldGutter)\n - [custom selection drawing](https://codemirror.net/6/docs/ref/#view.drawSelection)\n - [drop cursor](https://codemirror.net/6/docs/ref/#view.dropCursor)\n - [multiple selections](https://codemirror.net/6/docs/ref/#state.EditorState^allowMultipleSelections)\n - [reindentation on input](https://codemirror.net/6/docs/ref/#language.indentOnInput)\n - [the default highlight style](https://codemirror.net/6/docs/ref/#language.defaultHighlightStyle) (as fallback)\n - [bracket matching](https://codemirror.net/6/docs/ref/#language.bracketMatching)\n - [bracket closing](https://codemirror.net/6/docs/ref/#autocomplete.closeBrackets)\n - [autocompletion](https://codemirror.net/6/docs/ref/#autocomplete.autocompletion)\n - [rectangular selection](https://codemirror.net/6/docs/ref/#view.rectangularSelection) and [crosshair cursor](https://codemirror.net/6/docs/ref/#view.crosshairCursor)\n - [active line highlighting](https://codemirror.net/6/docs/ref/#view.highlightActiveLine)\n - [active line gutter highlighting](https://codemirror.net/6/docs/ref/#view.highlightActiveLineGutter)\n - [selection match highlighting](https://codemirror.net/6/docs/ref/#search.highlightSelectionMatches)\n - [search](https://codemirror.net/6/docs/ref/#search.searchKeymap)\n - [linting](https://codemirror.net/6/docs/ref/#lint.lintKeymap)\n\n(You'll probably want to add some language package to your setup\ntoo.)\n\nThis extension does not allow customization. The idea is that,\nonce you decide you want to configure your editor more precisely,\nyou take this package's source (which is just a bunch of imports\nand an array literal), copy it into your own code, and adjust it\nas desired.\n*/\nconst basicSetup = /*@__PURE__*/(() => [\n    lineNumbers(),\n    highlightActiveLineGutter(),\n    highlightSpecialChars(),\n    history(),\n    foldGutter(),\n    drawSelection(),\n    dropCursor(),\n    EditorState.allowMultipleSelections.of(true),\n    indentOnInput(),\n    syntaxHighlighting(defaultHighlightStyle, { fallback: true }),\n    bracketMatching(),\n    closeBrackets(),\n    autocompletion(),\n    rectangularSelection(),\n    crosshairCursor(),\n    highlightActiveLine(),\n    highlightSelectionMatches(),\n    keymap.of([\n        ...closeBracketsKeymap,\n        ...defaultKeymap,\n        ...searchKeymap,\n        ...historyKeymap,\n        ...foldKeymap,\n        ...completionKeymap,\n        ...lintKeymap\n    ])\n])();\n/**\nA minimal set of extensions to create a functional editor. Only\nincludes [the default keymap](https://codemirror.net/6/docs/ref/#commands.defaultKeymap), [undo\nhistory](https://codemirror.net/6/docs/ref/#commands.history), [special character\nhighlighting](https://codemirror.net/6/docs/ref/#view.highlightSpecialChars), [custom selection\ndrawing](https://codemirror.net/6/docs/ref/#view.drawSelection), and [default highlight\nstyle](https://codemirror.net/6/docs/ref/#language.defaultHighlightStyle).\n*/\nconst minimalSetup = /*@__PURE__*/(() => [\n    highlightSpecialChars(),\n    history(),\n    drawSelection(),\n    syntaxHighlighting(defaultHighlightStyle, { fallback: true }),\n    keymap.of([\n        ...defaultKeymap,\n        ...historyKeymap,\n    ])\n])();\n\nexport { basicSetup, minimalSetup };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,qBAAN,MAAyB;AAAA,EACrB,YAAY,MAAM,IAAI,YAAY;AAC9B,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,aAAa;AAAA,EACtB;AACJ;AACA,IAAM,YAAN,MAAM,WAAU;AAAA,EACZ,YAAY,aAAa,OAAO,UAAU;AACtC,SAAK,cAAc;AACnB,SAAK,QAAQ;AACb,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,OAAO,KAAK,aAAa,OAAO,OAAO;AAEnC,QAAI,mBAAmB,MAAM,MAAM,UAAU,EAAE;AAC/C,QAAI;AACA,oBAAc,iBAAiB,aAAa,KAAK;AACrD,QAAI,SAAS,YAAY,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;AAC9E,QAAI,OAAO,IAAI,gBAAgB,GAAG,SAAS,CAAC,GAAG,MAAM;AACrD,aAAS,IAAI,OAAK;AACd,UAAI,OAAO,KAAK,OAAO,SAAS,OAAO,OAAO,CAAC;AAC/C,UAAI,CAAC,QAAQ,CAAC,OAAO;AACjB;AACJ,UAAI,MAAM;AACV,UAAI,OAAO,QAAQ;AACf,eAAO;AACP,aAAK,OAAO,OAAO,CAAC,GAAG,MAAM,KAAK,IAAI,GAAG,EAAE,EAAE,GAAG,QAAQ,KAAK,OAAO,OAAO,KAAK,OAAO,GAAG;AAAA,MAC9F,OACK;AACD,eAAO,KAAK;AACZ,aAAK,KAAK;AACV,eAAO,KAAK,IAAI;AAChB;AAAA,MACJ;AACA,aAAO,IAAI,OAAO,QAAQ;AACtB,YAAIA,QAAO,OAAO,CAAC;AACnB,YAAIA,MAAK,QAAQ,SAASA,MAAK,KAAKA,MAAK,QAAQA,MAAK,MAAM,OAAO;AAC/D,iBAAO,KAAKA,KAAI;AAChB;AACA,eAAK,KAAK,IAAIA,MAAK,IAAI,EAAE;AAAA,QAC7B,OACK;AACD,eAAK,KAAK,IAAIA,MAAK,MAAM,EAAE;AAC3B;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,MAAM,YAAY,MAAM;AAC5B,UAAI,OAAO,KAAK,OAAK,EAAE,QAAQ,EAAE,MAAO,EAAE,QAAQ,EAAE,KAAK,KAAK,MAAM,IAAI,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAK,GAAG;AACnG,aAAK,IAAI,MAAM,MAAM,WAAW,OAAO;AAAA,UACnC,QAAQ,IAAI,iBAAiB,GAAG;AAAA,UAChC,aAAa,OAAO,MAAM;AAAA,QAC9B,CAAC,CAAC;AAAA,MACN,OACK;AACD,YAAI,YAAY,OAAO,OAAO,CAAC,GAAG,MAAM,EAAE,YAAY,IAAI,MAAM,EAAE,YAAY,GAAG,EAAE;AACnF,aAAK,IAAI,MAAM,IAAI,WAAW,KAAK;AAAA,UAC/B,OAAO,+BAA+B,MAAM;AAAA,UAC5C,aAAa,OAAO,MAAM;AAAA,UAC1B,cAAc,OAAO,KAAK,OAAK,EAAE,KAAK,EAAE;AAAA,QAC5C,CAAC,CAAC;AAAA,MACN;AACA,YAAM;AACN,eAASC,KAAI,GAAGA,KAAI,OAAO,QAAQA;AAC/B,YAAI,OAAOA,EAAC,EAAE,MAAM;AAChB,iBAAO,OAAOA,MAAK,CAAC;AAAA,IAChC;AACA,QAAI,MAAM,KAAK,OAAO;AACtB,WAAO,IAAI,WAAU,KAAK,OAAO,eAAe,GAAG,CAAC;AAAA,EACxD;AACJ;AACA,SAAS,eAAe,aAAa,aAAa,MAAM,QAAQ,GAAG;AAC/D,MAAI,QAAQ;AACZ,cAAY,QAAQ,OAAO,KAAK,CAAC,MAAM,IAAI,EAAE,KAAK,MAAM;AACpD,QAAI,cAAc,KAAK,YAAY,QAAQ,UAAU,IAAI;AACrD;AACJ,QAAI,CAAC;AACD,cAAQ,IAAI,mBAAmB,MAAM,IAAI,cAAc,KAAK,YAAY,CAAC,CAAC;AAAA,aACrE,KAAK,YAAY,QAAQ,MAAM,UAAU,IAAI;AAClD,aAAO;AAAA;AAEP,cAAQ,IAAI,mBAAmB,MAAM,MAAM,IAAI,MAAM,UAAU;AAAA,EACvE,CAAC;AACD,SAAO;AACX;AACA,SAAS,YAAY,IAAI,SAAS;AAC9B,MAAI,OAAO,QAAQ,KAAK,KAAK,QAAQ,OAAO;AAC5C,MAAI,SAAS,GAAG,MAAM,MAAM,UAAU,EAAE,OAAO,IAAI,MAAM,EAAE;AAC3D,MAAI,UAAU;AACV,WAAO;AACX,MAAI,OAAO,GAAG,WAAW,IAAI,OAAO,QAAQ,GAAG;AAC/C,SAAO,CAAC,EAAE,GAAG,QAAQ,KAAK,OAAK,EAAE,GAAG,oBAAoB,CAAC,KAAK,GAAG,QAAQ,aAAa,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC;AAC1H;AACA,SAAS,gBAAgB,OAAO,SAAS;AACrC,SAAO,MAAM,MAAM,WAAW,KAAK,IAAI,UAAU,QAAQ,OAAO,YAAY,aAAa,GAAG,cAAc,CAAC;AAC/G;AAMA,SAAS,eAAe,OAAO,aAAa;AACxC,SAAO;AAAA,IACH,SAAS,gBAAgB,OAAO,CAAC,qBAAqB,GAAG,WAAW,CAAC,CAAC;AAAA,EAC1E;AACJ;AAKA,IAAM,uBAAoC,YAAY,OAAO;AAC7D,IAAM,cAA2B,YAAY,OAAO;AACpD,IAAM,qBAAkC,YAAY,OAAO;AAC3D,IAAM,YAAyB,WAAW,OAAO;AAAA,EAC7C,SAAS;AACL,WAAO,IAAI,UAAU,WAAW,MAAM,MAAM,IAAI;AAAA,EACpD;AAAA,EACA,OAAO,OAAO,IAAI;AACd,QAAI,GAAG,cAAc,MAAM,YAAY,MAAM;AACzC,UAAI,SAAS,MAAM,YAAY,IAAI,GAAG,OAAO,GAAG,WAAW,MAAM,QAAQ,MAAM;AAC/E,UAAI,MAAM,UAAU;AAChB,YAAI,SAAS,GAAG,QAAQ,OAAO,MAAM,SAAS,MAAM,CAAC;AACrD,mBAAW,eAAe,QAAQ,MAAM,SAAS,YAAY,MAAM,KAAK,eAAe,QAAQ,MAAM,MAAM;AAAA,MAC/G;AACA,UAAI,CAAC,OAAO,QAAQ,SAAS,GAAG,MAAM,MAAM,UAAU,EAAE;AACpD,gBAAQ;AACZ,cAAQ,IAAI,UAAU,QAAQ,OAAO,QAAQ;AAAA,IACjD;AACA,aAAS,UAAU,GAAG,SAAS;AAC3B,UAAI,OAAO,GAAG,oBAAoB,GAAG;AACjC,YAAI,QAAQ,CAAC,GAAG,MAAM,MAAM,UAAU,EAAE,YAAY,MAAM,QAAQ,OAAO,MAAM,SAAS,UAAU,OAAO;AACzG,gBAAQ,UAAU,KAAK,OAAO,OAAO,OAAO,GAAG,KAAK;AAAA,MACxD,WACS,OAAO,GAAG,WAAW,GAAG;AAC7B,gBAAQ,IAAI,UAAU,MAAM,aAAa,OAAO,QAAQ,UAAU,OAAO,MAAM,MAAM,QAAQ;AAAA,MACjG,WACS,OAAO,GAAG,kBAAkB,GAAG;AACpC,gBAAQ,IAAI,UAAU,MAAM,aAAa,MAAM,OAAO,OAAO,KAAK;AAAA,MACtE;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,SAAS,OAAK;AAAA,IAAC,UAAU,KAAK,GAAG,SAAO,IAAI,KAAK;AAAA,IAC7C,WAAW,YAAY,KAAK,GAAG,OAAK,EAAE,WAAW;AAAA,EAAC;AAC1D,CAAC;AAQD,IAAM,aAA0B,WAAW,KAAK,EAAE,OAAO,mCAAmC,CAAC;AAC7F,SAAS,YAAY,MAAM,KAAK,MAAM;AAClC,MAAI,EAAE,YAAY,IAAI,KAAK,MAAM,MAAM,SAAS;AAChD,MAAI,OAAO,QAAQ,IAAI,MAAM;AAC7B,cAAY,QAAQ,OAAO,OAAO,IAAI,IAAI,IAAI,OAAO,OAAO,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,KAAK,MAAM;AAC5F,QAAI,OAAO,QAAQ,OAAO,OACrB,QAAQ,OAAQ,MAAM,QAAQ,OAAO,OAAO,MAAM,MAAM,OAAO,KAAM;AACtE,cAAQ,KAAK;AACb,cAAQ;AACR,YAAM;AACN,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACD,MAAI,mBAAmB,KAAK,MAAM,MAAM,UAAU,EAAE;AACpD,MAAI,SAAS;AACT,YAAQ,iBAAiB,OAAO,KAAK,KAAK;AAC9C,MAAI,CAAC;AACD,WAAO;AACX,SAAO;AAAA,IACH,KAAK;AAAA,IACL;AAAA,IACA,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,EAAE,KAAK;AAAA,IACzC,SAAS;AACL,aAAO,EAAE,KAAK,mBAAmB,MAAM,KAAK,EAAE;AAAA,IAClD;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmB,MAAM,aAAa;AAC3C,SAAO,MAAI,MAAM,EAAE,OAAO,kBAAkB,GAAG,YAAY,IAAI,OAAK,iBAAiB,MAAM,GAAG,KAAK,CAAC,CAAC;AACzG;AAIA,IAAM,gBAAgB,CAAC,SAAS;AAC5B,MAAI,QAAQ,KAAK,MAAM,MAAM,WAAW,KAAK;AAC7C,MAAI,CAAC,SAAS,CAAC,MAAM;AACjB,SAAK,SAAS,EAAE,SAAS,gBAAgB,KAAK,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC;AAClF,MAAI,QAAQ,SAAS,MAAM,UAAU,IAAI;AACzC,MAAI;AACA,UAAM,IAAI,cAAc,mBAAmB,EAAE,MAAM;AACvD,SAAO;AACX;AAIA,IAAM,iBAAiB,CAAC,SAAS;AAC7B,MAAI,QAAQ,KAAK,MAAM,MAAM,WAAW,KAAK;AAC7C,MAAI,CAAC,SAAS,CAAC,MAAM;AACjB,WAAO;AACX,OAAK,SAAS,EAAE,SAAS,YAAY,GAAG,KAAK,EAAE,CAAC;AAChD,SAAO;AACX;AAIA,IAAM,iBAAiB,CAAC,SAAS;AAC7B,MAAI,QAAQ,KAAK,MAAM,MAAM,WAAW,KAAK;AAC7C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,MAAM,KAAK,MAAM,UAAU,MAAM,OAAO,MAAM,YAAY,KAAK,IAAI,KAAK,CAAC;AAC7E,MAAI,CAAC,KAAK,OAAO;AACb,WAAO,MAAM,YAAY,KAAK,CAAC;AAC/B,QAAI,CAAC,KAAK,SAAS,KAAK,QAAQ,IAAI,QAAQ,KAAK,MAAM,IAAI;AACvD,aAAO;AAAA,EACf;AACA,OAAK,SAAS,EAAE,WAAW,EAAE,QAAQ,KAAK,MAAM,MAAM,KAAK,GAAG,GAAG,gBAAgB,KAAK,CAAC;AACvF,SAAO;AACX;AA+BA,IAAM,aAAa;AAAA,EACf,EAAE,KAAK,eAAe,KAAK,eAAe,gBAAgB,KAAK;AAAA,EAC/D,EAAE,KAAK,MAAM,KAAK,eAAe;AACrC;AACA,IAAM,aAA0B,WAAW,UAAU,MAAM;AAAA,EACvD,YAAY,MAAM;AACd,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,MAAM;AACX,QAAI,EAAE,MAAM,IAAI,KAAK,MAAM,MAAM,UAAU;AAC3C,SAAK,WAAW,KAAK,IAAI,IAAI;AAC7B,SAAK,MAAM,KAAK,IAAI,KAAK,IAAI;AAC7B,SAAK,UAAU,WAAW,KAAK,KAAK,KAAK;AAAA,EAC7C;AAAA,EACA,MAAM;AACF,iBAAa,KAAK,OAAO;AACzB,QAAI,MAAM,KAAK,IAAI;AACnB,QAAI,MAAM,KAAK,WAAW,IAAI;AAC1B,WAAK,UAAU,WAAW,KAAK,KAAK,KAAK,WAAW,GAAG;AAAA,IAC3D,OACK;AACD,WAAK,MAAM;AACX,UAAI,EAAE,MAAM,IAAI,KAAK,MAAM,EAAE,QAAQ,IAAI,MAAM,MAAM,UAAU;AAC/D,UAAI,QAAQ;AACR,qBAAa,QAAQ,IAAI,OAAK,QAAQ,QAAQ,EAAE,KAAK,IAAI,CAAC,CAAC,GAAG,iBAAe;AACzE,cAAI,KAAK,KAAK,MAAM,OAAO,MAAM;AAC7B,iBAAK,KAAK,SAAS,eAAe,KAAK,KAAK,OAAO,YAAY,OAAO,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AAAA,QACrG,GAAG,WAAS;AAAE,uBAAa,KAAK,KAAK,OAAO,KAAK;AAAA,QAAG,CAAC;AAAA,IAC7D;AAAA,EACJ;AAAA,EACA,OAAO,QAAQ;AACX,QAAI,SAAS,OAAO,MAAM,MAAM,UAAU;AAC1C,QAAI,OAAO,cAAc,UAAU,OAAO,WAAW,MAAM,UAAU,KACjE,OAAO,gBAAgB,OAAO,aAAa,MAAM,GAAG;AACpD,WAAK,WAAW,KAAK,IAAI,IAAI,OAAO;AACpC,UAAI,CAAC,KAAK,KAAK;AACX,aAAK,MAAM;AACX,aAAK,UAAU,WAAW,KAAK,KAAK,OAAO,KAAK;AAAA,MACpD;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,QAAQ;AACJ,QAAI,KAAK,KAAK;AACV,WAAK,WAAW,KAAK,IAAI;AACzB,WAAK,IAAI;AAAA,IACb;AAAA,EACJ;AAAA,EACA,UAAU;AACN,iBAAa,KAAK,OAAO;AAAA,EAC7B;AACJ,CAAC;AACD,SAAS,aAAa,UAAU,MAAM,OAAO;AACzC,MAAI,YAAY,CAAC,GAAG,UAAU;AAC9B,WAAS,KAAK;AACV,MAAE,KAAK,WAAS;AACZ,gBAAU,KAAK,KAAK;AACpB,mBAAa,OAAO;AACpB,UAAI,UAAU,UAAU,SAAS;AAC7B,aAAK,SAAS;AAAA;AAEd,kBAAU,WAAW,MAAM,KAAK,SAAS,GAAG,GAAG;AAAA,IACvD,GAAG,KAAK;AAChB;AACA,IAAM,aAA0B,MAAM,OAAO;AAAA,EACzC,QAAQ,OAAO;AACX,WAAO,OAAO,OAAO,EAAE,SAAS,MAAM,IAAI,OAAK,EAAE,MAAM,EAAE,OAAO,OAAK,KAAK,IAAI,EAAE,GAAG,cAAc,MAAM,IAAI,OAAK,EAAE,MAAM,GAAG;AAAA,MACvH,OAAO;AAAA,MACP,cAAc;AAAA,MACd,eAAe;AAAA,MACf,cAAc;AAAA,MACd,QAAQ,MAAM;AAAA,IAClB,GAAG;AAAA,MACC,cAAc,CAAC,GAAG,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,OAAK,EAAE,CAAC,KAAK,EAAE,CAAC;AAAA,IAC9D,CAAC,CAAC;AAAA,EACN;AACJ,CAAC;AAuBD,SAAS,WAAW,SAAS;AACzB,MAAI,WAAW,CAAC;AAChB,MAAI;AACA,YAAS,UAAS,EAAE,KAAK,KAAK,SAAS;AACnC,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,YAAI,KAAK,KAAK,CAAC;AACf,YAAI,WAAW,KAAK,EAAE,KAAK,CAAC,SAAS,KAAK,OAAK,EAAE,YAAY,KAAK,GAAG,YAAY,CAAC,GAAG;AACjF,mBAAS,KAAK,EAAE;AAChB,mBAAS;AAAA,QACb;AAAA,MACJ;AACA,eAAS,KAAK,EAAE;AAAA,IACpB;AACJ,SAAO;AACX;AACA,SAAS,iBAAiB,MAAM,YAAY,SAAS;AACjD,MAAI;AACJ,MAAI,OAAO,UAAU,WAAW,WAAW,OAAO,IAAI,CAAC;AACvD,SAAO,MAAI,MAAM,EAAE,OAAO,iCAAiC,WAAW,SAAS,GAAG,MAAI,QAAQ,EAAE,OAAO,oBAAoB,GAAG,WAAW,gBAAgB,WAAW,cAAc,IAAI,IAAI,WAAW,OAAO,IAAI,KAAK,WAAW,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,CAAC,QAAQ,MAAM;AAChS,QAAI,QAAQ,OAAO,QAAQ,CAAC,MAAM;AAC9B,QAAE,eAAe;AACjB,UAAI;AACA;AACJ,cAAQ;AACR,UAAI,QAAQ,eAAe,KAAK,MAAM,MAAM,SAAS,EAAE,aAAa,UAAU;AAC9E,UAAI;AACA,eAAO,MAAM,MAAM,MAAM,MAAM,MAAM,EAAE;AAAA,IAC/C;AACA,QAAI,EAAE,KAAK,IAAI,QAAQ,WAAW,KAAK,CAAC,IAAI,KAAK,QAAQ,KAAK,CAAC,CAAC,IAAI;AACpE,QAAI,UAAU,WAAW,IAAI,OAAO;AAAA,MAAC,KAAK,MAAM,GAAG,QAAQ;AAAA,MACvD,MAAI,KAAK,KAAK,MAAM,UAAU,WAAW,CAAC,CAAC;AAAA,MAC3C,KAAK,MAAM,WAAW,CAAC;AAAA,IAAC;AAC5B,WAAO,MAAI,UAAU;AAAA,MACjB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,aAAa;AAAA,MACb,cAAc,YAAY,IAAI,GAAG,WAAW,IAAI,KAAK,iBAAiB,KAAK,CAAC,CAAC,IAAI;AAAA,IACrF,GAAG,OAAO;AAAA,EACd,CAAC,GAAG,WAAW,UAAU,MAAI,OAAO,EAAE,OAAO,sBAAsB,GAAG,WAAW,MAAM,CAAC;AAC5F;AACA,IAAM,mBAAN,cAA+B,WAAW;AAAA,EACtC,YAAY,KAAK;AACb,UAAM;AACN,SAAK,MAAM;AAAA,EACf;AAAA,EACA,GAAG,OAAO;AAAE,WAAO,MAAM,OAAO,KAAK;AAAA,EAAK;AAAA,EAC1C,QAAQ;AACJ,WAAO,MAAI,QAAQ,EAAE,OAAO,+BAA+B,KAAK,IAAI,CAAC;AAAA,EACzE;AACJ;AACA,IAAM,YAAN,MAAgB;AAAA,EACZ,YAAY,MAAM,YAAY;AAC1B,SAAK,aAAa;AAClB,SAAK,KAAK,UAAU,KAAK,MAAM,KAAK,OAAO,IAAI,UAAU,EAAE,SAAS,EAAE;AACtE,SAAK,MAAM,iBAAiB,MAAM,YAAY,IAAI;AAClD,SAAK,IAAI,KAAK,KAAK;AACnB,SAAK,IAAI,aAAa,QAAQ,QAAQ;AAAA,EAC1C;AACJ;AACA,IAAM,YAAN,MAAM,WAAU;AAAA,EACZ,YAAY,MAAM;AACd,SAAK,OAAO;AACZ,SAAK,QAAQ,CAAC;AACd,QAAI,YAAY,CAAC,UAAU;AACvB,UAAI,MAAM,WAAW,IAAI;AACrB,uBAAe,KAAK,IAAI;AACxB,aAAK,KAAK,MAAM;AAAA,MACpB,WACS,MAAM,WAAW,MAAM,MAAM,WAAW,IAAI;AACjD,aAAK,eAAe,KAAK,gBAAgB,IAAI,KAAK,MAAM,UAAU,KAAK,MAAM,MAAM;AAAA,MACvF,WACS,MAAM,WAAW,MAAM,MAAM,WAAW,IAAI;AACjD,aAAK,eAAe,KAAK,gBAAgB,KAAK,KAAK,MAAM,MAAM;AAAA,MACnE,WACS,MAAM,WAAW,IAAI;AAC1B,aAAK,cAAc,CAAC;AAAA,MACxB,WACS,MAAM,WAAW,IAAI;AAC1B,aAAK,cAAc,KAAK,MAAM,SAAS,CAAC;AAAA,MAC5C,WACS,MAAM,WAAW,IAAI;AAC1B,aAAK,KAAK,MAAM;AAAA,MACpB,WACS,MAAM,WAAW,MAAM,MAAM,WAAW,MAAM,KAAK,iBAAiB,GAAG;AAC5E,YAAI,EAAE,WAAW,IAAI,KAAK,MAAM,KAAK,aAAa,GAAG,OAAO,WAAW,WAAW,OAAO;AACzF,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ;AAC7B,cAAI,KAAK,CAAC,EAAE,YAAY,EAAE,WAAW,CAAC,KAAK,MAAM,SAAS;AACtD,gBAAI,QAAQ,eAAe,KAAK,KAAK,MAAM,MAAM,SAAS,EAAE,aAAa,UAAU;AACnF,gBAAI;AACA,yBAAW,QAAQ,CAAC,EAAE,MAAM,MAAM,MAAM,MAAM,MAAM,EAAE;AAAA,UAC9D;AAAA,MACR,OACK;AACD;AAAA,MACJ;AACA,YAAM,eAAe;AAAA,IACzB;AACA,QAAI,UAAU,CAAC,UAAU;AACrB,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,YAAI,KAAK,MAAM,CAAC,EAAE,IAAI,SAAS,MAAM,MAAM;AACvC,eAAK,cAAc,CAAC;AAAA,MAC5B;AAAA,IACJ;AACA,SAAK,OAAO,MAAI,MAAM;AAAA,MAClB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,cAAc,KAAK,KAAK,MAAM,OAAO,aAAa;AAAA,MAClD;AAAA,MACA;AAAA,IACJ,CAAC;AACD,SAAK,MAAM,MAAI,OAAO,EAAE,OAAO,gBAAgB,GAAG,KAAK,MAAM,MAAI,UAAU;AAAA,MACvE,MAAM;AAAA,MACN,MAAM;AAAA,MACN,cAAc,KAAK,KAAK,MAAM,OAAO,OAAO;AAAA,MAC5C,SAAS,MAAM,eAAe,KAAK,IAAI;AAAA,IAC3C,GAAG,GAAG,CAAC;AACP,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,gBAAgB;AAChB,QAAI,WAAW,KAAK,KAAK,MAAM,MAAM,SAAS,EAAE;AAChD,QAAI,CAAC;AACD,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ;AACnC,UAAI,KAAK,MAAM,CAAC,EAAE,cAAc,SAAS;AACrC,eAAO;AACf,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,QAAI,EAAE,aAAa,SAAS,IAAI,KAAK,KAAK,MAAM,MAAM,SAAS;AAC/D,QAAI,IAAI,GAAG,YAAY,OAAO,kBAAkB;AAChD,QAAI,OAAO,oBAAI,IAAI;AACnB,gBAAY,QAAQ,GAAG,KAAK,KAAK,MAAM,IAAI,QAAQ,CAAC,QAAQ,MAAM,EAAE,KAAK,MAAM;AAC3E,eAAS,cAAc,KAAK,aAAa;AACrC,YAAI,KAAK,IAAI,UAAU;AACnB;AACJ,aAAK,IAAI,UAAU;AACnB,YAAI,QAAQ,IAAI;AAChB,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ;AACnC,cAAI,KAAK,MAAM,CAAC,EAAE,cAAc,YAAY;AACxC,oBAAQ;AACR;AAAA,UACJ;AACJ,YAAI,QAAQ,GAAG;AACX,iBAAO,IAAI,UAAU,KAAK,MAAM,UAAU;AAC1C,eAAK,MAAM,OAAO,GAAG,GAAG,IAAI;AAC5B,sBAAY;AAAA,QAChB,OACK;AACD,iBAAO,KAAK,MAAM,KAAK;AACvB,cAAI,QAAQ,GAAG;AACX,iBAAK,MAAM,OAAO,GAAG,QAAQ,CAAC;AAC9B,wBAAY;AAAA,UAChB;AAAA,QACJ;AACA,YAAI,YAAY,KAAK,cAAc,SAAS,YAAY;AACpD,cAAI,CAAC,KAAK,IAAI,aAAa,eAAe,GAAG;AACzC,iBAAK,IAAI,aAAa,iBAAiB,MAAM;AAC7C,8BAAkB;AAAA,UACtB;AAAA,QACJ,WACS,KAAK,IAAI,aAAa,eAAe,GAAG;AAC7C,eAAK,IAAI,gBAAgB,eAAe;AAAA,QAC5C;AACA;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,WAAO,IAAI,KAAK,MAAM,UAAU,EAAE,KAAK,MAAM,UAAU,KAAK,KAAK,MAAM,CAAC,EAAE,WAAW,OAAO,IAAI;AAC5F,kBAAY;AACZ,WAAK,MAAM,IAAI;AAAA,IACnB;AACA,QAAI,KAAK,MAAM,UAAU,GAAG;AACxB,WAAK,MAAM,KAAK,IAAI,UAAU,KAAK,MAAM;AAAA,QACrC,MAAM;AAAA,QAAI,IAAI;AAAA,QACd,UAAU;AAAA,QACV,SAAS,KAAK,KAAK,MAAM,OAAO,gBAAgB;AAAA,MACpD,CAAC,CAAC;AACF,kBAAY;AAAA,IAChB;AACA,QAAI,iBAAiB;AACjB,WAAK,KAAK,aAAa,yBAAyB,gBAAgB,EAAE;AAClE,WAAK,KAAK,eAAe;AAAA,QACrB,KAAK;AAAA,QACL,MAAM,OAAO,EAAE,KAAK,gBAAgB,IAAI,sBAAsB,GAAG,OAAO,KAAK,KAAK,sBAAsB,EAAE;AAAA,QAC1G,OAAO,CAAC,EAAE,KAAK,MAAM,MAAM;AACvB,cAAI,SAAS,MAAM,SAAS,KAAK,KAAK;AACtC,cAAI,IAAI,MAAM,MAAM;AAChB,iBAAK,KAAK,cAAc,MAAM,MAAM,IAAI,OAAO;AAAA,mBAC1C,IAAI,SAAS,MAAM;AACxB,iBAAK,KAAK,cAAc,IAAI,SAAS,MAAM,UAAU;AAAA,QAC7D;AAAA,MACJ,CAAC;AAAA,IACL,WACS,KAAK,gBAAgB,GAAG;AAC7B,WAAK,KAAK,gBAAgB,uBAAuB;AAAA,IACrD;AACA,QAAI;AACA,WAAK,KAAK;AAAA,EAClB;AAAA,EACA,OAAO;AACH,QAAI,SAAS,KAAK,KAAK;AACvB,aAAS,KAAK;AACV,UAAI,OAAO;AACX,eAAS,KAAK;AACd,WAAK,OAAO;AAAA,IAChB;AACA,aAAS,QAAQ,KAAK,OAAO;AACzB,UAAI,KAAK,IAAI,cAAc,KAAK,MAAM;AAClC,eAAO,UAAU,KAAK;AAClB,aAAG;AACP,iBAAS,KAAK,IAAI;AAAA,MACtB,OACK;AACD,aAAK,KAAK,aAAa,KAAK,KAAK,MAAM;AAAA,MAC3C;AAAA,IACJ;AACA,WAAO;AACH,SAAG;AAAA,EACX;AAAA,EACA,cAAc,eAAe;AACzB,QAAI,KAAK,gBAAgB;AACrB;AACJ,QAAI,QAAQ,KAAK,KAAK,MAAM,MAAM,SAAS;AAC3C,QAAI,YAAY,eAAe,MAAM,aAAa,KAAK,MAAM,aAAa,EAAE,UAAU;AACtF,QAAI,CAAC;AACD;AACJ,SAAK,KAAK,SAAS;AAAA,MACf,WAAW,EAAE,QAAQ,UAAU,MAAM,MAAM,UAAU,GAAG;AAAA,MACxD,gBAAgB;AAAA,MAChB,SAAS,mBAAmB,GAAG,SAAS;AAAA,IAC5C,CAAC;AAAA,EACL;AAAA,EACA,OAAO,KAAK,MAAM;AAAE,WAAO,IAAI,WAAU,IAAI;AAAA,EAAG;AACpD;AACA,SAAS,IAAI,SAAS,QAAQ,uBAAuB;AACjD,SAAO,mEAAmE,KAAK,IAAI,mBAAmB,OAAO,CAAC;AAClH;AACA,SAAS,UAAU,OAAO;AACtB,SAAO,IAAI,qDAAqD,KAAK,qCAAqC,sBAAsB;AACpI;AACA,IAAM,YAAyB,WAAW,UAAU;AAAA,EAChD,kBAAkB;AAAA,IACd,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,EAChB;AAAA,EACA,wBAAwB,EAAE,YAAY,iBAAiB;AAAA,EACvD,0BAA0B,EAAE,YAAY,mBAAmB;AAAA,EAC3D,uBAAuB,EAAE,YAAY,iBAAiB;AAAA,EACtD,uBAAuB,EAAE,YAAY,iBAAiB;AAAA,EACtD,wBAAwB;AAAA,IACpB,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,OAAO;AAAA,IACP,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,QAAQ;AAAA,EACZ;AAAA,EACA,wBAAwB;AAAA,IACpB,UAAU;AAAA,IACV,SAAS;AAAA,EACb;AAAA,EACA,iBAAiB;AAAA,IACb,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,IAClB,eAAe;AAAA,EACnB;AAAA,EACA,uBAAuB,EAAE,iBAA8B,UAAU,MAAM,EAAE;AAAA,EACzE,yBAAyB,EAAE,iBAA8B,UAAU,QAAQ,EAAE;AAAA,EAC7E,sBAAsB,EAAE,iBAA8B,UAAU,MAAM,EAAE;AAAA,EACxE,sBAAsB,EAAE,iBAA8B,UAAU,MAAM,EAAE;AAAA,EACxE,wBAAwB,EAAE,iBAAiB,YAAY;AAAA,EACvD,oBAAoB;AAAA,IAChB,SAAS;AAAA,IACT,QAAQ;AAAA,EACZ;AAAA,EACA,iBAAiB;AAAA,IACb,UAAU;AAAA,IACV,WAAW;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,cAAc;AAAA,IAClB;AAAA,EACJ;AAAA,EACA,yBAAyB;AAAA,IACrB,WAAW,EAAE,mBAAmB,SAAS;AAAA,EAC7C;AAAA,EACA,sBAAsB;AAAA,IAClB,WAAW,EAAE,mBAAmB,OAAO;AAAA,EAC3C;AAAA,EACA,sBAAsB;AAAA,IAClB,WAAW,EAAE,mBAAmB,OAAO;AAAA,EAC3C;AAAA,EACA,2BAA2B;AAAA,IACvB,UAAU;AAAA,IACV,QAAQ;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,qBAAqB;AAAA,QACjB,iBAAiB;AAAA,QACjB,OAAO,EAAE,gBAAgB,YAAY;AAAA,MACzC;AAAA,MACA,2BAA2B;AAAA,QACvB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACX;AAAA,MACA,OAAO,EAAE,gBAAgB,OAAO;AAAA,MAChC,SAAS;AAAA,MACT,QAAQ;AAAA,IACZ;AAAA,IACA,kBAAkB;AAAA,MACd,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,IACZ;AAAA,EACJ;AACJ,CAAC;AACD,SAAS,eAAe,KAAK;AACzB,SAAO,OAAO,UAAU,IAAI,OAAO,YAAY,IAAI,OAAO,SAAS,IAAI;AAC3E;AACA,SAAS,YAAY,aAAa;AAC9B,MAAI,MAAM,QAAQ,SAAS;AAC3B,WAAS,KAAK,aAAa;AACvB,QAAI,IAAI,eAAe,EAAE,QAAQ;AACjC,QAAI,IAAI,QAAQ;AACZ,eAAS;AACT,YAAM,EAAE;AAAA,IACZ;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,mBAAN,cAA+B,aAAa;AAAA,EACxC,YAAY,aAAa;AACrB,UAAM;AACN,SAAK,cAAc;AACnB,SAAK,WAAW,YAAY,WAAW;AAAA,EAC3C;AAAA,EACA,MAAM,MAAM;AACR,QAAI,MAAM,SAAS,cAAc,KAAK;AACtC,QAAI,YAAY,mCAAmC,KAAK;AACxD,QAAI,cAAc,KAAK;AACvB,QAAI,oBAAoB,KAAK,MAAM,MAAM,gBAAgB,EAAE;AAC3D,QAAI;AACA,oBAAc,kBAAkB,aAAa,KAAK,KAAK;AAC3D,QAAI,YAAY;AACZ,UAAI,cAAc,MAAM,sBAAsB,MAAM,KAAK,WAAW;AACxE,WAAO;AAAA,EACX;AACJ;AACA,SAAS,aAAa,MAAM,QAAQ;AAChC,MAAI,YAAY,CAAC,UAAU;AACvB,QAAI,OAAO,OAAO,sBAAsB;AACxC,QAAI,MAAM,UAAU,KAAK,OAAO,MAAyB,MAAM,UAAU,KAAK,QAAQ,MAClF,MAAM,UAAU,KAAK,MAAM,MAAyB,MAAM,UAAU,KAAK,SAAS;AAClF;AACJ,aAAS,SAAS,MAAM,QAAQ,QAAQ,SAAS,OAAO,YAAY;AAChE,UAAI,OAAO,YAAY,KAAK,OAAO,UAAU,SAAS,iBAAiB;AACnE;AAAA,IACR;AACA,WAAO,oBAAoB,aAAa,SAAS;AACjD,QAAI,KAAK,MAAM,MAAM,iBAAiB;AAClC,WAAK,SAAS,EAAE,SAAS,qBAAqB,GAAG,IAAI,EAAE,CAAC;AAAA,EAChE;AACA,SAAO,iBAAiB,aAAa,SAAS;AAClD;AACA,SAAS,sBAAsB,MAAM,QAAQ,aAAa;AACtD,WAAS,UAAU;AACf,QAAI,OAAO,KAAK,gBAAgB,OAAO,sBAAsB,EAAE,MAAM,IAAI,KAAK,WAAW;AACzF,UAAM,UAAU,KAAK,YAAY,KAAK,IAAI;AAC1C,QAAI,SAAS;AACT,WAAK,SAAS,EAAE,SAAS,qBAAqB,GAAG;AAAA,QACzC,KAAK,KAAK;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,SAAS;AACL,iBAAO;AAAA,YACH,KAAK,mBAAmB,MAAM,WAAW;AAAA,YACzC,WAAW,MAAM,OAAO,sBAAsB;AAAA,UAClD;AAAA,QACJ;AAAA,MACJ,CAAC,EAAE,CAAC;AAAA,IACZ;AACA,WAAO,aAAa,OAAO,cAAc;AACzC,iBAAa,MAAM,MAAM;AAAA,EAC7B;AACA,MAAI,EAAE,UAAU,IAAI,KAAK,MAAM,MAAM,gBAAgB;AACrD,MAAI,eAAe,WAAW,SAAS,SAAS;AAChD,SAAO,aAAa,MAAM;AACtB,iBAAa,YAAY;AACzB,WAAO,aAAa,OAAO,cAAc;AAAA,EAC7C;AACA,SAAO,cAAc,MAAM;AACvB,iBAAa,YAAY;AACzB,mBAAe,WAAW,SAAS,SAAS;AAAA,EAChD;AACJ;AACA,SAAS,sBAAsB,KAAK,aAAa;AAC7C,MAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,WAAS,cAAc,aAAa;AAChC,QAAI,OAAO,IAAI,OAAO,WAAW,IAAI;AACrC,KAAC,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU;AAAA,EACnE;AACA,MAAI,UAAU,CAAC;AACf,WAAS,QAAQ,QAAQ;AACrB,YAAQ,KAAK,IAAI,iBAAiB,OAAO,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC;AAAA,EAChE;AACA,SAAO,SAAS,GAAG,SAAS,IAAI;AACpC;AACA,IAAM,sBAAmC,OAAO;AAAA,EAC5C,OAAO;AAAA,EACP,SAAS,UAAQ,KAAK,MAAM,MAAM,iBAAiB;AAAA,EACnD,cAAc,CAAC,MAAM,QAAQ,UAAU;AACnC,QAAI,cAAc,CAAC;AACnB,SAAK,MAAM,MAAM,iBAAiB,EAAE,QAAQ,MAAM,MAAM,MAAM,IAAI,CAAC,MAAM,IAAI,UAAU;AACnF,UAAI,OAAO,MAAM,QAAQ,OAAO,MAAM;AAClC,oBAAY,KAAK,GAAG,MAAM,WAAW;AAAA,IAC7C,CAAC;AACD,WAAO,YAAY,SAAS,IAAI,iBAAiB,WAAW,IAAI;AAAA,EACpE;AACJ,CAAC;AACD,IAAM,oBAAiC,WAAW,OAAO;AAAA,EACrD,SAAS;AACL,WAAO,SAAS;AAAA,EACpB;AAAA,EACA,OAAO,SAAS,IAAI;AAChB,cAAU,QAAQ,IAAI,GAAG,OAAO;AAChC,QAAI,mBAAmB,GAAG,MAAM,MAAM,gBAAgB,EAAE;AACxD,aAAS,UAAU,GAAG,SAAS;AAC3B,UAAI,OAAO,GAAG,oBAAoB,GAAG;AACjC,YAAI,cAAc,OAAO;AACzB,YAAI;AACA,wBAAc,iBAAiB,eAAe,CAAC,GAAG,GAAG,KAAK;AAC9D,kBAAU,sBAAsB,GAAG,MAAM,KAAK,YAAY,MAAM,CAAC,CAAC;AAAA,MACtE;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ,CAAC;AACD,IAAM,uBAAoC,YAAY,OAAO;AAC7D,IAAM,oBAAiC,WAAW,OAAO;AAAA,EACrD,SAAS;AAAE,WAAO;AAAA,EAAM;AAAA,EACxB,OAAO,SAAS,IAAI;AAChB,QAAI,WAAW,GAAG;AACd,gBAAU,YAAY,IAAI,OAAO,IAAI,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,KAAK,GAAG,QAAQ,OAAO,QAAQ,GAAG,EAAE,CAAC;AACjI,WAAO,GAAG,QAAQ,OAAO,CAAC,GAAG,MAAM,EAAE,GAAG,oBAAoB,IAAI,EAAE,QAAQ,GAAG,OAAO;AAAA,EACxF;AAAA,EACA,SAAS,WAAS,YAAY,KAAK,KAAK;AAC5C,CAAC;AACD,IAAM,kBAA+B,WAAW,UAAU;AAAA,EACtD,mBAAmB;AAAA,IACf,OAAO;AAAA,IACP,uBAAuB;AAAA,MACnB,SAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,mBAAmB;AAAA,IACf,OAAO;AAAA,IACP,QAAQ;AAAA,EACZ;AAAA,EACA,wBAAwB;AAAA,IACpB,SAAsB,IAAI,sGAAsG;AAAA,EACpI;AAAA,EACA,2BAA2B;AAAA,IACvB,SAAsB,IAAI,kGAAkG;AAAA,EAChI;AAAA,EACA,yBAAyB;AAAA,IACrB,SAAsB,IAAI,6EAA6E;AAAA,EAC3G;AACJ,CAAC;AACD,IAAM,iBAAiB;AAAA,EACnB;AAAA,EACa,WAAW,YAAY,QAAQ,CAAC,SAAS,GAAG,WAAS;AAC9D,QAAI,EAAE,UAAU,MAAM,IAAI,MAAM,MAAM,SAAS;AAC/C,WAAO,CAAC,YAAY,CAAC,SAAS,SAAS,QAAQ,SAAS,KAAK,WAAW,OAAO,WAAW,IAAI;AAAA,MAC1F,WAAW,MAAM,SAAS,MAAM,SAAS,EAAE;AAAA,IAC/C,CAAC;AAAA,EACL,CAAC;AAAA,EACY,aAAa,aAAa,EAAE,QAAQ,YAAY,CAAC;AAAA,EAC9D;AACJ;AACA,IAAM,mBAAgC,MAAM,OAAO;AAAA,EAC/C,QAAQ,SAAS;AACb,WAAO,cAAc,SAAS;AAAA,MAC1B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,eAAe;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;;;ACtyBD,IAAM,cAA2B,MAAM;AAAA,EACnC,YAAY;AAAA,EACZ,0BAA0B;AAAA,EAC1B,sBAAsB;AAAA,EACtB,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY,wBAAwB,GAAG,IAAI;AAAA,EAC3C,cAAc;AAAA,EACd,mBAAmB,uBAAuB,EAAE,UAAU,KAAK,CAAC;AAAA,EAC5D,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,eAAe;AAAA,EACf,qBAAqB;AAAA,EACrB,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,0BAA0B;AAAA,EAC1B,OAAO,GAAG;AAAA,IACN,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACP,CAAC;AACL,GAAG;AASH,IAAM,gBAA6B,MAAM;AAAA,EACrC,sBAAsB;AAAA,EACtB,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,mBAAmB,uBAAuB,EAAE,UAAU,KAAK,CAAC;AAAA,EAC5D,OAAO,GAAG;AAAA,IACN,GAAG;AAAA,IACH,GAAG;AAAA,EACP,CAAC;AACL,GAAG;", "names": ["next", "i"]}