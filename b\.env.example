# 环境配置示例文件
# 复制此文件为 .env 并填入实际配置

# 应用环境
NODE_ENV=development

# API配置
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/ws

# 市场数据源
VITE_MARKET_DATA_PROVIDER=mock
VITE_ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
VITE_FINNHUB_API_KEY=your_finnhub_key
VITE_POLYGON_API_KEY=your_polygon_key

# AI功能配置
VITE_AI_MODEL_URL=https://huggingface.co/models
VITE_ENABLE_AI_FEATURES=true

# 性能监控
VITE_ENABLE_ANALYTICS=false
VITE_SENTRY_DSN=your_sentry_dsn

# 功能开关
VITE_ENABLE_REAL_TRADING=false
VITE_ENABLE_PAPER_TRADING=true
VITE_ENABLE_BACKTEST=true
VITE_ENABLE_STRATEGY_SHARING=true

# 安全配置
VITE_ENABLE_CSP=true
VITE_ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# 缓存配置
VITE_CACHE_DURATION=300000
VITE_ENABLE_SERVICE_WORKER=true

# 调试配置
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info
