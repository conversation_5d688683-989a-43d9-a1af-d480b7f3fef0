import { createSignal, For } from 'solid-js'
import { css } from '../../styled-system/css'

export default function BacktestAnalysis() {
  const [selectedBacktest, setSelectedBacktest] = createSignal('bt_001')

  // 回测记录数据
  const backtests = [
    {
      id: 'bt_001',
      name: '动量策略回测',
      strategy: '动量策略',
      symbol: '000001',
      period: '2023-01-01 至 2024-01-01',
      status: 'completed',
      totalReturn: 15.6,
      sharpeRatio: 1.85,
      maxDrawdown: -3.2,
      winRate: 68.5,
      trades: 156,
      createdAt: '2024-01-15'
    },
    {
      id: 'bt_002',
      name: '均值回归策略回测',
      strategy: '均值回归策略',
      symbol: '000002',
      period: '2023-06-01 至 2024-06-01',
      status: 'completed',
      totalReturn: 8.3,
      sharpeRatio: 1.42,
      maxDrawdown: -2.1,
      winRate: 72.3,
      trades: 89,
      createdAt: '2024-01-10'
    },
    {
      id: 'bt_003',
      name: '网格交易策略回测',
      strategy: '网格交易策略',
      symbol: '000858',
      period: '2023-03-01 至 2024-03-01',
      status: 'running',
      totalReturn: 5.7,
      sharpeRatio: 1.23,
      maxDrawdown: -1.8,
      winRate: 65.4,
      trades: 234,
      createdAt: '2024-01-08'
    }
  ]

  const currentBacktest = () => backtests.find(b => b.id === selectedBacktest())

  // 性能指标数据
  const performanceMetrics = [
    { label: '总收益率', value: `+${currentBacktest()?.totalReturn}%`, trend: 'up' },
    { label: '年化收益率', value: '+18.7%', trend: 'up' },
    { label: '最大回撤', value: `${currentBacktest()?.maxDrawdown}%`, trend: 'down' },
    { label: '夏普比率', value: currentBacktest()?.sharpeRatio.toString(), trend: 'up' },
    { label: '胜率', value: `${currentBacktest()?.winRate}%`, trend: 'up' },
    { label: '交易次数', value: currentBacktest()?.trades.toString(), trend: 'neutral' }
  ]

  return (
    <div class={css({
      display: 'flex',
      flexDirection: 'column',
      gap: '24px'
    })}>
      {/* 回测记录列表 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
        gap: '16px'
      })}>
        <For each={backtests}>
          {(backtest) => (
            <div
              class={css({
                bg: 'white',
                borderRadius: '12px',
                p: '20px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                border: selectedBacktest() === backtest.id ? '2px solid #1890ff' : '1px solid #f0f0f0',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                _hover: {
                  boxShadow: '0 4px 16px rgba(0,0,0,0.15)',
                  transform: 'translateY(-2px)'
                }
              })}
              onClick={() => setSelectedBacktest(backtest.id)}
            >
              <div class={css({
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                mb: '12px'
              })}>
                <h3 class={css({
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#262626',
                  margin: 0
                })}>
                  {backtest.name}
                </h3>
                <div class={css({
                  px: '8px',
                  py: '4px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontWeight: '500',
                  bg: backtest.status === 'completed' ? '#f6ffed' :
                      backtest.status === 'running' ? '#e6f7ff' : '#fff2f0',
                  color: backtest.status === 'completed' ? '#52c41a' :
                         backtest.status === 'running' ? '#1890ff' : '#ff4d4f'
                })}>
                  {backtest.status === 'completed' ? '已完成' :
                   backtest.status === 'running' ? '运行中' : '失败'}
                </div>
              </div>

              <div class={css({
                fontSize: '14px',
                color: '#8c8c8c',
                mb: '12px'
              })}>
                {backtest.strategy} · {backtest.symbol} · {backtest.period}
              </div>

              <div class={css({
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '12px',
                mb: '12px'
              })}>
                <div>
                  <div class={css({
                    fontSize: '12px',
                    color: '#8c8c8c',
                    mb: '4px'
                  })}>
                    总收益率
                  </div>
                  <div class={css({
                    fontSize: '16px',
                    fontWeight: '700',
                    color: backtest.totalReturn >= 0 ? '#52c41a' : '#ff4d4f'
                  })}>
                    {backtest.totalReturn >= 0 ? '+' : ''}{backtest.totalReturn}%
                  </div>
                </div>
                <div>
                  <div class={css({
                    fontSize: '12px',
                    color: '#8c8c8c',
                    mb: '4px'
                  })}>
                    夏普比率
                  </div>
                  <div class={css({
                    fontSize: '16px',
                    fontWeight: '700',
                    color: '#262626'
                  })}>
                    {backtest.sharpeRatio}
                  </div>
                </div>
              </div>

              <div class={css({
                fontSize: '12px',
                color: '#8c8c8c'
              })}>
                创建时间: {backtest.createdAt}
              </div>
            </div>
          )}
        </For>
      </div>

      {/* 性能指标详情 */}
      <div class={css({
        bg: 'white',
        borderRadius: '16px',
        p: '24px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        border: '1px solid #f0f0f0'
      })}>
        <h3 class={css({
          fontSize: '18px',
          fontWeight: '600',
          color: '#262626',
          margin: 0,
          mb: '20px'
        })}>
          {currentBacktest()?.name} - 详细分析
        </h3>

        <div class={css({
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
          gap: '16px'
        })}>
          <For each={performanceMetrics}>
            {(metric) => (
              <div class={css({
                textAlign: 'center',
                p: '16px',
                borderRadius: '8px',
                bg: '#fafafa',
                border: '1px solid #f0f0f0'
              })}>
                <div class={css({
                  fontSize: '12px',
                  color: '#8c8c8c',
                  mb: '8px'
                })}>
                  {metric.label}
                </div>
                <div class={css({
                  fontSize: '20px',
                  fontWeight: '700',
                  color: metric.trend === 'up' ? '#52c41a' :
                         metric.trend === 'down' ? '#ff4d4f' : '#262626'
                })}>
                  {metric.value}
                </div>
              </div>
            )}
          </For>
        </div>
      </div>

      {/* 图表占位符 */}
      <div class={css({
        bg: 'white',
        borderRadius: '16px',
        p: '24px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        border: '1px solid #f0f0f0',
        textAlign: 'center',
        minHeight: '300px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      })}>
        <div>
          <div class={css({
            fontSize: '64px',
            mb: '16px'
          })}>
            📈
          </div>
          <h3 class={css({
            fontSize: '18px',
            fontWeight: '600',
            color: '#262626',
            mb: '8px'
          })}>
            收益曲线图表
          </h3>
          <p class={css({
            fontSize: '14px',
            color: '#8c8c8c'
          })}>
            图表组件将在后续版本中实现
          </p>
        </div>
      </div>
    </div>
  )
}


