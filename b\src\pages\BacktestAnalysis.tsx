import { useAtomValue } from 'jotai';
import { createSignal, For, Show } from 'solid-js';
import { backtestResultsAtom, currentBacktestResultAtom, strategiesAtom } from '@/stores/strategy';
import { css } from '../../styled-system/css';
import { FiPlay, FiBarChart3, FiTrendingUp, FiTrendingDown, FiActivity, FiCalendar } from 'solid-icons/fi';

export default function BacktestAnalysis() {
  const backtestResults = useAtomValue(backtestResultsAtom);
  const currentResult = useAtomValue(currentBacktestResultAtom);
  const strategies = useAtomValue(strategiesAtom);
  
  const [selectedResult, setSelectedResult] = createSignal(backtestResults[0] || null);
  const [isRunning, setIsRunning] = createSignal(false);
  
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };
  
  const formatPercent = (value: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value / 100);
  };
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };
  
  const runBacktest = async () => {
    setIsRunning(true);
    
    // 模拟回测运行
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 生成模拟回测结果
    const mockResult = {
      strategyId: 'mock-strategy',
      startDate: '2023-01-01',
      endDate: '2024-01-01',
      initialCapital: 100000,
      finalValue: 125000,
      totalReturn: 25,
      annualizedReturn: 25,
      maxDrawdown: -8.5,
      sharpeRatio: 1.45,
      winRate: 68.5,
      profitFactor: 1.8,
      totalTrades: 156,
      trades: [],
      equity: []
    };
    
    setIsRunning(false);
    console.log('回测完成:', mockResult);
  };
  
  return (
    <div class={css({
      w: 'full',
      maxW: '7xl',
      mx: 'auto',
      px: 4,
      py: 6
    })}>
      {/* 页面标题 */}
      <div class={css({
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        mb: 8
      })}>
        <div>
          <h1 class={css({
            fontSize: '3xl',
            fontWeight: 'bold',
            color: 'gray.900',
            mb: 2,
            _dark: { color: 'gray.100' }
          })}>
            回测分析
          </h1>
          <p class={css({
            fontSize: 'lg',
            color: 'gray.600',
            _dark: { color: 'gray.400' }
          })}>
            验证策略历史表现，优化交易参数
          </p>
        </div>
        
        <button
          onClick={runBacktest}
          disabled={isRunning()}
          class={css({
            px: 6,
            py: 3,
            bg: 'primary.500',
            color: 'white',
            rounded: 'lg',
            fontSize: 'sm',
            fontWeight: 'medium',
            transition: 'all 0.2s ease',
            _hover: {
              bg: 'primary.600'
            },
            _disabled: {
              opacity: 0.5,
              cursor: 'not-allowed'
            }
          })}
        >
          <FiPlay size={16} class={css({ mr: 2 })} />
          {isRunning() ? '运行中...' : '运行回测'}
        </button>
      </div>
      
      {/* 回测配置 */}
      <div class={css({
        p: 6,
        bg: 'white',
        rounded: 'xl',
        border: '1px solid',
        borderColor: 'gray.200',
        mb: 6,
        _dark: {
          bg: 'gray.800',
          borderColor: 'gray.700'
        }
      })}>
        <h2 class={css({
          fontSize: 'lg',
          fontWeight: 'semibold',
          color: 'gray.900',
          mb: 4,
          _dark: { color: 'gray.100' }
        })}>
          回测配置
        </h2>
        
        <div class={css({
          display: 'grid',
          gridTemplateColumns: { base: '1fr', md: 'repeat(2, 1fr)', lg: 'repeat(4, 1fr)' },
          gap: 4
        })}>
          <div>
            <label class={css({
              display: 'block',
              fontSize: 'sm',
              fontWeight: 'medium',
              color: 'gray.700',
              mb: 2,
              _dark: { color: 'gray.300' }
            })}>
              选择策略
            </label>
            <select class={css({
              w: 'full',
              px: 3,
              py: 2,
              border: '1px solid',
              borderColor: 'gray.300',
              rounded: 'md',
              fontSize: 'sm',
              _focus: {
                outline: 'none',
                borderColor: 'primary.500',
                ring: '2px',
                ringColor: 'primary.200'
              },
              _dark: {
                bg: 'gray.700',
                borderColor: 'gray.600',
                color: 'gray.100'
              }
            })}>
              <option value="">选择策略</option>
              <For each={strategies}>
                {(strategy) => (
                  <option value={strategy.id}>{strategy.name}</option>
                )}
              </For>
            </select>
          </div>
          
          <div>
            <label class={css({
              display: 'block',
              fontSize: 'sm',
              fontWeight: 'medium',
              color: 'gray.700',
              mb: 2,
              _dark: { color: 'gray.300' }
            })}>
              开始日期
            </label>
            <input
              type="date"
              value="2023-01-01"
              class={css({
                w: 'full',
                px: 3,
                py: 2,
                border: '1px solid',
                borderColor: 'gray.300',
                rounded: 'md',
                fontSize: 'sm',
                _focus: {
                  outline: 'none',
                  borderColor: 'primary.500',
                  ring: '2px',
                  ringColor: 'primary.200'
                },
                _dark: {
                  bg: 'gray.700',
                  borderColor: 'gray.600',
                  color: 'gray.100'
                }
              })}
            />
          </div>
          
          <div>
            <label class={css({
              display: 'block',
              fontSize: 'sm',
              fontWeight: 'medium',
              color: 'gray.700',
              mb: 2,
              _dark: { color: 'gray.300' }
            })}>
              结束日期
            </label>
            <input
              type="date"
              value="2024-01-01"
              class={css({
                w: 'full',
                px: 3,
                py: 2,
                border: '1px solid',
                borderColor: 'gray.300',
                rounded: 'md',
                fontSize: 'sm',
                _focus: {
                  outline: 'none',
                  borderColor: 'primary.500',
                  ring: '2px',
                  ringColor: 'primary.200'
                },
                _dark: {
                  bg: 'gray.700',
                  borderColor: 'gray.600',
                  color: 'gray.100'
                }
              })}
            />
          </div>
          
          <div>
            <label class={css({
              display: 'block',
              fontSize: 'sm',
              fontWeight: 'medium',
              color: 'gray.700',
              mb: 2,
              _dark: { color: 'gray.300' }
            })}>
              初始资金
            </label>
            <input
              type="number"
              value="100000"
              class={css({
                w: 'full',
                px: 3,
                py: 2,
                border: '1px solid',
                borderColor: 'gray.300',
                rounded: 'md',
                fontSize: 'sm',
                _focus: {
                  outline: 'none',
                  borderColor: 'primary.500',
                  ring: '2px',
                  ringColor: 'primary.200'
                },
                _dark: {
                  bg: 'gray.700',
                  borderColor: 'gray.600',
                  color: 'gray.100'
                }
              })}
            />
          </div>
        </div>
      </div>
      
      {/* 回测结果 */}
      <Show 
        when={backtestResults.length > 0}
        fallback={
          <div class={css({
            p: 12,
            bg: 'white',
            rounded: 'xl',
            border: '1px solid',
            borderColor: 'gray.200',
            textAlign: 'center',
            _dark: {
              bg: 'gray.800',
              borderColor: 'gray.700'
            }
          })}>
            <FiBarChart3 size={64} class={css({ mx: 'auto', mb: 4, color: 'gray.400' })} />
            <h3 class={css({
              fontSize: 'lg',
              fontWeight: 'semibold',
              color: 'gray.900',
              mb: 2,
              _dark: { color: 'gray.100' }
            })}>
              暂无回测结果
            </h3>
            <p class={css({
              color: 'gray.600',
              _dark: { color: 'gray.400' }
            })}>
              选择策略并点击"运行回测"开始分析
            </p>
          </div>
        }
      >
        <div class={css({
          display: 'grid',
          gridTemplateColumns: { base: '1fr', xl: '1fr 300px' },
          gap: 6
        })}>
          {/* 主要结果区域 */}
          <div class={css({
            space: 'y-6'
          })}>
            {/* 关键指标 */}
            <div class={css({
              p: 6,
              bg: 'white',
              rounded: 'xl',
              border: '1px solid',
              borderColor: 'gray.200',
              _dark: {
                bg: 'gray.800',
                borderColor: 'gray.700'
              }
            })}>
              <h3 class={css({
                fontSize: 'lg',
                fontWeight: 'semibold',
                color: 'gray.900',
                mb: 4,
                _dark: { color: 'gray.100' }
              })}>
                关键指标
              </h3>
              
              <div class={css({
                display: 'grid',
                gridTemplateColumns: { base: 'repeat(2, 1fr)', md: 'repeat(4, 1fr)' },
                gap: 6
              })}>
                <div class={css({ textAlign: 'center' })}>
                  <div class={css({
                    fontSize: '2xl',
                    fontWeight: 'bold',
                    color: 'success.600',
                    mb: 1
                  })}>
                    {formatPercent(25)}
                  </div>
                  <div class={css({
                    fontSize: 'sm',
                    color: 'gray.600',
                    _dark: { color: 'gray.400' }
                  })}>
                    总收益率
                  </div>
                </div>
                
                <div class={css({ textAlign: 'center' })}>
                  <div class={css({
                    fontSize: '2xl',
                    fontWeight: 'bold',
                    color: 'danger.600',
                    mb: 1
                  })}>
                    -8.5%
                  </div>
                  <div class={css({
                    fontSize: 'sm',
                    color: 'gray.600',
                    _dark: { color: 'gray.400' }
                  })}>
                    最大回撤
                  </div>
                </div>
                
                <div class={css({ textAlign: 'center' })}>
                  <div class={css({
                    fontSize: '2xl',
                    fontWeight: 'bold',
                    color: 'primary.600',
                    mb: 1
                  })}>
                    1.45
                  </div>
                  <div class={css({
                    fontSize: 'sm',
                    color: 'gray.600',
                    _dark: { color: 'gray.400' }
                  })}>
                    夏普比率
                  </div>
                </div>
                
                <div class={css({ textAlign: 'center' })}>
                  <div class={css({
                    fontSize: '2xl',
                    fontWeight: 'bold',
                    color: 'warning.600',
                    mb: 1
                  })}>
                    68.5%
                  </div>
                  <div class={css({
                    fontSize: 'sm',
                    color: 'gray.600',
                    _dark: { color: 'gray.400' }
                  })}>
                    胜率
                  </div>
                </div>
              </div>
            </div>
            
            {/* 收益曲线图表占位 */}
            <div class={css({
              p: 6,
              bg: 'white',
              rounded: 'xl',
              border: '1px solid',
              borderColor: 'gray.200',
              h: '400px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              _dark: {
                bg: 'gray.800',
                borderColor: 'gray.700'
              }
            })}>
              <div class={css({
                textAlign: 'center',
                color: 'gray.500',
                _dark: { color: 'gray.400' }
              })}>
                <FiTrendingUp size={48} class={css({ mx: 'auto', mb: 3 })} />
                <p>收益曲线图表</p>
                <p class={css({ fontSize: 'sm', mt: 1 })}>
                  (图表组件将在后续版本中实现)
                </p>
              </div>
            </div>
          </div>
          
          {/* 侧边栏 */}
          <div class={css({
            space: 'y-6'
          })}>
            {/* 历史回测 */}
            <div class={css({
              p: 4,
              bg: 'white',
              rounded: 'xl',
              border: '1px solid',
              borderColor: 'gray.200',
              _dark: {
                bg: 'gray.800',
                borderColor: 'gray.700'
              }
            })}>
              <h3 class={css({
                fontSize: 'md',
                fontWeight: 'semibold',
                color: 'gray.900',
                mb: 3,
                _dark: { color: 'gray.100' }
              })}>
                历史回测
              </h3>
              
              <div class={css({
                space: 'y-2'
              })}>
                <For each={[1, 2, 3]}>
                  {(index) => (
                    <div class={css({
                      p: 3,
                      bg: 'gray.50',
                      rounded: 'lg',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      _hover: {
                        bg: 'gray.100'
                      },
                      _dark: {
                        bg: 'gray.700',
                        _hover: {
                          bg: 'gray.600'
                        }
                      }
                    })}>
                      <div class={css({
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        mb: 1
                      })}>
                        <span class={css({
                          fontSize: 'sm',
                          fontWeight: 'medium',
                          color: 'gray.900',
                          _dark: { color: 'gray.100' }
                        })}>
                          回测 #{index}
                        </span>
                        <span class={css({
                          fontSize: 'xs',
                          color: index === 1 ? 'success.600' : 'danger.600',
                          fontWeight: 'medium'
                        })}>
                          {index === 1 ? '+25%' : index === 2 ? '-5%' : '+12%'}
                        </span>
                      </div>
                      <div class={css({
                        fontSize: 'xs',
                        color: 'gray.600',
                        _dark: { color: 'gray.400' }
                      })}>
                        2024-01-{index.toString().padStart(2, '0')}
                      </div>
                    </div>
                  )}
                </For>
              </div>
            </div>
            
            {/* 风险指标 */}
            <div class={css({
              p: 4,
              bg: 'white',
              rounded: 'xl',
              border: '1px solid',
              borderColor: 'gray.200',
              _dark: {
                bg: 'gray.800',
                borderColor: 'gray.700'
              }
            })}>
              <h3 class={css({
                fontSize: 'md',
                fontWeight: 'semibold',
                color: 'gray.900',
                mb: 3,
                _dark: { color: 'gray.100' }
              })}>
                风险指标
              </h3>
              
              <div class={css({
                space: 'y-3'
              })}>
                <div class={css({
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                })}>
                  <span class={css({
                    fontSize: 'sm',
                    color: 'gray.600',
                    _dark: { color: 'gray.400' }
                  })}>
                    波动率
                  </span>
                  <span class={css({
                    fontSize: 'sm',
                    fontWeight: 'medium',
                    color: 'gray.900',
                    _dark: { color: 'gray.100' }
                  })}>
                    15.2%
                  </span>
                </div>
                
                <div class={css({
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                })}>
                  <span class={css({
                    fontSize: 'sm',
                    color: 'gray.600',
                    _dark: { color: 'gray.400' }
                  })}>
                    VaR (95%)
                  </span>
                  <span class={css({
                    fontSize: 'sm',
                    fontWeight: 'medium',
                    color: 'gray.900',
                    _dark: { color: 'gray.100' }
                  })}>
                    -2.8%
                  </span>
                </div>
                
                <div class={css({
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                })}>
                  <span class={css({
                    fontSize: 'sm',
                    color: 'gray.600',
                    _dark: { color: 'gray.400' }
                  })}>
                    贝塔系数
                  </span>
                  <span class={css({
                    fontSize: 'sm',
                    fontWeight: 'medium',
                    color: 'gray.900',
                    _dark: { color: 'gray.100' }
                  })}>
                    0.85
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Show>
    </div>
  );
}
