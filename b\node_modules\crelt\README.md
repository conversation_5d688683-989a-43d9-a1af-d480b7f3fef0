# CRELT

Tiny DOM-element creation utility. Exports a single (default) value,
which is a function that you call with:

 - A tag name string or DOM element.

 - Optionally an attribute object mapping names to values. When the
   values are strings, they are added to the element with
   `setAttribute`. When they have another type, they are assigned as
   regular properties (mostly useful for event handlers via `onclick`
   and such). When an attribute's value is null or undefined, it is
   not assigned.

 - Any number of children, which may be null (ignored), strings (added
   as text nodes), DOM nodes (added), or arrays (each element is added
   separately).

The function returns a DOM element.

## License

This software is licensed under an MIT license.
