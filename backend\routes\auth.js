/**
 * 用户认证路由
 */
import express from 'express';
import { v4 as uuidv4 } from 'uuid';

const router = express.Router();

// 模拟用户数据库
const users = [
  {
    id: 1,
    username: 'admin',
    password: 'admin',
    nickname: '管理员',
    email: '<EMAIL>',
    permissions: ['*'],
    avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
    createdAt: '2024-01-01T00:00:00Z',
    lastLogin: new Date().toISOString()
  },
  {
    id: 2,
    username: 'trader',
    password: 'trader123',
    nickname: '交易员',
    email: '<EMAIL>',
    permissions: ['trading:*', 'market:read', 'portfolio:read'],
    avatar: 'https://avatars.githubusercontent.com/u/2?v=4',
    createdAt: '2024-01-01T00:00:00Z',
    lastLogin: new Date().toISOString()
  },
  {
    id: 3,
    username: 'analyst',
    password: 'analyst123',
    nickname: '分析师',
    email: '<EMAIL>',
    permissions: ['strategy:*', 'backtest:*', 'market:read'],
    avatar: 'https://avatars.githubusercontent.com/u/3?v=4',
    createdAt: '2024-01-01T00:00:00Z',
    lastLogin: new Date().toISOString()
  }
];

// 模拟会话存储
const sessions = new Map();

/**
 * 用户登录
 */
router.post('/login', async (req, res) => {
  try {
    const { username, password, rememberMe = false } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空',
        code: 'INVALID_PARAMS'
      });
    }
    
    // 查找用户
    const user = users.find(u => u.username === username && u.password === password);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误',
        code: 'INVALID_CREDENTIALS'
      });
    }
    
    // 生成token
    const token = `token_${uuidv4()}`;
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + (rememberMe ? 24 * 7 : 24));
    
    // 存储会话
    sessions.set(token, {
      userId: user.id,
      username: user.username,
      expiresAt: expiresAt.toISOString(),
      createdAt: new Date().toISOString()
    });
    
    // 更新用户最后登录时间
    user.lastLogin = new Date().toISOString();
    
    // 返回用户信息（不包含密码）
    const { password: _, ...userInfo } = user;
    
    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: userInfo,
        token,
        expiresAt: expiresAt.toISOString(),
        permissions: user.permissions
      }
    });
    
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '登录服务出现错误',
      code: 'SERVER_ERROR'
    });
  }
});

/**
 * 用户注册
 */
router.post('/register', async (req, res) => {
  try {
    const { username, password, nickname, email } = req.body;
    
    if (!username || !password || !email) {
      return res.status(400).json({
        success: false,
        message: '用户名、密码和邮箱不能为空',
        code: 'INVALID_PARAMS'
      });
    }
    
    // 检查用户是否已存在
    if (users.find(u => u.username === username)) {
      return res.status(409).json({
        success: false,
        message: '用户名已存在',
        code: 'USER_EXISTS'
      });
    }
    
    if (users.find(u => u.email === email)) {
      return res.status(409).json({
        success: false,
        message: '邮箱已被使用',
        code: 'EMAIL_EXISTS'
      });
    }
    
    // 创建新用户
    const newUser = {
      id: users.length + 1,
      username,
      password,
      nickname: nickname || username,
      email,
      permissions: ['market:read', 'portfolio:read'],
      avatar: `https://avatars.githubusercontent.com/u/${users.length + 1}?v=4`,
      createdAt: new Date().toISOString(),
      lastLogin: new Date().toISOString()
    };
    
    users.push(newUser);
    
    // 返回用户信息（不包含密码）
    const { password: _, ...userInfo } = newUser;
    
    res.status(201).json({
      success: true,
      message: '注册成功',
      data: {
        user: userInfo
      }
    });
    
  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({
      success: false,
      message: '注册服务出现错误',
      code: 'SERVER_ERROR'
    });
  }
});

/**
 * 验证token中间件
 */
export const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer token
  
  if (!token) {
    return res.status(401).json({
      success: false,
      message: '访问令牌不能为空',
      code: 'TOKEN_MISSING'
    });
  }
  
  const session = sessions.get(token);
  
  if (!session) {
    return res.status(401).json({
      success: false,
      message: '无效的访问令牌',
      code: 'INVALID_TOKEN'
    });
  }
  
  // 检查token是否过期
  if (new Date() > new Date(session.expiresAt)) {
    sessions.delete(token);
    return res.status(401).json({
      success: false,
      message: '访问令牌已过期',
      code: 'TOKEN_EXPIRED'
    });
  }
  
  // 获取用户信息
  const user = users.find(u => u.id === session.userId);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: '用户不存在',
      code: 'USER_NOT_FOUND'
    });
  }
  
  req.user = user;
  req.session = session;
  next();
};

/**
 * 获取当前用户信息
 */
router.get('/profile', authenticateToken, (req, res) => {
  const { password: _, ...userInfo } = req.user;
  
  res.json({
    success: true,
    data: {
      user: userInfo,
      session: {
        expiresAt: req.session.expiresAt,
        createdAt: req.session.createdAt
      }
    }
  });
});

/**
 * 用户登出
 */
router.post('/logout', authenticateToken, (req, res) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (token) {
    sessions.delete(token);
  }
  
  res.json({
    success: true,
    message: '登出成功'
  });
});

/**
 * 刷新token
 */
router.post('/refresh', authenticateToken, (req, res) => {
  const authHeader = req.headers['authorization'];
  const oldToken = authHeader && authHeader.split(' ')[1];
  
  // 生成新token
  const newToken = `token_${uuidv4()}`;
  const expiresAt = new Date();
  expiresAt.setHours(expiresAt.getHours() + 24);
  
  // 创建新会话
  sessions.set(newToken, {
    userId: req.user.id,
    username: req.user.username,
    expiresAt: expiresAt.toISOString(),
    createdAt: new Date().toISOString()
  });
  
  // 删除旧token
  if (oldToken) {
    sessions.delete(oldToken);
  }
  
  res.json({
    success: true,
    message: 'Token刷新成功',
    data: {
      token: newToken,
      expiresAt: expiresAt.toISOString()
    }
  });
});

export default router;