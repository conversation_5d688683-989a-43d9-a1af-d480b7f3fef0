/**
 * 数据加载器服务
 * 负责加载和处理历史数据
 */
import fs from 'fs-extra';
import path from 'path';
import csvParser from 'csv-parser';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class DataLoader {
  constructor() {
    this.dataCache = new Map();
    this.initialized = false;
    this.dataPath = path.join(__dirname, '../..', 'data');
  }

  /**
   * 初始化数据加载器
   */
  async initialize() {
    try {
      console.log('📂 正在初始化数据加载器...');
      
      // 检查数据目录是否存在，如果不存在则创建模拟数据
      if (!await fs.pathExists(this.dataPath)) {
        console.log('📁 数据目录不存在，创建模拟数据...');
        await this.createMockData();
      } else {
        console.log('📊 数据目录已存在，加载历史数据...');
        await this.loadHistoricalData();
      }
      
      this.initialized = true;
      console.log('✅ 数据加载器初始化完成');
      
    } catch (error) {
      console.error('❌ 数据加载器初始化失败:', error);
      // 即使失败也要初始化，使用内存中的模拟数据
      this.initialized = true;
      console.log('⚠️ 使用内存模拟数据模式');
    }
  }

  /**
   * 创建模拟数据
   */
  async createMockData() {
    const symbols = ['000001', '000002', '600000', '600036', '000858'];
    
    // 创建数据目录结构
    await fs.ensureDir(path.join(this.dataPath, '2024'));
    
    // 生成2024年的模拟数据
    const startDate = new Date('2024-01-01');
    const endDate = new Date('2024-12-31');
    
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      // 跳过周末
      if (d.getDay() === 0 || d.getDay() === 6) continue;
      
      const dateStr = d.toISOString().split('T')[0].replace(/-/g, '');
      const csvData = this.generateDayData(symbols, d);
      
      // 写入CSV文件
      const filePath = path.join(this.dataPath, '2024', `${dateStr}.csv`);
      await fs.writeFile(filePath, csvData);
    }
    
    console.log('✅ 模拟数据创建完成');
  }

  /**
   * 生成单日数据
   */
  generateDayData(symbols, date) {
    const headers = '日期,股票代码,开盘,收盘,最高,最低,成交量,成交额,振幅,涨跌幅,涨跌额,换手率\n';
    
    let csvData = headers;
    
    for (const symbol of symbols) {
      const basePrice = this.getBasePrice(symbol);
      const volatility = 0.02; // 2% 日波动率
      
      // 生成价格数据
      const prevClose = basePrice * (0.98 + Math.random() * 0.04);
      const change = (Math.random() - 0.5) * 2 * volatility * prevClose;
      const close = Math.max(0.01, prevClose + change);
      const open = prevClose * (0.995 + Math.random() * 0.01);
      const high = Math.max(open, close) * (1 + Math.random() * 0.03);
      const low = Math.min(open, close) * (1 - Math.random() * 0.03);
      
      // 生成成交量和成交额
      const volume = Math.floor(1000000 + Math.random() * 10000000);
      const turnover = volume * close;
      
      // 计算其他指标
      const amplitude = ((high - low) / prevClose * 100);
      const changePercent = (change / prevClose * 100);
      const changeAmount = change;
      const turnoverRate = (volume / 1000000000 * 100); // 假设总股本10亿
      
      const dateStr = date.toISOString().split('T')[0];
      
      csvData += `${dateStr},${symbol},${open.toFixed(2)},${close.toFixed(2)},${high.toFixed(2)},${low.toFixed(2)},${volume},${turnover.toFixed(2)},${amplitude.toFixed(2)},${changePercent.toFixed(2)},${changeAmount.toFixed(2)},${turnoverRate.toFixed(2)}\n`;
    }
    
    return csvData;
  }

  /**
   * 获取股票基准价格
   */
  getBasePrice(symbol) {
    const basePrices = {
      '000001': 12.50,
      '000002': 22.30,
      '600000': 18.90,
      '600036': 36.80,
      '000858': 165.30
    };
    return basePrices[symbol] || 20.00;
  }

  /**
   * 加载历史数据
   */
  async loadHistoricalData() {
    try {
      const years = await fs.readdir(this.dataPath);
      
      for (const year of years) {
        const yearPath = path.join(this.dataPath, year);
        const stat = await fs.stat(yearPath);
        
        if (stat.isDirectory()) {
          console.log(`📊 加载 ${year} 年数据...`);
          
          const files = await fs.readdir(yearPath);
          const csvFiles = files.filter(file => file.endsWith('.csv'));
          
          // 只加载最近30天的数据到缓存中，以节省内存
          const recentFiles = csvFiles.slice(-30);
          
          for (const file of recentFiles) {
            const filePath = path.join(yearPath, file);
            const dateStr = file.replace('.csv', '');
            const data = await this.loadCsvFile(filePath);
            this.dataCache.set(dateStr, data);
          }
        }
      }
      
      console.log(`📈 已加载 ${this.dataCache.size} 天的历史数据到缓存`);
      
    } catch (error) {
      console.error('❌ 加载历史数据失败:', error);
      throw error;
    }
  }

  /**
   * 加载CSV文件
   */
  async loadCsvFile(filePath) {
    return new Promise((resolve, reject) => {
      const data = [];
      
      fs.createReadStream(filePath)
        .pipe(csvParser())
        .on('data', (row) => {
          data.push({
            date: row['日期'],
            symbol: row['股票代码'],
            open: parseFloat(row['开盘']),
            close: parseFloat(row['收盘']),
            high: parseFloat(row['最高']),
            low: parseFloat(row['最低']),
            volume: parseInt(row['成交量']),
            turnover: parseFloat(row['成交额']),
            amplitude: parseFloat(row['振幅']),
            changePercent: parseFloat(row['涨跌幅']),
            changeAmount: parseFloat(row['涨跌额']),
            turnoverRate: parseFloat(row['换手率'])
          });
        })
        .on('end', () => {
          resolve(data);
        })
        .on('error', (error) => {
          reject(error);
        });
    });
  }

  /**
   * 获取股票历史数据
   */
  async getStockData(symbol, startDate, endDate, limit = 1000) {
    try {
      // 先从缓存中查找
      const cachedData = [];
      
      for (const [dateStr, dayData] of this.dataCache) {
        const date = this.parseDate(dateStr);
        const start = new Date(startDate);
        const end = new Date(endDate);
        
        if (date >= start && date <= end) {
          const stockData = dayData.filter(item => item.symbol === symbol);
          cachedData.push(...stockData);
        }
      }
      
      if (cachedData.length > 0) {
        return cachedData.slice(0, limit);
      }
      
      // 如果缓存中没有，生成模拟数据
      return this.generateStockData(symbol, startDate, endDate, limit);
      
    } catch (error) {
      console.error('获取股票数据错误:', error);
      return this.generateStockData(symbol, startDate, endDate, limit);
    }
  }

  /**
   * 生成模拟股票数据
   */
  generateStockData(symbol, startDate, endDate, limit) {
    const data = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    const basePrice = this.getBasePrice(symbol);
    
    let currentPrice = basePrice;
    let currentDate = new Date(start);
    
    while (currentDate <= end && data.length < limit) {
      // 跳过周末
      if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) {
        const volatility = 0.02;
        const change = (Math.random() - 0.5) * 2 * volatility * currentPrice;
        const close = Math.max(0.01, currentPrice + change);
        const open = currentPrice * (0.995 + Math.random() * 0.01);
        const high = Math.max(open, close) * (1 + Math.random() * 0.03);
        const low = Math.min(open, close) * (1 - Math.random() * 0.03);
        const volume = Math.floor(1000000 + Math.random() * 10000000);
        
        data.push({
          date: currentDate.toISOString().split('T')[0],
          symbol,
          open: Number(open.toFixed(2)),
          close: Number(close.toFixed(2)),
          high: Number(high.toFixed(2)),
          low: Number(low.toFixed(2)),
          volume,
          turnover: Number((volume * close).toFixed(2)),
          amplitude: Number(((high - low) / currentPrice * 100).toFixed(2)),
          changePercent: Number((change / currentPrice * 100).toFixed(2)),
          changeAmount: Number(change.toFixed(2)),
          turnoverRate: Number((volume / 1000000000 * 100).toFixed(2))
        });
        
        currentPrice = close;
      }
      
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return data;
  }

  /**
   * 解析日期字符串
   */
  parseDate(dateStr) {
    if (dateStr.length === 8) {
      // YYYYMMDD格式
      const year = dateStr.substring(0, 4);
      const month = dateStr.substring(4, 6);
      const day = dateStr.substring(6, 8);
      return new Date(`${year}-${month}-${day}`);
    }
    return new Date(dateStr);
  }

  /**
   * 获取可用的股票列表
   */
  getAvailableSymbols() {
    const symbols = new Set();
    
    for (const dayData of this.dataCache.values()) {
      for (const item of dayData) {
        symbols.add(item.symbol);
      }
    }
    
    return Array.from(symbols);
  }

  /**
   * 获取数据统计信息
   */
  getStats() {
    const symbols = this.getAvailableSymbols();
    const totalRecords = Array.from(this.dataCache.values())
      .reduce((sum, dayData) => sum + dayData.length, 0);
    
    return {
      totalDays: this.dataCache.size,
      totalSymbols: symbols.length,
      totalRecords,
      symbols,
      dateRange: {
        start: Math.min(...Array.from(this.dataCache.keys())),
        end: Math.max(...Array.from(this.dataCache.keys()))
      }
    };
  }
}