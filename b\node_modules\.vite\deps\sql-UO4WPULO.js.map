{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/sql/sql.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/sql/sql.ts\nvar conf = {\n  comments: {\n    lineComment: \"--\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".sql\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \"ABORT\",\n    \"ABSOLUTE\",\n    \"ACTION\",\n    \"ADA\",\n    \"ADD\",\n    \"AFTER\",\n    \"ALL\",\n    \"ALLOCATE\",\n    \"ALTER\",\n    \"ALWAYS\",\n    \"ANALYZE\",\n    \"AND\",\n    \"ANY\",\n    \"ARE\",\n    \"AS\",\n    \"ASC\",\n    \"ASSERTION\",\n    \"AT\",\n    \"ATTACH\",\n    \"AUTHORIZATION\",\n    \"AUTOINCREMENT\",\n    \"AVG\",\n    \"BACKUP\",\n    \"BEFORE\",\n    \"BEGIN\",\n    \"BETWEEN\",\n    \"BIT\",\n    \"BIT_LENGTH\",\n    \"BOTH\",\n    \"BREAK\",\n    \"BROWSE\",\n    \"BULK\",\n    \"BY\",\n    \"CASCADE\",\n    \"CASCADED\",\n    \"CASE\",\n    \"CAST\",\n    \"CATALOG\",\n    \"CHAR\",\n    \"CHARACTER\",\n    \"CHARACTER_LENGTH\",\n    \"CHAR_LENGTH\",\n    \"CHECK\",\n    \"CHECKPOINT\",\n    \"CLOSE\",\n    \"CLUSTERED\",\n    \"COALESCE\",\n    \"COLLATE\",\n    \"COLLATION\",\n    \"COLUMN\",\n    \"COMMIT\",\n    \"COMPUTE\",\n    \"CONFLICT\",\n    \"CONNECT\",\n    \"CONNECTION\",\n    \"CONSTRAINT\",\n    \"CONSTRAINTS\",\n    \"CONTAINS\",\n    \"CONTAINSTABLE\",\n    \"CONTINUE\",\n    \"CONVERT\",\n    \"CORRESPONDING\",\n    \"COUNT\",\n    \"CREATE\",\n    \"CROSS\",\n    \"CURRENT\",\n    \"CURRENT_DATE\",\n    \"CURRENT_TIME\",\n    \"CURRENT_TIMESTAMP\",\n    \"CURRENT_USER\",\n    \"CURSOR\",\n    \"DATABASE\",\n    \"DATE\",\n    \"DAY\",\n    \"DBCC\",\n    \"DEALLOCATE\",\n    \"DEC\",\n    \"DECIMAL\",\n    \"DECLARE\",\n    \"DEFAULT\",\n    \"DEFERRABLE\",\n    \"DEFERRED\",\n    \"DELETE\",\n    \"DENY\",\n    \"DESC\",\n    \"DESCRIBE\",\n    \"DESCRIPTOR\",\n    \"DETACH\",\n    \"DIAGNOSTICS\",\n    \"DISCONNECT\",\n    \"DISK\",\n    \"DISTINCT\",\n    \"DISTRIBUTED\",\n    \"DO\",\n    \"DOMAIN\",\n    \"DOUBLE\",\n    \"DROP\",\n    \"DUMP\",\n    \"EACH\",\n    \"ELSE\",\n    \"END\",\n    \"END-EXEC\",\n    \"ERRLVL\",\n    \"ESCAPE\",\n    \"EXCEPT\",\n    \"EXCEPTION\",\n    \"EXCLUDE\",\n    \"EXCLUSIVE\",\n    \"EXEC\",\n    \"EXECUTE\",\n    \"EXISTS\",\n    \"EXIT\",\n    \"EXPLAIN\",\n    \"EXTERNAL\",\n    \"EXTRACT\",\n    \"FAIL\",\n    \"FALSE\",\n    \"FETCH\",\n    \"FILE\",\n    \"FILLFACTOR\",\n    \"FILTER\",\n    \"FIRST\",\n    \"FLOAT\",\n    \"FOLLOWING\",\n    \"FOR\",\n    \"FOREIGN\",\n    \"FORTRAN\",\n    \"FOUND\",\n    \"FREETEXT\",\n    \"FREETEXTTABLE\",\n    \"FROM\",\n    \"FULL\",\n    \"FUNCTION\",\n    \"GENERATED\",\n    \"GET\",\n    \"GLOB\",\n    \"GLOBAL\",\n    \"GO\",\n    \"GOTO\",\n    \"GRANT\",\n    \"GROUP\",\n    \"GROUPS\",\n    \"HAVING\",\n    \"HOLDLOCK\",\n    \"HOUR\",\n    \"IDENTITY\",\n    \"IDENTITYCOL\",\n    \"IDENTITY_INSERT\",\n    \"IF\",\n    \"IGNORE\",\n    \"IMMEDIATE\",\n    \"IN\",\n    \"INCLUDE\",\n    \"INDEX\",\n    \"INDEXED\",\n    \"INDICATOR\",\n    \"INITIALLY\",\n    \"INNER\",\n    \"INPUT\",\n    \"INSENSITIVE\",\n    \"INSERT\",\n    \"INSTEAD\",\n    \"INT\",\n    \"INTEGER\",\n    \"INTERSECT\",\n    \"INTERVAL\",\n    \"INTO\",\n    \"IS\",\n    \"ISNULL\",\n    \"ISOLATION\",\n    \"JOIN\",\n    \"KEY\",\n    \"KILL\",\n    \"LANGUAGE\",\n    \"LAST\",\n    \"LEADING\",\n    \"LEFT\",\n    \"LEVEL\",\n    \"LIKE\",\n    \"LIMIT\",\n    \"LINENO\",\n    \"LOAD\",\n    \"LOCAL\",\n    \"LOWER\",\n    \"MATCH\",\n    \"MATERIALIZED\",\n    \"MAX\",\n    \"MERGE\",\n    \"MIN\",\n    \"MINUTE\",\n    \"MODULE\",\n    \"MONTH\",\n    \"NAMES\",\n    \"NATIONAL\",\n    \"NATURAL\",\n    \"NCHAR\",\n    \"NEXT\",\n    \"NO\",\n    \"NOCHECK\",\n    \"NONCLUSTERED\",\n    \"NONE\",\n    \"NOT\",\n    \"NOTHING\",\n    \"NOTNULL\",\n    \"NULL\",\n    \"NULLIF\",\n    \"NULLS\",\n    \"NUMERIC\",\n    \"OCTET_LENGTH\",\n    \"OF\",\n    \"OFF\",\n    \"OFFSET\",\n    \"OFFSETS\",\n    \"ON\",\n    \"ONLY\",\n    \"OPEN\",\n    \"OPENDATASOURCE\",\n    \"OPENQUERY\",\n    \"OPENROWSET\",\n    \"OPENXML\",\n    \"OPTION\",\n    \"OR\",\n    \"ORDER\",\n    \"OTHERS\",\n    \"OUTER\",\n    \"OUTPUT\",\n    \"OVER\",\n    \"OVERLAPS\",\n    \"PAD\",\n    \"PARTIAL\",\n    \"PARTITION\",\n    \"PASCAL\",\n    \"PERCENT\",\n    \"PIVOT\",\n    \"PLAN\",\n    \"POSITION\",\n    \"PRAGMA\",\n    \"PRECEDING\",\n    \"PRECISION\",\n    \"PREPARE\",\n    \"PRESERVE\",\n    \"PRIMARY\",\n    \"PRINT\",\n    \"PRIOR\",\n    \"PRIVILEGES\",\n    \"PROC\",\n    \"PROCEDURE\",\n    \"PUBLIC\",\n    \"QUERY\",\n    \"RAISE\",\n    \"RAISERROR\",\n    \"RANGE\",\n    \"READ\",\n    \"READTEXT\",\n    \"REAL\",\n    \"RECONFIGURE\",\n    \"RECURSIVE\",\n    \"REFERENCES\",\n    \"REGEXP\",\n    \"REINDEX\",\n    \"RELATIVE\",\n    \"RELEASE\",\n    \"RENAME\",\n    \"REPLACE\",\n    \"REPLICATION\",\n    \"RESTORE\",\n    \"RESTRICT\",\n    \"RETURN\",\n    \"RETURNING\",\n    \"REVERT\",\n    \"REVOKE\",\n    \"RIGHT\",\n    \"ROLLBACK\",\n    \"ROW\",\n    \"ROWCOUNT\",\n    \"ROWGUIDCOL\",\n    \"ROWS\",\n    \"RULE\",\n    \"SAVE\",\n    \"SAVEPOINT\",\n    \"SCHEMA\",\n    \"SCROLL\",\n    \"SECOND\",\n    \"SECTION\",\n    \"SECURITYAUDIT\",\n    \"SELECT\",\n    \"SEMANTICKEYPHRASETABLE\",\n    \"SEMANTICSIMILARITYDETAILSTABLE\",\n    \"SEMANTICSIMILARITYTABLE\",\n    \"SESSION\",\n    \"SESSION_USER\",\n    \"SET\",\n    \"SETUSER\",\n    \"SHUTDOWN\",\n    \"SIZE\",\n    \"SMALLINT\",\n    \"SOME\",\n    \"SPACE\",\n    \"SQL\",\n    \"SQLCA\",\n    \"SQLCODE\",\n    \"SQLERROR\",\n    \"SQLSTATE\",\n    \"SQLWARNING\",\n    \"STATISTICS\",\n    \"SUBSTRING\",\n    \"SUM\",\n    \"SYSTEM_USER\",\n    \"TABLE\",\n    \"TABLESAMPLE\",\n    \"TEMP\",\n    \"TEMPORARY\",\n    \"TEXTSIZE\",\n    \"THEN\",\n    \"TIES\",\n    \"TIME\",\n    \"TIMESTAMP\",\n    \"TIMEZONE_HOUR\",\n    \"TIMEZONE_MINUTE\",\n    \"TO\",\n    \"TOP\",\n    \"TRAILING\",\n    \"TRAN\",\n    \"TRANSACTION\",\n    \"TRANSLATE\",\n    \"TRANSLATION\",\n    \"TRIGGER\",\n    \"TRIM\",\n    \"TRUE\",\n    \"TRUNCATE\",\n    \"TRY_CONVERT\",\n    \"TSEQUAL\",\n    \"UNBOUNDED\",\n    \"UNION\",\n    \"UNIQUE\",\n    \"UNKNOWN\",\n    \"UNPIVOT\",\n    \"UPDATE\",\n    \"UPDATETEXT\",\n    \"UPPER\",\n    \"USAGE\",\n    \"USE\",\n    \"USER\",\n    \"USING\",\n    \"VACUUM\",\n    \"VALUE\",\n    \"VALUES\",\n    \"VARCHAR\",\n    \"VARYING\",\n    \"VIEW\",\n    \"VIRTUAL\",\n    \"WAITFOR\",\n    \"WHEN\",\n    \"WHENEVER\",\n    \"WHERE\",\n    \"WHILE\",\n    \"WINDOW\",\n    \"WITH\",\n    \"WITHIN GROUP\",\n    \"WITHOUT\",\n    \"WORK\",\n    \"WRITE\",\n    \"WRITETEXT\",\n    \"YEAR\",\n    \"ZONE\"\n  ],\n  operators: [\n    \"ALL\",\n    \"AND\",\n    \"ANY\",\n    \"BETWEEN\",\n    \"EXISTS\",\n    \"IN\",\n    \"LIKE\",\n    \"NOT\",\n    \"OR\",\n    \"SOME\",\n    \"EXCEPT\",\n    \"INTERSECT\",\n    \"UNION\",\n    \"APPLY\",\n    \"CROSS\",\n    \"FULL\",\n    \"INNER\",\n    \"JOIN\",\n    \"LEFT\",\n    \"OUTER\",\n    \"RIGHT\",\n    \"CONTAINS\",\n    \"FREETEXT\",\n    \"IS\",\n    \"NULL\",\n    \"PIVOT\",\n    \"UNPIVOT\",\n    \"MATCHED\"\n  ],\n  builtinFunctions: [\n    \"AVG\",\n    \"CHECKSUM_AGG\",\n    \"COUNT\",\n    \"COUNT_BIG\",\n    \"GROUPING\",\n    \"GROUPING_ID\",\n    \"MAX\",\n    \"MIN\",\n    \"SUM\",\n    \"STDEV\",\n    \"STDEVP\",\n    \"VAR\",\n    \"VARP\",\n    \"CUME_DIST\",\n    \"FIRST_VALUE\",\n    \"LAG\",\n    \"LAST_VALUE\",\n    \"LEAD\",\n    \"PERCENTILE_CONT\",\n    \"PERCENTILE_DISC\",\n    \"PERCENT_RANK\",\n    \"COLLATE\",\n    \"COLLATIONPROPERTY\",\n    \"TERTIARY_WEIGHTS\",\n    \"FEDERATION_FILTERING_VALUE\",\n    \"CAST\",\n    \"CONVERT\",\n    \"PARSE\",\n    \"TRY_CAST\",\n    \"TRY_CONVERT\",\n    \"TRY_PARSE\",\n    \"ASYMKEY_ID\",\n    \"ASYMKEYPROPERTY\",\n    \"CERTPROPERTY\",\n    \"CERT_ID\",\n    \"CRYPT_GEN_RANDOM\",\n    \"DECRYPTBYASYMKEY\",\n    \"DECRYPTBYCERT\",\n    \"DECRYPTBYKEY\",\n    \"DECRYPTBYKEYAUTOASYMKEY\",\n    \"DECRYPTBYKEYAUTOCERT\",\n    \"DECRYPTBYPASSPHRASE\",\n    \"ENCRYPTBYASYMKEY\",\n    \"ENCRYPTBYCERT\",\n    \"ENCRYPTBYKEY\",\n    \"ENCRYPTBYPASSPHRASE\",\n    \"HASHBYTES\",\n    \"IS_OBJECTSIGNED\",\n    \"KEY_GUID\",\n    \"KEY_ID\",\n    \"KEY_NAME\",\n    \"SIGNBYASYMKEY\",\n    \"SIGNBYCERT\",\n    \"SYMKEYPROPERTY\",\n    \"VERIFYSIGNEDBYCERT\",\n    \"VERIFYSIGNEDBYASYMKEY\",\n    \"CURSOR_STATUS\",\n    \"DATALENGTH\",\n    \"IDENT_CURRENT\",\n    \"IDENT_INCR\",\n    \"IDENT_SEED\",\n    \"IDENTITY\",\n    \"SQL_VARIANT_PROPERTY\",\n    \"CURRENT_TIMESTAMP\",\n    \"DATEADD\",\n    \"DATEDIFF\",\n    \"DATEFROMPARTS\",\n    \"DATENAME\",\n    \"DATEPART\",\n    \"DATETIME2FROMPARTS\",\n    \"DATETIMEFROMPARTS\",\n    \"DATETIMEOFFSETFROMPARTS\",\n    \"DAY\",\n    \"EOMONTH\",\n    \"GETDATE\",\n    \"GETUTCDATE\",\n    \"ISDATE\",\n    \"MONTH\",\n    \"SMALLDATETIMEFROMPARTS\",\n    \"SWITCHOFFSET\",\n    \"SYSDATETIME\",\n    \"SYSDATETIMEOFFSET\",\n    \"SYSUTCDATETIME\",\n    \"TIMEFROMPARTS\",\n    \"TODATETIMEOFFSET\",\n    \"YEAR\",\n    \"CHOOSE\",\n    \"COALESCE\",\n    \"IIF\",\n    \"NULLIF\",\n    \"ABS\",\n    \"ACOS\",\n    \"ASIN\",\n    \"ATAN\",\n    \"ATN2\",\n    \"CEILING\",\n    \"COS\",\n    \"COT\",\n    \"DEGREES\",\n    \"EXP\",\n    \"FLOOR\",\n    \"LOG\",\n    \"LOG10\",\n    \"PI\",\n    \"POWER\",\n    \"RADIANS\",\n    \"RAND\",\n    \"ROUND\",\n    \"SIGN\",\n    \"SIN\",\n    \"SQRT\",\n    \"SQUARE\",\n    \"TAN\",\n    \"APP_NAME\",\n    \"APPLOCK_MODE\",\n    \"APPLOCK_TEST\",\n    \"ASSEMBLYPROPERTY\",\n    \"COL_LENGTH\",\n    \"COL_NAME\",\n    \"COLUMNPROPERTY\",\n    \"DATABASE_PRINCIPAL_ID\",\n    \"DATABASEPROPERTYEX\",\n    \"DB_ID\",\n    \"DB_NAME\",\n    \"FILE_ID\",\n    \"FILE_IDEX\",\n    \"FILE_NAME\",\n    \"FILEGROUP_ID\",\n    \"FILEGROUP_NAME\",\n    \"FILEGROUPPROPERTY\",\n    \"FILEPROPERTY\",\n    \"FULLTEXTCATALOGPROPERTY\",\n    \"FULLTEXTSERVICEPROPERTY\",\n    \"INDEX_COL\",\n    \"INDEXKEY_PROPERTY\",\n    \"INDEXPROPERTY\",\n    \"OBJECT_DEFINITION\",\n    \"OBJECT_ID\",\n    \"OBJECT_NAME\",\n    \"OBJECT_SCHEMA_NAME\",\n    \"OBJECTPROPERTY\",\n    \"OBJECTPROPERTYEX\",\n    \"ORIGINAL_DB_NAME\",\n    \"PARSENAME\",\n    \"SCHEMA_ID\",\n    \"SCHEMA_NAME\",\n    \"SCOPE_IDENTITY\",\n    \"SERVERPROPERTY\",\n    \"STATS_DATE\",\n    \"TYPE_ID\",\n    \"TYPE_NAME\",\n    \"TYPEPROPERTY\",\n    \"DENSE_RANK\",\n    \"NTILE\",\n    \"RANK\",\n    \"ROW_NUMBER\",\n    \"PUBLISHINGSERVERNAME\",\n    \"OPENDATASOURCE\",\n    \"OPENQUERY\",\n    \"OPENROWSET\",\n    \"OPENXML\",\n    \"CERTENCODED\",\n    \"CERTPRIVATEKEY\",\n    \"CURRENT_USER\",\n    \"HAS_DBACCESS\",\n    \"HAS_PERMS_BY_NAME\",\n    \"IS_MEMBER\",\n    \"IS_ROLEMEMBER\",\n    \"IS_SRVROLEMEMBER\",\n    \"LOGINPROPERTY\",\n    \"ORIGINAL_LOGIN\",\n    \"PERMISSIONS\",\n    \"PWDENCRYPT\",\n    \"PWDCOMPARE\",\n    \"SESSION_USER\",\n    \"SESSIONPROPERTY\",\n    \"SUSER_ID\",\n    \"SUSER_NAME\",\n    \"SUSER_SID\",\n    \"SUSER_SNAME\",\n    \"SYSTEM_USER\",\n    \"USER\",\n    \"USER_ID\",\n    \"USER_NAME\",\n    \"ASCII\",\n    \"CHAR\",\n    \"CHARINDEX\",\n    \"CONCAT\",\n    \"DIFFERENCE\",\n    \"FORMAT\",\n    \"LEFT\",\n    \"LEN\",\n    \"LOWER\",\n    \"LTRIM\",\n    \"NCHAR\",\n    \"PATINDEX\",\n    \"QUOTENAME\",\n    \"REPLACE\",\n    \"REPLICATE\",\n    \"REVERSE\",\n    \"RIGHT\",\n    \"RTRIM\",\n    \"SOUNDEX\",\n    \"SPACE\",\n    \"STR\",\n    \"STUFF\",\n    \"SUBSTRING\",\n    \"UNICODE\",\n    \"UPPER\",\n    \"BINARY_CHECKSUM\",\n    \"CHECKSUM\",\n    \"CONNECTIONPROPERTY\",\n    \"CONTEXT_INFO\",\n    \"CURRENT_REQUEST_ID\",\n    \"ERROR_LINE\",\n    \"ERROR_NUMBER\",\n    \"ERROR_MESSAGE\",\n    \"ERROR_PROCEDURE\",\n    \"ERROR_SEVERITY\",\n    \"ERROR_STATE\",\n    \"FORMATMESSAGE\",\n    \"GETANSINULL\",\n    \"GET_FILESTREAM_TRANSACTION_CONTEXT\",\n    \"HOST_ID\",\n    \"HOST_NAME\",\n    \"ISNULL\",\n    \"ISNUMERIC\",\n    \"MIN_ACTIVE_ROWVERSION\",\n    \"NEWID\",\n    \"NEWSEQUENTIALID\",\n    \"ROWCOUNT_BIG\",\n    \"XACT_STATE\",\n    \"TEXTPTR\",\n    \"TEXTVALID\",\n    \"COLUMNS_UPDATED\",\n    \"EVENTDATA\",\n    \"TRIGGER_NESTLEVEL\",\n    \"UPDATE\",\n    \"CHANGETABLE\",\n    \"CHANGE_TRACKING_CONTEXT\",\n    \"CHANGE_TRACKING_CURRENT_VERSION\",\n    \"CHANGE_TRACKING_IS_COLUMN_IN_MASK\",\n    \"CHANGE_TRACKING_MIN_VALID_VERSION\",\n    \"CONTAINSTABLE\",\n    \"FREETEXTTABLE\",\n    \"SEMANTICKEYPHRASETABLE\",\n    \"SEMANTICSIMILARITYDETAILSTABLE\",\n    \"SEMANTICSIMILARITYTABLE\",\n    \"FILETABLEROOTPATH\",\n    \"GETFILENAMESPACEPATH\",\n    \"GETPATHLOCATOR\",\n    \"PATHNAME\",\n    \"GET_TRANSMISSION_STATUS\"\n  ],\n  builtinVariables: [\n    \"@@DATEFIRST\",\n    \"@@DBTS\",\n    \"@@LANGID\",\n    \"@@LANGUAGE\",\n    \"@@LOCK_TIMEOUT\",\n    \"@@MAX_CONNECTIONS\",\n    \"@@MAX_PRECISION\",\n    \"@@NESTLEVEL\",\n    \"@@OPTIONS\",\n    \"@@REMSERVER\",\n    \"@@SERVERNAME\",\n    \"@@SERVICENAME\",\n    \"@@SPID\",\n    \"@@TEXTSIZE\",\n    \"@@VERSION\",\n    \"@@CURSOR_ROWS\",\n    \"@@FETCH_STATUS\",\n    \"@@DATEFIRST\",\n    \"@@PROCID\",\n    \"@@ERROR\",\n    \"@@IDENTITY\",\n    \"@@ROWCOUNT\",\n    \"@@TRANCOUNT\",\n    \"@@CONNECTIONS\",\n    \"@@CPU_BUSY\",\n    \"@@IDLE\",\n    \"@@IO_BUSY\",\n    \"@@PACKET_ERRORS\",\n    \"@@PACK_RECEIVED\",\n    \"@@PACK_SENT\",\n    \"@@TIMETICKS\",\n    \"@@TOTAL_ERRORS\",\n    \"@@TOTAL_READ\",\n    \"@@TOTAL_WRITE\"\n  ],\n  pseudoColumns: [\"$ACTION\", \"$IDENTITY\", \"$ROWGUID\", \"$PARTITION\"],\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@pseudoColumns\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@complexIdentifiers\" },\n      { include: \"@scopes\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [\n        /[\\w@#$]+/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@builtinVariables\": \"predefined\",\n            \"@builtinFunctions\": \"predefined\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [/--+.*/, \"comment\"],\n      [/\\/\\*/, { token: \"comment.quote\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^*/]+/, \"comment\"],\n      [/\\*\\//, { token: \"comment.quote\", next: \"@pop\" }],\n      [/./, \"comment\"]\n    ],\n    pseudoColumns: [\n      [\n        /[$][A-Za-z_][\\w@#$]*/,\n        {\n          cases: {\n            \"@pseudoColumns\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [\n      [/N'/, { token: \"string\", next: \"@string\" }],\n      [/'/, { token: \"string\", next: \"@string\" }]\n    ],\n    string: [\n      [/[^']+/, \"string\"],\n      [/''/, \"string\"],\n      [/'/, { token: \"string\", next: \"@pop\" }]\n    ],\n    complexIdentifiers: [\n      [/\\[/, { token: \"identifier.quote\", next: \"@bracketedIdentifier\" }],\n      [/\"/, { token: \"identifier.quote\", next: \"@quotedIdentifier\" }]\n    ],\n    bracketedIdentifier: [\n      [/[^\\]]+/, \"identifier\"],\n      [/]]/, \"identifier\"],\n      [/]/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    quotedIdentifier: [\n      [/[^\"]+/, \"identifier\"],\n      [/\"\"/, \"identifier\"],\n      [/\"/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    scopes: [\n      [/BEGIN\\s+(DISTRIBUTED\\s+)?TRAN(SACTION)?\\b/i, \"keyword\"],\n      [/BEGIN\\s+TRY\\b/i, { token: \"keyword.try\" }],\n      [/END\\s+TRY\\b/i, { token: \"keyword.try\" }],\n      [/BEGIN\\s+CATCH\\b/i, { token: \"keyword.catch\" }],\n      [/END\\s+CATCH\\b/i, { token: \"keyword.catch\" }],\n      [/(BEGIN|CASE)\\b/i, { token: \"keyword.block\" }],\n      [/END\\b/i, { token: \"keyword.block\" }],\n      [/WHEN\\b/i, { token: \"keyword.choice\" }],\n      [/THEN\\b/i, { token: \"keyword.choice\" }]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,EAC1D;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,eAAe,CAAC,WAAW,aAAa,YAAY,YAAY;AAAA,EAChE,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,YAAY;AAAA,MACvB,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,sBAAsB;AAAA,MACjC,EAAE,SAAS,UAAU;AAAA,MACrB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,QAAQ,WAAW;AAAA,MACpB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,qBAAqB;AAAA,YACrB,qBAAqB;AAAA,YACrB,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,oBAAoB,UAAU;AAAA,IACjC;AAAA,IACA,YAAY,CAAC,CAAC,OAAO,OAAO,CAAC;AAAA,IAC7B,UAAU;AAAA,MACR,CAAC,SAAS,SAAS;AAAA,MACnB,CAAC,QAAQ,EAAE,OAAO,iBAAiB,MAAM,WAAW,CAAC;AAAA,IACvD;AAAA,IACA,SAAS;AAAA,MACP,CAAC,UAAU,SAAS;AAAA,MACpB,CAAC,QAAQ,EAAE,OAAO,iBAAiB,MAAM,OAAO,CAAC;AAAA,MACjD,CAAC,KAAK,SAAS;AAAA,IACjB;AAAA,IACA,eAAe;AAAA,MACb;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,kBAAkB;AAAA,YAClB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,CAAC,qBAAqB,QAAQ;AAAA,MAC9B,CAAC,uBAAuB,QAAQ;AAAA,MAChC,CAAC,2CAA2C,QAAQ;AAAA,IACtD;AAAA,IACA,SAAS;AAAA,MACP,CAAC,MAAM,EAAE,OAAO,UAAU,MAAM,UAAU,CAAC;AAAA,MAC3C,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,UAAU,CAAC;AAAA,IAC5C;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,MAAM,QAAQ;AAAA,MACf,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,IACzC;AAAA,IACA,oBAAoB;AAAA,MAClB,CAAC,MAAM,EAAE,OAAO,oBAAoB,MAAM,uBAAuB,CAAC;AAAA,MAClE,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,oBAAoB,CAAC;AAAA,IAChE;AAAA,IACA,qBAAqB;AAAA,MACnB,CAAC,UAAU,YAAY;AAAA,MACvB,CAAC,MAAM,YAAY;AAAA,MACnB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,IACnD;AAAA,IACA,kBAAkB;AAAA,MAChB,CAAC,SAAS,YAAY;AAAA,MACtB,CAAC,MAAM,YAAY;AAAA,MACnB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,IACnD;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,8CAA8C,SAAS;AAAA,MACxD,CAAC,kBAAkB,EAAE,OAAO,cAAc,CAAC;AAAA,MAC3C,CAAC,gBAAgB,EAAE,OAAO,cAAc,CAAC;AAAA,MACzC,CAAC,oBAAoB,EAAE,OAAO,gBAAgB,CAAC;AAAA,MAC/C,CAAC,kBAAkB,EAAE,OAAO,gBAAgB,CAAC;AAAA,MAC7C,CAAC,mBAAmB,EAAE,OAAO,gBAAgB,CAAC;AAAA,MAC9C,CAAC,UAAU,EAAE,OAAO,gBAAgB,CAAC;AAAA,MACrC,CAAC,WAAW,EAAE,OAAO,iBAAiB,CAAC;AAAA,MACvC,CAAC,WAAW,EAAE,OAAO,iBAAiB,CAAC;AAAA,IACzC;AAAA,EACF;AACF;", "names": []}