/**
 * 策略管理API
 */
import { httpClient } from '../utils/http'
import { API_PATHS, ENV_CONFIG, STRATEGY_CONFIG } from '../utils/constants'
import type { ApiResponse, ListResponse, PaginatedResponse } from '../utils/http'

// 策略类型定义
export type StrategyType = 'trend_following' | 'mean_reversion' | 'momentum' | 'arbitrage' | 'market_making'
export type StrategyStatus = 'inactive' | 'active' | 'paused' | 'error'
export type RiskLevel = 'low' | 'medium' | 'high'
export type StrategyFrequency = 'tick' | 'minute' | 'hour' | 'day'

// 策略参数类型
export interface StrategyParameter {
  name: string
  type: 'number' | 'string' | 'boolean' | 'select'
  value: any
  description?: string
  min?: number
  max?: number
  options?: Array<{ label: string; value: any }>
}

// 策略配置
export interface StrategyConfig {
  symbols: string[]
  maxPositions: number
  maxPositionSize: number
  frequency: StrategyFrequency
  stopLoss?: number
  takeProfit?: number
  maxDrawdown?: number
  initialCapital: number
  positionSizing: 'fixed' | 'percent' | 'kelly' | 'optimal_f'
  positionSizeValue: number
  commission: number
  slippage: number
  enableShortSelling: boolean
  enableLeverage: boolean
  maxLeverage?: number
}

// 策略性能数据
export interface StrategyPerformance {
  totalReturn: number
  annualizedReturn: number
  maxDrawdown: number
  sharpeRatio: number
  winRate: number
  profitFactor: number
  totalTrades: number
  avgTradeDuration: number
  volatility: number
  beta: number
  alpha: number
  informationRatio: number
}

// 策略基本信息
export interface Strategy {
  id: string
  name: string
  description: string
  type: StrategyType
  status: StrategyStatus
  riskLevel: RiskLevel
  frequency: StrategyFrequency
  code: string
  codeLanguage: 'python' | 'javascript' | 'pine'
  parameters: StrategyParameter[]
  config: StrategyConfig
  tags: string[]
  version: string
  createdAt: string
  updatedAt: string
  lastRunAt?: string
  createdBy: string
  authorName: string
  isPublic: boolean
  runningTime?: number
  totalRuns: number
  successRuns: number
  failureRuns: number
  performance?: StrategyPerformance
}

// 策略信号
export interface StrategySignal {
  id: string
  strategyId: string
  symbol: string
  signal: 'buy' | 'sell' | 'hold'
  price: number
  quantity: number
  confidence: number
  timestamp: number
  reason: string
  metadata?: Record<string, any>
}

// 策略模板
export interface StrategyTemplate {
  id: string
  name: string
  description: string
  type: StrategyType
  code: string
  parameters: StrategyParameter[]
  tags: string[]
  author: string
  downloads: number
  rating: number
  createdAt: string
}

// 请求参数类型
export interface StrategyListParams {
  page?: number
  pageSize?: number
  keyword?: string
  type?: StrategyType
  status?: StrategyStatus
  riskLevel?: RiskLevel
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface CreateStrategyRequest {
  name: string
  description: string
  type: StrategyType
  code: string
  codeLanguage: 'python' | 'javascript' | 'pine'
  parameters?: StrategyParameter[]
  config: StrategyConfig
  tags?: string[]
  isPublic?: boolean
}

export interface UpdateStrategyRequest extends Partial<CreateStrategyRequest> {
  id: string
}

// 模拟数据生成
const generateMockStrategy = (id: string): Strategy => {
  const types: StrategyType[] = ['trend_following', 'mean_reversion', 'momentum', 'arbitrage', 'market_making']
  const statuses: StrategyStatus[] = ['inactive', 'active', 'paused', 'error']
  const riskLevels: RiskLevel[] = ['low', 'medium', 'high']
  
  return {
    id,
    name: `策略${id.slice(-3)}`,
    description: `这是一个${types[Math.floor(Math.random() * types.length)]}策略的示例`,
    type: types[Math.floor(Math.random() * types.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    riskLevel: riskLevels[Math.floor(Math.random() * riskLevels.length)],
    frequency: 'day',
    code: `# 策略代码示例\nclass Strategy:\n    def __init__(self):\n        self.name = "示例策略"\n    \n    def on_data(self, data):\n        pass`,
    codeLanguage: 'python',
    parameters: [
      {
        name: 'period',
        type: 'number',
        value: 20,
        description: '移动平均周期',
        min: 5,
        max: 100
      }
    ],
    config: {
      symbols: ['000001', '000002'],
      maxPositions: 10,
      maxPositionSize: 0.1,
      frequency: 'day',
      initialCapital: 100000,
      positionSizing: 'percent',
      positionSizeValue: 0.05,
      commission: 0.0003,
      slippage: 0.0001,
      enableShortSelling: false,
      enableLeverage: false
    },
    tags: ['技术分析', '趋势跟踪'],
    version: '1.0.0',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'user-001',
    authorName: '量化研究员',
    isPublic: Math.random() > 0.5,
    totalRuns: Math.floor(Math.random() * 100),
    successRuns: Math.floor(Math.random() * 80),
    failureRuns: Math.floor(Math.random() * 20),
    performance: {
      totalReturn: (Math.random() - 0.3) * 50,
      annualizedReturn: (Math.random() - 0.3) * 30,
      maxDrawdown: Math.random() * 20,
      sharpeRatio: Math.random() * 3,
      winRate: Math.random() * 100,
      profitFactor: Math.random() * 3 + 0.5,
      totalTrades: Math.floor(Math.random() * 500),
      avgTradeDuration: Math.random() * 10,
      volatility: Math.random() * 30,
      beta: Math.random() * 2,
      alpha: (Math.random() - 0.5) * 10,
      informationRatio: Math.random() * 2
    }
  }
}

/**
 * 策略管理API类
 */
export class StrategyAPI {
  private useMock = ENV_CONFIG.enableMock

  /**
   * 获取策略列表
   */
  async getStrategies(params?: StrategyListParams): Promise<PaginatedResponse<Strategy>> {
    if (this.useMock) {
      const mockStrategies = Array.from({ length: 20 }, (_, i) => 
        generateMockStrategy(`strategy-${String(i + 1).padStart(3, '0')}`)
      )
      
      let filteredStrategies = mockStrategies
      
      // 应用过滤条件
      if (params?.keyword) {
        filteredStrategies = filteredStrategies.filter(s => 
          s.name.includes(params.keyword!) || s.description.includes(params.keyword!)
        )
      }
      
      if (params?.type) {
        filteredStrategies = filteredStrategies.filter(s => s.type === params.type)
      }
      
      if (params?.status) {
        filteredStrategies = filteredStrategies.filter(s => s.status === params.status)
      }
      
      // 分页
      const page = params?.page || 1
      const pageSize = params?.pageSize || 10
      const start = (page - 1) * pageSize
      const end = start + pageSize
      
      return {
        items: filteredStrategies.slice(start, end),
        total: filteredStrategies.length,
        page,
        pageSize,
        totalPages: Math.ceil(filteredStrategies.length / pageSize)
      }
    }

    try {
      const response = await httpClient.get<PaginatedResponse<Strategy>>(API_PATHS.STRATEGY.LIST, params)
      return response.data!
    } catch (error) {
      console.warn('获取策略列表API调用失败，使用模拟数据:', error)
      return {
        items: [generateMockStrategy('strategy-001')],
        total: 1,
        page: 1,
        pageSize: 10,
        totalPages: 1
      }
    }
  }

  /**
   * 获取策略详情
   */
  async getStrategy(id: string): Promise<Strategy> {
    if (this.useMock) {
      return generateMockStrategy(id)
    }

    try {
      const response = await httpClient.get<Strategy>(`${API_PATHS.STRATEGY.DETAIL}/${id}`)
      return response.data!
    } catch (error) {
      console.warn('获取策略详情API调用失败，使用模拟数据:', error)
      return generateMockStrategy(id)
    }
  }

  /**
   * 创建策略
   */
  async createStrategy(data: CreateStrategyRequest): Promise<Strategy> {
    if (this.useMock) {
      const newStrategy = generateMockStrategy(`strategy-${Date.now()}`)
      return {
        ...newStrategy,
        ...data,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    }

    try {
      const response = await httpClient.post<Strategy>(API_PATHS.STRATEGY.CREATE, data)
      return response.data!
    } catch (error) {
      console.warn('创建策略API调用失败，使用模拟数据:', error)
      throw error
    }
  }

  /**
   * 更新策略
   */
  async updateStrategy(data: UpdateStrategyRequest): Promise<Strategy> {
    if (this.useMock) {
      const existingStrategy = generateMockStrategy(data.id)
      return {
        ...existingStrategy,
        ...data,
        updatedAt: new Date().toISOString()
      }
    }

    try {
      const response = await httpClient.put<Strategy>(`${API_PATHS.STRATEGY.UPDATE}/${data.id}`, data)
      return response.data!
    } catch (error) {
      console.warn('更新策略API调用失败:', error)
      throw error
    }
  }

  /**
   * 删除策略
   */
  async deleteStrategy(id: string): Promise<void> {
    if (this.useMock) {
      return Promise.resolve()
    }

    try {
      await httpClient.delete(`${API_PATHS.STRATEGY.DELETE}/${id}`)
    } catch (error) {
      console.warn('删除策略API调用失败:', error)
      throw error
    }
  }

  /**
   * 启动策略
   */
  async startStrategy(id: string): Promise<void> {
    if (this.useMock) {
      return Promise.resolve()
    }

    try {
      await httpClient.post(`${API_PATHS.STRATEGY.START}/${id}`)
    } catch (error) {
      console.warn('启动策略API调用失败:', error)
      throw error
    }
  }

  /**
   * 停止策略
   */
  async stopStrategy(id: string): Promise<void> {
    if (this.useMock) {
      return Promise.resolve()
    }

    try {
      await httpClient.post(`${API_PATHS.STRATEGY.STOP}/${id}`)
    } catch (error) {
      console.warn('停止策略API调用失败:', error)
      throw error
    }
  }

  /**
   * 获取策略模板
   */
  async getTemplates(): Promise<StrategyTemplate[]> {
    if (this.useMock) {
      return [
        {
          id: 'template-001',
          name: '双均线策略',
          description: '基于短期和长期移动平均线的经典策略',
          type: 'trend_following',
          code: '# 双均线策略代码',
          parameters: [],
          tags: ['技术分析', '趋势跟踪'],
          author: '量化团队',
          downloads: 1250,
          rating: 4.5,
          createdAt: new Date().toISOString()
        }
      ]
    }

    try {
      const response = await httpClient.get<StrategyTemplate[]>(API_PATHS.STRATEGY.TEMPLATES)
      return response.data || []
    } catch (error) {
      console.warn('获取策略模板API调用失败，使用模拟数据:', error)
      return []
    }
  }
}

// 创建实例
export const strategyApi = new StrategyAPI()

// 导出默认实例
export default strategyApi
