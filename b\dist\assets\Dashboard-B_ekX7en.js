import{c as G,o as ce,a as pe,t as Q,u as oe,s as e,i as d,m as A,b as ae,d as ne,e as de,f as K,S as re,g as le}from"./index-B8TYkGLu.js";import{S as ge}from"./SimpleSlideVerify-DHA9A53Q.js";var fe=Q('<div style=border-radius:4px;background-color:#f7f9fa;user-select:none><div><canvas style="border-radius:4px 4px 0 0"></canvas><canvas style="border-radius:4px 4px 0 0"></canvas></div><div style="background-color:#f7f9fa;border-top:1px solid #e1e5e9;align-items:center"><div style="border-radius:4px;align-items:center;justify-content:center;font-size:18px;box-shadow:0 2px 4px rgba(0,0,0,0.1)"></div><div style=text-align:center;font-size:14px;pointer-events:none></div><button title=刷新验证码 style=background-color:transparent;font-size:16px>↻'),he=Q('<div style="background-color:rgba(255, 255, 255, 0.8);align-items:center;justify-content:center"><div>加载中...');function ue(t){const[k,S]=G(!1),[x,_]=G(!1),[b,$]=G(0),[V,R]=G(!1),[L,C]=G(0),[v,J]=G(0),[O,D]=G(!1);let z,c;const i=()=>t.width||320,r=()=>t.height||160,T=()=>t.sliderText||"向右滑动验证",F=()=>{const l=Math.random()*(i()-100)+50;return J(l),l},N=()=>{if(console.log("Drawing puzzle...",{canvasRef:z,blockCanvasRef:c,width:i(),height:r()}),!z||!c){console.log("Canvas refs not available");return}const l=z,g=c,a=l.getContext("2d"),s=g.getContext("2d");if(!a||!s){console.log("Canvas contexts not available");return}console.log("Starting to draw..."),l.width=i(),l.height=r(),g.width=i(),g.height=r(),a.clearRect(0,0,i(),r()),s.clearRect(0,0,i(),r());const E=a.createLinearGradient(0,0,i(),r());E.addColorStop(0,"#74b9ff"),E.addColorStop(1,"#0984e3"),a.fillStyle=E,a.fillRect(0,0,i(),r()),a.fillStyle="rgba(255, 255, 255, 0.3)";for(let f=0;f<20;f++){const w=Math.random()*i(),n=Math.random()*r(),h=Math.random()*3+1;a.beginPath(),a.arc(w,n,h,0,Math.PI*2),a.fill()}const o=42,m=v(),p=r()/2-o/2;console.log("Puzzle position:",{x:m,y:p,puzzleSize:o});const M=(f,w,n)=>{f.beginPath(),f.moveTo(w,n),f.lineTo(w+o,n),f.lineTo(w+o,n+o),f.lineTo(w,n+o),f.closePath()};a.save(),M(a,m,p),a.globalCompositeOperation="destination-out",a.fill(),a.restore(),s.save(),M(s,m,p),s.clip();const X=s.createLinearGradient(0,0,i(),r());X.addColorStop(0,"#74b9ff"),X.addColorStop(1,"#0984e3"),s.fillStyle=X,s.fillRect(0,0,i(),r()),s.restore(),s.strokeStyle="#fff",s.lineWidth=2,M(s,m,p),s.stroke(),D(!0),console.log("Puzzle drawing completed")},U=()=>{console.log("Resetting puzzle..."),S(!1),_(!1),$(0),R(!1),D(!1),q()},ie=()=>{Math.abs(b()-v())<5?(S(!0),_(!1),t.onSuccess?.()):(_(!0),S(!1),t.onFail?.(),setTimeout(U,1e3))},B=l=>{if(k())return;R(!0);const g="touches"in l?l.touches[0].clientX:l.clientX;C(g-b())},P=l=>{if(!V()||k())return;l.preventDefault();const g="touches"in l?l.touches[0].clientX:l.clientX,a=Math.max(0,Math.min(g-L(),i()-60));$(a)},I=()=>{!V()||k()||(R(!1),ie())},q=()=>{console.log("Initializing puzzle..."),F(),setTimeout(()=>{N()},50)};return ce(()=>{console.log("Component mounted"),setTimeout(()=>{console.log("Delayed initialization"),q()},200),document.addEventListener("mousemove",P),document.addEventListener("mouseup",I),document.addEventListener("touchmove",P,{passive:!1}),document.addEventListener("touchend",I)}),pe(()=>{document.removeEventListener("mousemove",P),document.removeEventListener("mouseup",I),document.removeEventListener("touchmove",P),document.removeEventListener("touchend",I)}),(()=>{var l=fe(),g=l.firstChild,a=g.firstChild,s=a.nextSibling,E=g.nextSibling,o=E.firstChild,m=o.nextSibling,p=m.nextSibling,M=containerRef;typeof M=="function"?oe(M,l):containerRef=l,e(l,"border","1px solid #e1e5e9"),e(l,"position","relative"),e(g,"position","relative"),e(g,"overflow","hidden");var X=z;typeof X=="function"?oe(X,a):z=a,e(a,"display","block");var f=c;typeof f=="function"?oe(f,s):c=s,e(s,"position","absolute"),e(s,"top","0"),e(s,"display","block"),d(g,(()=>{var n=A(()=>!!isLoading());return()=>n()&&(()=>{var h=he();return e(h,"position","absolute"),e(h,"top","0"),e(h,"left","0"),e(h,"width","100%"),e(h,"height","100%"),e(h,"display","flex"),h})()})(),null),e(E,"height","40px"),e(E,"position","relative"),e(E,"display","flex"),o.$$touchstart=B,o.$$mousedown=B;var w=sliderRef;return typeof w=="function"?oe(w,o):sliderRef=o,e(o,"position","absolute"),e(o,"width","60px"),e(o,"height","38px"),e(o,"border","1px solid #d9d9d9"),e(o,"display","flex"),e(o,"color","white"),d(o,(()=>{var n=A(()=>!!k());return()=>n()?"✓":x()?"✗":"→"})()),e(m,"width","100%"),e(m,"color","#999"),d(m,(()=>{var n=A(()=>!!k());return()=>n()?"验证成功":A(()=>!!x())()?"验证失败，请重试":T()})()),p.$$click=()=>{U(),t.onRefresh?.()},e(p,"position","absolute"),e(p,"right","10px"),e(p,"width","24px"),e(p,"height","24px"),e(p,"border","none"),e(p,"cursor","pointer"),e(p,"color","#999"),ae(n=>{var h=`${i()}px`,H=i(),Y=r(),j=i(),y=r(),W=`${b()}px`,u=V()?"none":"left 0.3s ease",Z=`${b()}px`,ee=k()?"#52c41a":x()?"#ff4d4f":"#1890ff",te=V()?"grabbing":"grab",se=V()?"none":"all 0.3s ease";return h!==n.e&&e(l,"width",n.e=h),H!==n.t&&ne(a,"width",n.t=H),Y!==n.a&&ne(a,"height",n.a=Y),j!==n.o&&ne(s,"width",n.o=j),y!==n.i&&ne(s,"height",n.i=y),W!==n.n&&e(s,"left",n.n=W),u!==n.s&&e(s,"transition",n.s=u),Z!==n.h&&e(o,"left",n.h=Z),ee!==n.r&&e(o,"background-color",n.r=ee),te!==n.d&&e(o,"cursor",n.d=te),se!==n.l&&e(o,"transition",n.l=se),n},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0}),l})()}de(["mousedown","touchstart","click"]);var xe=Q("<div><h2>👤 用户信息</h2><div><div><img alt=用户头像><div><h3></h3><p>@</p></div></div><span>用户ID:</span><span></span><span>邮箱:</span><span></span><span>手机:</span><span></span><span>角色:</span><span></span><span>状态:</span><span></span><span>注册时间:</span><span></span><span>最后登录:</span><span></span></div><div><h4>偏好设置</h4><div><span>主题:</span><span></span><span>语言:</span><span></span><span>时区:</span><span></span></div></div><div><h4>个人资料</h4><div><span>真实姓名:</span><span></span><span>性别:</span><span></span><span>投资经验:</span><span></span><span>风险承受:</span><span></span></div></div><div><button>✏️ 编辑资料</button><button>🚪 退出登录");function be(){const t=()=>le.state.user,k=()=>le.state.isAuthenticated;return K(re,{get when(){return A(()=>!!k())()&&t()},get children(){var S=xe(),x=S.firstChild,_=x.nextSibling,b=_.firstChild,$=b.firstChild,V=$.nextSibling,R=V.firstChild,L=R.nextSibling;L.firstChild;var C=b.nextSibling,v=C.nextSibling,J=v.nextSibling,O=J.nextSibling,D=O.nextSibling,z=D.nextSibling,c=z.nextSibling,i=c.nextSibling,r=i.nextSibling,T=r.nextSibling,F=T.nextSibling,N=F.nextSibling,U=N.nextSibling,ie=U.nextSibling,B=_.nextSibling,P=B.firstChild,I=P.nextSibling,q=I.firstChild,l=q.nextSibling,g=l.nextSibling,a=g.nextSibling,s=a.nextSibling,E=s.nextSibling,o=B.nextSibling,m=o.firstChild,p=m.nextSibling,M=p.firstChild,X=M.nextSibling,f=X.nextSibling,w=f.nextSibling,n=w.nextSibling,h=n.nextSibling,H=h.nextSibling,Y=H.nextSibling,j=o.nextSibling,y=j.firstChild,W=y.nextSibling;return e(S,"background","white"),e(S,"borderRadius","12px"),e(S,"padding","24px"),e(S,"boxShadow","0 4px 12px rgba(0,0,0,0.1)"),e(S,"margin","20px 0"),e(x,"fontSize","20px"),e(x,"fontWeight","bold"),e(x,"color","#111827"),e(x,"marginBottom","20px"),e(x,"display","flex"),e(x,"alignItems","center"),e(x,"gap","8px"),e(_,"display","grid"),e(_,"gridTemplateColumns","auto 1fr"),e(_,"gap","16px"),e(_,"alignItems","center"),e(b,"gridColumn","1 / -1"),e(b,"display","flex"),e(b,"alignItems","center"),e(b,"gap","16px"),e(b,"marginBottom","16px"),e($,"width","64px"),e($,"height","64px"),e($,"borderRadius","50%"),e($,"border","3px solid #e5e7eb"),e(R,"fontSize","18px"),e(R,"fontWeight","600"),e(R,"color","#111827"),e(R,"margin","0 0 4px 0"),d(R,()=>t()?.nickname),e(L,"fontSize","14px"),e(L,"color","#6b7280"),e(L,"margin","0"),d(L,()=>t()?.username,null),e(C,"fontWeight","500"),e(C,"color","#374151"),e(v,"color","#6b7280"),e(v,"fontFamily","monospace"),d(v,()=>t()?.id),e(J,"fontWeight","500"),e(J,"color","#374151"),e(O,"color","#6b7280"),d(O,()=>t()?.email),e(D,"fontWeight","500"),e(D,"color","#374151"),e(z,"color","#6b7280"),d(z,()=>t()?.phone),e(c,"fontWeight","500"),e(c,"color","#374151"),e(i,"color","white"),e(i,"padding","2px 8px"),e(i,"borderRadius","12px"),e(i,"fontSize","12px"),e(i,"fontWeight","500"),d(i,(()=>{var u=A(()=>t()?.role==="admin");return()=>u()?"管理员":t()?.role==="trader"?"交易员":"普通用户"})()),e(r,"fontWeight","500"),e(r,"color","#374151"),e(T,"fontWeight","500"),d(T,()=>t()?.status==="active"?"✅ 活跃":"❌ 禁用"),e(F,"fontWeight","500"),e(F,"color","#374151"),e(N,"color","#6b7280"),d(N,()=>new Date(t()?.createdAt||"").toLocaleDateString("zh-CN")),e(U,"fontWeight","500"),e(U,"color","#374151"),e(ie,"color","#6b7280"),d(ie,()=>new Date(t()?.lastLoginAt||"").toLocaleString("zh-CN")),e(B,"marginTop","24px"),e(B,"paddingTop","20px"),e(B,"borderTop","1px solid #e5e7eb"),e(P,"fontSize","16px"),e(P,"fontWeight","600"),e(P,"color","#111827"),e(P,"marginBottom","12px"),e(I,"display","grid"),e(I,"gridTemplateColumns","auto 1fr"),e(I,"gap","12px"),e(I,"fontSize","14px"),e(q,"fontWeight","500"),e(q,"color","#374151"),e(l,"color","#6b7280"),d(l,()=>t()?.preferences?.theme==="light"?"🌞 浅色":"🌙 深色"),e(g,"fontWeight","500"),e(g,"color","#374151"),e(a,"color","#6b7280"),d(a,()=>t()?.preferences?.language==="zh-CN"?"🇨🇳 中文":"🇺🇸 English"),e(s,"fontWeight","500"),e(s,"color","#374151"),e(E,"color","#6b7280"),d(E,()=>t()?.preferences?.timezone),e(o,"marginTop","20px"),e(o,"paddingTop","20px"),e(o,"borderTop","1px solid #e5e7eb"),e(m,"fontSize","16px"),e(m,"fontWeight","600"),e(m,"color","#111827"),e(m,"marginBottom","12px"),e(p,"display","grid"),e(p,"gridTemplateColumns","auto 1fr"),e(p,"gap","12px"),e(p,"fontSize","14px"),e(M,"fontWeight","500"),e(M,"color","#374151"),e(X,"color","#6b7280"),d(X,()=>t()?.profile?.realName),e(f,"fontWeight","500"),e(f,"color","#374151"),e(w,"color","#6b7280"),d(w,()=>t()?.profile?.gender==="male"?"👨 男":"👩 女"),e(n,"fontWeight","500"),e(n,"color","#374151"),e(h,"color","#6b7280"),d(h,(()=>{var u=A(()=>t()?.profile?.investmentExperience==="beginner");return()=>u()?"🔰 新手":t()?.profile?.investmentExperience==="intermediate"?"📈 中级":"🎯 专家"})()),e(H,"fontWeight","500"),e(H,"color","#374151"),e(Y,"color","#6b7280"),d(Y,(()=>{var u=A(()=>t()?.profile?.riskTolerance==="conservative");return()=>u()?"🛡️ 保守":t()?.profile?.riskTolerance==="moderate"?"⚖️ 稳健":"🚀 激进"})()),e(j,"marginTop","24px"),e(j,"display","flex"),e(j,"gap","12px"),e(j,"justifyContent","flex-end"),y.$$click=()=>{alert("编辑个人资料功能开发中...")},e(y,"padding","8px 16px"),e(y,"backgroundColor","#f3f4f6"),e(y,"color","#374151"),e(y,"border","none"),e(y,"borderRadius","6px"),e(y,"cursor","pointer"),e(y,"fontSize","14px"),e(y,"fontWeight","500"),W.$$click=async()=>{confirm("确定要退出登录吗？")&&(await le.logout(),window.location.href="/login")},e(W,"padding","8px 16px"),e(W,"backgroundColor","#ef4444"),e(W,"color","white"),e(W,"border","none"),e(W,"borderRadius","6px"),e(W,"cursor","pointer"),e(W,"fontSize","14px"),e(W,"fontWeight","500"),ae(u=>{var Z=t()?.avatar,ee=t()?.role==="admin"?"#ef4444":t()?.role==="trader"?"#3b82f6":"#10b981",te=t()?.status==="active"?"#10b981":"#ef4444";return Z!==u.e&&ne($,"src",u.e=Z),ee!==u.t&&e(i,"backgroundColor",u.t=ee),te!==u.a&&e(T,"color",u.a=te),u},{e:void 0,t:void 0,a:void 0}),S}})}de(["click"]);var ve=Q("<div><p>⚠️ 您尚未登录，请先 <a href=/login>登录</a> 以查看完整功能。"),me=Q("<div><h1>量化交易仪表盘</h1><p>欢迎使用量化交易平台！</p><p>当前时间: </p><div><h2>🎉 路由工作正常！</h2><p>这是Dashboard页面</p><p>SolidJS Router 已经正确配置</p></div><div><h2>滑动验证组件演示</h2><p>参考开源项目实现的滑动验证组件：</p><div><h3>Canvas版本（复杂实现）</h3></div><div><h3>简化版本（推荐使用）"),Se=Q("<div>");function Ce(){console.log("Dashboard component is rendering...");const[t,k]=G(""),S=()=>le.state.isAuthenticated,x=()=>{k("✅ 滑动验证成功！"),console.log("滑动验证成功")},_=()=>{k("❌ 滑动验证失败，请重试"),console.log("滑动验证失败")},b=()=>{k("🔄 验证码已刷新"),console.log("验证码已刷新")};return(()=>{var $=me(),V=$.firstChild,R=V.nextSibling,L=R.nextSibling;L.firstChild;var C=L.nextSibling,v=C.nextSibling,J=v.firstChild,O=J.nextSibling,D=O.nextSibling;D.firstChild;var z=D.nextSibling;return z.firstChild,d(L,()=>new Date().toLocaleString("zh-CN"),null),d($,K(re,{get when(){return S()},get children(){return K(be,{})}}),C),d($,K(re,{get when(){return!S()},get children(){var c=ve(),i=c.firstChild,r=i.firstChild,T=r.nextSibling;return e(c,"background","#fef3c7"),e(c,"border","1px solid #f59e0b"),e(c,"borderRadius","8px"),e(c,"padding","16px"),e(c,"margin","20px 0"),e(i,"margin","0"),e(i,"color","#92400e"),e(T,"color","#d97706"),e(T,"textDecoration","underline"),c}}),C),e(C,"padding","20px"),e(C,"background","#f0f0f0"),e(C,"margin","20px 0"),e(C,"borderRadius","8px"),e(v,"padding","20px"),e(v,"background","white"),e(v,"margin","20px 0"),e(v,"borderRadius","8px"),e(v,"boxShadow","0 2px 8px rgba(0,0,0,0.1)"),e(D,"margin","20px 0"),d(D,K(ue,{width:320,height:160,sliderText:"向右滑动完成验证",onSuccess:x,onFail:_,onRefresh:b}),null),e(z,"margin","20px 0"),d(z,K(ge,{width:320,height:160,sliderText:"向右滑动完成验证",onSuccess:x,onFail:_,onRefresh:b}),null),d(v,(()=>{var c=A(()=>!!t());return()=>c()&&(()=>{var i=Se();return e(i,"padding","10px"),e(i,"marginTop","10px"),e(i,"borderRadius","4px"),d(i,t),ae(r=>{var T=t().includes("成功")?"#f6ffed":t().includes("失败")?"#fff2f0":"#f0f9ff",F=`1px solid ${t().includes("成功")?"#b7eb8f":t().includes("失败")?"#ffccc7":"#91d5ff"}`,N=t().includes("成功")?"#52c41a":t().includes("失败")?"#ff4d4f":"#1890ff";return T!==r.e&&e(i,"backgroundColor",r.e=T),F!==r.t&&e(i,"border",r.t=F),N!==r.a&&e(i,"color",r.a=N),r},{e:void 0,t:void 0,a:void 0}),i})()})(),null),$})()}export{Ce as default};
