// 回测计算Worker - 在后台线程执行回测计算以避免阻塞UI

interface BacktestWorkerMessage {
  type: 'run-backtest' | 'cancel-backtest';
  data: any;
  id: string;
}

interface BacktestWorkerResponse {
  type: 'backtest-progress' | 'backtest-complete' | 'backtest-error' | 'backtest-cancelled';
  data: any;
  id: string;
}

interface BacktestConfig {
  strategy: string;
  startDate: string;
  endDate: string;
  initialCapital: number;
  commission: number;
  slippage: number;
  marketData: any[];
}

interface BacktestResult {
  totalReturn: number;
  annualizedReturn: number;
  maxDrawdown: number;
  sharpeRatio: number;
  winRate: number;
  profitFactor: number;
  totalTrades: number;
  trades: Trade[];
  equity: EquityPoint[];
  metrics: PerformanceMetrics;
}

interface Trade {
  id: string;
  symbol: string;
  side: 'buy' | 'sell';
  quantity: number;
  price: number;
  timestamp: number;
  pnl: number;
}

interface EquityPoint {
  timestamp: number;
  value: number;
}

interface PerformanceMetrics {
  totalReturn: number;
  annualizedReturn: number;
  volatility: number;
  maxDrawdown: number;
  maxDrawdownDuration: number;
  sharpeRatio: number;
  sortinoRatio: number;
  calmarRatio: number;
  winRate: number;
  profitFactor: number;
  averageWin: number;
  averageLoss: number;
  largestWin: number;
  largestLoss: number;
  consecutiveWins: number;
  consecutiveLosses: number;
}

let isRunning = false;
let shouldCancel = false;

// 监听主线程消息
self.addEventListener('message', async (event: MessageEvent<BacktestWorkerMessage>) => {
  const { type, data, id } = event.data;
  
  try {
    switch (type) {
      case 'run-backtest':
        await runBacktest(data, id);
        break;
        
      case 'cancel-backtest':
        shouldCancel = true;
        postMessage({
          type: 'backtest-cancelled',
          data: { message: '回测已取消' },
          id
        } as BacktestWorkerResponse);
        break;
        
      default:
        throw new Error(`Unknown message type: ${type}`);
    }
  } catch (error) {
    postMessage({
      type: 'backtest-error',
      data: { error: error instanceof Error ? error.message : 'Unknown error' },
      id
    } as BacktestWorkerResponse);
  }
});

// 运行回测
async function runBacktest(config: BacktestConfig, id: string) {
  if (isRunning) {
    throw new Error('回测正在运行中');
  }
  
  isRunning = true;
  shouldCancel = false;
  
  try {
    const result = await executeBacktest(config, id);
    
    if (!shouldCancel) {
      postMessage({
        type: 'backtest-complete',
        data: result,
        id
      } as BacktestWorkerResponse);
    }
  } finally {
    isRunning = false;
    shouldCancel = false;
  }
}

// 执行回测计算
async function executeBacktest(config: BacktestConfig, id: string): Promise<BacktestResult> {
  const {
    strategy,
    startDate,
    endDate,
    initialCapital,
    commission = 0.001,
    slippage = 0.0005,
    marketData
  } = config;
  
  // 初始化回测状态
  let portfolio = {
    cash: initialCapital,
    positions: new Map<string, number>(),
    value: initialCapital
  };
  
  const trades: Trade[] = [];
  const equity: EquityPoint[] = [];
  let tradeId = 1;
  
  // 模拟策略执行
  const totalSteps = marketData.length;
  
  for (let i = 0; i < totalSteps; i++) {
    if (shouldCancel) {
      throw new Error('回测被取消');
    }
    
    const currentData = marketData[i];
    const timestamp = new Date(currentData.timestamp).getTime();
    
    // 发送进度更新
    if (i % Math.max(1, Math.floor(totalSteps / 100)) === 0) {
      postMessage({
        type: 'backtest-progress',
        data: {
          progress: (i / totalSteps) * 100,
          currentDate: currentData.timestamp,
          portfolioValue: portfolio.value
        },
        id
      } as BacktestWorkerResponse);
    }
    
    // 执行策略逻辑（简化版）
    const signals = executeStrategy(strategy, currentData, i, marketData);
    
    // 处理交易信号
    for (const signal of signals) {
      const trade = executeOrder(signal, currentData, portfolio, commission, slippage);
      if (trade) {
        trade.id = `trade_${tradeId++}`;
        trades.push(trade);
      }
    }
    
    // 更新投资组合价值
    portfolio.value = calculatePortfolioValue(portfolio, currentData);
    
    // 记录权益曲线
    equity.push({
      timestamp,
      value: portfolio.value
    });
    
    // 模拟计算延迟
    if (i % 1000 === 0) {
      await new Promise(resolve => setTimeout(resolve, 1));
    }
  }
  
  // 计算性能指标
  const metrics = calculatePerformanceMetrics(equity, trades, initialCapital);
  
  return {
    totalReturn: ((portfolio.value - initialCapital) / initialCapital) * 100,
    annualizedReturn: metrics.annualizedReturn,
    maxDrawdown: metrics.maxDrawdown,
    sharpeRatio: metrics.sharpeRatio,
    winRate: metrics.winRate,
    profitFactor: metrics.profitFactor,
    totalTrades: trades.length,
    trades,
    equity,
    metrics
  };
}

// 执行策略逻辑（简化版）
function executeStrategy(strategy: string, currentData: any, index: number, allData: any[]): any[] {
  const signals: any[] = [];
  
  // 这里应该解析和执行实际的策略代码
  // 为了演示，我们使用简单的双均线策略
  if (index >= 20) {
    const shortMA = calculateSMA(allData.slice(index - 4, index + 1).map(d => d.close));
    const longMA = calculateSMA(allData.slice(index - 19, index + 1).map(d => d.close));
    
    if (shortMA > longMA) {
      signals.push({
        action: 'buy',
        symbol: currentData.symbol || 'AAPL',
        quantity: 100,
        price: currentData.close
      });
    } else if (shortMA < longMA) {
      signals.push({
        action: 'sell',
        symbol: currentData.symbol || 'AAPL',
        quantity: 100,
        price: currentData.close
      });
    }
  }
  
  return signals;
}

// 执行订单
function executeOrder(
  signal: any,
  marketData: any,
  portfolio: any,
  commission: number,
  slippage: number
): Trade | null {
  const { action, symbol, quantity, price } = signal;
  
  // 计算实际执行价格（考虑滑点）
  const slippageAmount = price * slippage;
  const executionPrice = action === 'buy' 
    ? price + slippageAmount 
    : price - slippageAmount;
  
  // 计算手续费
  const commissionCost = executionPrice * quantity * commission;
  
  if (action === 'buy') {
    const totalCost = executionPrice * quantity + commissionCost;
    
    if (portfolio.cash >= totalCost) {
      portfolio.cash -= totalCost;
      portfolio.positions.set(symbol, (portfolio.positions.get(symbol) || 0) + quantity);
      
      return {
        id: '',
        symbol,
        side: 'buy',
        quantity,
        price: executionPrice,
        timestamp: marketData.timestamp,
        pnl: -commissionCost
      };
    }
  } else if (action === 'sell') {
    const currentPosition = portfolio.positions.get(symbol) || 0;
    const sellQuantity = Math.min(quantity, currentPosition);
    
    if (sellQuantity > 0) {
      const totalRevenue = executionPrice * sellQuantity - commissionCost;
      portfolio.cash += totalRevenue;
      portfolio.positions.set(symbol, currentPosition - sellQuantity);
      
      return {
        id: '',
        symbol,
        side: 'sell',
        quantity: sellQuantity,
        price: executionPrice,
        timestamp: marketData.timestamp,
        pnl: totalRevenue - (executionPrice * sellQuantity) // 简化的PnL计算
      };
    }
  }
  
  return null;
}

// 计算投资组合价值
function calculatePortfolioValue(portfolio: any, marketData: any): number {
  let totalValue = portfolio.cash;
  
  for (const [symbol, quantity] of portfolio.positions) {
    if (quantity > 0) {
      // 简化：假设所有持仓都是当前数据的symbol
      totalValue += quantity * marketData.close;
    }
  }
  
  return totalValue;
}

// 计算简单移动平均
function calculateSMA(prices: number[]): number {
  return prices.reduce((sum, price) => sum + price, 0) / prices.length;
}

// 计算性能指标
function calculatePerformanceMetrics(
  equity: EquityPoint[],
  trades: Trade[],
  initialCapital: number
): PerformanceMetrics {
  if (equity.length === 0) {
    return createEmptyMetrics();
  }
  
  const returns = equity.slice(1).map((point, i) => 
    (point.value - equity[i].value) / equity[i].value
  );
  
  const finalValue = equity[equity.length - 1].value;
  const totalReturn = ((finalValue - initialCapital) / initialCapital) * 100;
  
  // 计算年化收益率
  const daysDiff = (equity[equity.length - 1].timestamp - equity[0].timestamp) / (1000 * 60 * 60 * 24);
  const years = daysDiff / 365.25;
  const annualizedReturn = years > 0 ? (Math.pow(finalValue / initialCapital, 1 / years) - 1) * 100 : 0;
  
  // 计算波动率
  const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
  const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
  const volatility = Math.sqrt(variance) * Math.sqrt(252) * 100; // 年化波动率
  
  // 计算最大回撤
  let maxDrawdown = 0;
  let peak = equity[0].value;
  
  for (const point of equity) {
    if (point.value > peak) {
      peak = point.value;
    }
    const drawdown = (peak - point.value) / peak;
    maxDrawdown = Math.max(maxDrawdown, drawdown);
  }
  
  // 计算夏普比率
  const riskFreeRate = 0.02; // 假设无风险利率为2%
  const excessReturn = (annualizedReturn / 100) - riskFreeRate;
  const sharpeRatio = volatility > 0 ? excessReturn / (volatility / 100) : 0;
  
  // 计算交易统计
  const winningTrades = trades.filter(t => t.pnl > 0);
  const losingTrades = trades.filter(t => t.pnl < 0);
  const winRate = trades.length > 0 ? (winningTrades.length / trades.length) * 100 : 0;
  
  const totalWins = winningTrades.reduce((sum, t) => sum + t.pnl, 0);
  const totalLosses = Math.abs(losingTrades.reduce((sum, t) => sum + t.pnl, 0));
  const profitFactor = totalLosses > 0 ? totalWins / totalLosses : 0;
  
  return {
    totalReturn,
    annualizedReturn,
    volatility,
    maxDrawdown: maxDrawdown * 100,
    maxDrawdownDuration: 0, // 简化
    sharpeRatio,
    sortinoRatio: 0, // 简化
    calmarRatio: maxDrawdown > 0 ? annualizedReturn / (maxDrawdown * 100) : 0,
    winRate,
    profitFactor,
    averageWin: winningTrades.length > 0 ? totalWins / winningTrades.length : 0,
    averageLoss: losingTrades.length > 0 ? totalLosses / losingTrades.length : 0,
    largestWin: winningTrades.length > 0 ? Math.max(...winningTrades.map(t => t.pnl)) : 0,
    largestLoss: losingTrades.length > 0 ? Math.min(...losingTrades.map(t => t.pnl)) : 0,
    consecutiveWins: 0, // 简化
    consecutiveLosses: 0 // 简化
  };
}

function createEmptyMetrics(): PerformanceMetrics {
  return {
    totalReturn: 0,
    annualizedReturn: 0,
    volatility: 0,
    maxDrawdown: 0,
    maxDrawdownDuration: 0,
    sharpeRatio: 0,
    sortinoRatio: 0,
    calmarRatio: 0,
    winRate: 0,
    profitFactor: 0,
    averageWin: 0,
    averageLoss: 0,
    largestWin: 0,
    largestLoss: 0,
    consecutiveWins: 0,
    consecutiveLosses: 0
  };
}
