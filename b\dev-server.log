
> quant-frontend@1.0.0 dev
> panda codegen && vite

✔️ `styled-system/css`: the css function to author styles
✔️ `styled-system/tokens`: the css variables and js function to query your tokens
✔️ `styled-system/patterns`: functions to implement and apply common layout patterns


Port 3000 is in use, trying another one...
Port 3001 is in use, trying another one...
Port 3002 is in use, trying another one...

  [32m[1mVITE[22m v5.4.19[39m  [2mready in [0m[1m861[22m[2m[0m ms[22m

  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m3003[22m/[39m
  [32m➜[39m  [1mNetwork[22m: [36mhttp://**********:[1m3003[22m/[39m
  [32m➜[39m  [1mNetwork[22m: [36mhttp://*************:[1m3003[22m/[39m
