
> quant-frontend@1.0.0 dev
> panda codegen && vite

✔️ `styled-system/css`: the css function to author styles
✔️ `styled-system/tokens`: the css variables and js function to query your tokens
✔️ `styled-system/patterns`: functions to implement and apply common layout patterns


Port 3000 is in use, trying another one...
Port 3001 is in use, trying another one...
Port 3002 is in use, trying another one...

  [32m[1mVITE[22m v5.4.19[39m  [2mready in [0m[1m861[22m[2m[0m ms[22m

  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m3003[22m/[39m
  [32m➜[39m  [1mNetwork[22m: [36mhttp://**********:[1m3003[22m/[39m
  [32m➜[39m  [1mNetwork[22m: [36mhttp://*************:[1m3003[22m/[39m
[2m11:57:36[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "./pages/EnhancedStrategyEditor" from "src/EnhancedApp.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Desktop/frontend/b/src/EnhancedApp.tsx[39m:17:49
[33m  203|  const Dashboard = lazy(() => import("./pages/Dashboard"));
  204|  const StrategyEditor = lazy(() => import("./pages/StrategyEditor"));
  205|  const EnhancedStrategyEditor = lazy(() => import("./pages/EnhancedStrategyEditor"));
     |                                                   ^
  206|  const BacktestAnalysis = lazy(() => import("./pages/BacktestAnalysis"));
  207|  const MarketData = lazy(() => import("./pages/MarketData"));[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64291:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64423:39
      at async Promise.all (index 19)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64350:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:51977:27)
[2m11:57:36[22m [31m[1m[vite][22m[39m Pre-transform error: Failed to resolve import "./pages/EnhancedStrategyEditor" from "src/EnhancedApp.tsx". Does the file exist?
[2m11:57:51[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2msrc/workers/backtest.worker.ts[22m
[2m11:57:52[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "./pages/EnhancedStrategyEditor" from "src/EnhancedApp.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Desktop/frontend/b/src/EnhancedApp.tsx[39m:17:49
[33m  203|  const Dashboard = lazy(() => import("./pages/Dashboard"));
  204|  const StrategyEditor = lazy(() => import("./pages/StrategyEditor"));
  205|  const EnhancedStrategyEditor = lazy(() => import("./pages/EnhancedStrategyEditor"));
     |                                                   ^
  206|  const BacktestAnalysis = lazy(() => import("./pages/BacktestAnalysis"));
  207|  const MarketData = lazy(() => import("./pages/MarketData"));[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64291:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64423:39
      at async Promise.all (index 19)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64350:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:51977:27)
[2m11:57:52[22m [31m[1m[vite][22m[39m Pre-transform error: Failed to resolve import "./pages/EnhancedStrategyEditor" from "src/EnhancedApp.tsx". Does the file exist?
[2m11:58:09[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Header.tsx, /src/components/RouteGuard.tsx[22m
[2m11:58:10[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "./pages/EnhancedStrategyEditor" from "src/EnhancedApp.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Desktop/frontend/b/src/EnhancedApp.tsx[39m:17:49
[33m  203|  const Dashboard = lazy(() => import("./pages/Dashboard"));
  204|  const StrategyEditor = lazy(() => import("./pages/StrategyEditor"));
  205|  const EnhancedStrategyEditor = lazy(() => import("./pages/EnhancedStrategyEditor"));
     |                                                   ^
  206|  const BacktestAnalysis = lazy(() => import("./pages/BacktestAnalysis"));
  207|  const MarketData = lazy(() => import("./pages/MarketData"));[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64291:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64423:39
      at async Promise.all (index 19)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64350:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:51977:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Desktop/frontend/b/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:62105:24)
[2m11:58:46[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2msrc/pages/EnhancedStrategyEditor.tsx[22m
