import { createSignal, createEffect, onMount, onCleanup } from 'solid-js';

interface SlideVerifyProps {
  width?: number;
  height?: number;
  sliderText?: string;
  onSuccess?: () => void;
  onFail?: () => void;
  onRefresh?: () => void;
}

export default function SlideVerify(props: SlideVerifyProps) {
  const [isLoading, setIsLoading] = createSignal(false);
  const [isSuccess, setIsSuccess] = createSignal(false);
  const [isFailed, setIsFailed] = createSignal(false);
  const [sliderLeft, setSliderLeft] = createSignal(0);
  const [isDragging, setIsDragging] = createSignal(false);
  const [startX, setStartX] = createSignal(0);
  const [puzzleX, setPuzzleX] = createSignal(0);
  
  let canvasRef: HTMLCanvasElement | undefined;
  let blockCanvasRef: HTMLCanvasElement | undefined;
  let sliderRef: HTMLDivElement | undefined;
  let containerRef: HTMLDivElement | undefined;

  const width = () => props.width || 320;
  const height = () => props.height || 160;
  const sliderText = () => props.sliderText || '向右滑动验证';

  // 生成随机拼图位置
  const generatePuzzle = () => {
    const x = Math.random() * (width() - 100) + 50;
    setPuzzleX(x);
    return x;
  };

  // 绘制背景图和拼图
  const drawPuzzle = () => {
    if (!canvasRef || !blockCanvasRef) return;

    const canvas = canvasRef;
    const blockCanvas = blockCanvasRef;
    const ctx = canvas.getContext('2d');
    const blockCtx = blockCanvas.getContext('2d');

    if (!ctx || !blockCtx) return;

    // 清空画布
    ctx.clearRect(0, 0, width(), height());
    blockCtx.clearRect(0, 0, width(), height());

    // 绘制背景渐变
    const gradient = ctx.createLinearGradient(0, 0, width(), height());
    gradient.addColorStop(0, '#74b9ff');
    gradient.addColorStop(1, '#0984e3');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width(), height());

    // 添加一些装饰点
    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
    for (let i = 0; i < 20; i++) {
      const x = Math.random() * width();
      const y = Math.random() * height();
      const radius = Math.random() * 3 + 1;
      ctx.beginPath();
      ctx.arc(x, y, radius, 0, Math.PI * 2);
      ctx.fill();
    }

    // 拼图参数
    const puzzleSize = 42;
    const x = puzzleX();
    const y = height() / 2 - puzzleSize / 2;

    // 绘制拼图形状路径
    const drawPuzzlePath = (context: CanvasRenderingContext2D, x: number, y: number) => {
      context.beginPath();
      context.moveTo(x, y);
      context.lineTo(x + puzzleSize, y);
      context.lineTo(x + puzzleSize, y + puzzleSize);
      context.lineTo(x, y + puzzleSize);
      context.closePath();
    };

    // 在主画布上绘制拼图缺口
    ctx.save();
    drawPuzzlePath(ctx, x, y);
    ctx.globalCompositeOperation = 'destination-out';
    ctx.fill();
    ctx.restore();

    // 在拼图画布上绘制拼图块
    blockCtx.save();
    drawPuzzlePath(blockCtx, x, y);
    blockCtx.clip();
    
    // 复制背景到拼图块
    const gradient2 = blockCtx.createLinearGradient(0, 0, width(), height());
    gradient2.addColorStop(0, '#74b9ff');
    gradient2.addColorStop(1, '#0984e3');
    blockCtx.fillStyle = gradient2;
    blockCtx.fillRect(0, 0, width(), height());
    
    blockCtx.restore();

    // 给拼图块添加边框
    blockCtx.strokeStyle = '#fff';
    blockCtx.lineWidth = 2;
    drawPuzzlePath(blockCtx, x, y);
    blockCtx.stroke();
  };

  // 重置验证
  const reset = () => {
    setIsSuccess(false);
    setIsFailed(false);
    setSliderLeft(0);
    setIsDragging(false);
    generatePuzzle();
    drawPuzzle();
  };

  // 验证结果
  const verify = () => {
    const tolerance = 5; // 允许的误差范围
    const isValid = Math.abs(sliderLeft() - puzzleX()) < tolerance;
    
    if (isValid) {
      setIsSuccess(true);
      setIsFailed(false);
      props.onSuccess?.();
    } else {
      setIsFailed(true);
      setIsSuccess(false);
      props.onFail?.();
      // 1秒后重置
      setTimeout(reset, 1000);
    }
  };

  // 鼠标/触摸事件处理
  const handleStart = (e: MouseEvent | TouchEvent) => {
    if (isSuccess()) return;
    
    setIsDragging(true);
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    setStartX(clientX - sliderLeft());
  };

  const handleMove = (e: MouseEvent | TouchEvent) => {
    if (!isDragging() || isSuccess()) return;
    
    e.preventDefault();
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const newLeft = Math.max(0, Math.min(clientX - startX(), width() - 60));
    setSliderLeft(newLeft);
  };

  const handleEnd = () => {
    if (!isDragging() || isSuccess()) return;
    
    setIsDragging(false);
    verify();
  };

  // 组件挂载时初始化
  onMount(() => {
    generatePuzzle();
    drawPuzzle();

    // 添加全局事件监听
    document.addEventListener('mousemove', handleMove);
    document.addEventListener('mouseup', handleEnd);
    document.addEventListener('touchmove', handleMove, { passive: false });
    document.addEventListener('touchend', handleEnd);
  });

  // 组件卸载时清理事件
  onCleanup(() => {
    document.removeEventListener('mousemove', handleMove);
    document.removeEventListener('mouseup', handleEnd);
    document.removeEventListener('touchmove', handleMove);
    document.removeEventListener('touchend', handleEnd);
  });

  return (
    <div 
      ref={containerRef}
      style={{
        width: `${width()}px`,
        border: '1px solid #e1e5e9',
        'border-radius': '4px',
        'background-color': '#f7f9fa',
        position: 'relative',
        'user-select': 'none'
      }}
    >
      {/* 画布容器 */}
      <div style={{ position: 'relative', overflow: 'hidden' }}>
        {/* 背景画布 */}
        <canvas
          ref={canvasRef}
          width={width()}
          height={height()}
          style={{
            display: 'block',
            'border-radius': '4px 4px 0 0'
          }}
        />
        
        {/* 拼图块画布 */}
        <canvas
          ref={blockCanvasRef}
          width={width()}
          height={height()}
          style={{
            position: 'absolute',
            top: '0',
            left: `${sliderLeft()}px`,
            display: 'block',
            'border-radius': '4px 4px 0 0',
            transition: isDragging() ? 'none' : 'left 0.3s ease'
          }}
        />
        
        {/* 加载遮罩 */}
        {isLoading() && (
          <div style={{
            position: 'absolute',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            'background-color': 'rgba(255, 255, 255, 0.8)',
            display: 'flex',
            'align-items': 'center',
            'justify-content': 'center'
          }}>
            <div>加载中...</div>
          </div>
        )}
      </div>

      {/* 滑块轨道 */}
      <div style={{
        height: '40px',
        'background-color': '#f7f9fa',
        'border-top': '1px solid #e1e5e9',
        position: 'relative',
        display: 'flex',
        'align-items': 'center'
      }}>
        {/* 滑块 */}
        <div
          ref={sliderRef}
          style={{
            position: 'absolute',
            left: `${sliderLeft()}px`,
            width: '60px',
            height: '38px',
            'background-color': isSuccess() ? '#52c41a' : isFailed() ? '#ff4d4f' : '#1890ff',
            border: '1px solid #d9d9d9',
            'border-radius': '4px',
            cursor: isDragging() ? 'grabbing' : 'grab',
            display: 'flex',
            'align-items': 'center',
            'justify-content': 'center',
            color: 'white',
            'font-size': '18px',
            transition: isDragging() ? 'none' : 'all 0.3s ease',
            'box-shadow': '0 2px 4px rgba(0,0,0,0.1)'
          }}
          onMouseDown={handleStart}
          onTouchStart={handleStart}
        >
          {isSuccess() ? '✓' : isFailed() ? '✗' : '→'}
        </div>

        {/* 滑块文本 */}
        <div style={{
          width: '100%',
          'text-align': 'center',
          color: '#999',
          'font-size': '14px',
          'pointer-events': 'none'
        }}>
          {isSuccess() ? '验证成功' : isFailed() ? '验证失败，请重试' : sliderText()}
        </div>

        {/* 刷新按钮 */}
        <button
          style={{
            position: 'absolute',
            right: '10px',
            width: '24px',
            height: '24px',
            border: 'none',
            'background-color': 'transparent',
            cursor: 'pointer',
            'font-size': '16px',
            color: '#999'
          }}
          onClick={() => {
            reset();
            props.onRefresh?.();
          }}
          title="刷新验证码"
        >
          ↻
        </button>
      </div>
    </div>
  );
}
