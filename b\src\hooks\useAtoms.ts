/**
 * SolidJS 状态管理 hooks
 * 提供与现有 stores 系统兼容的状态管理
 */

import { createSignal, createEffect, onCleanup } from 'solid-js';

// 简化的状态管理 hooks，与现有 stores 系统兼容

// 创建简单的状态 hook
export function createAtomSignal<T>(initialValue: T) {
  const [value, setValue] = createSignal(initialValue);

  return {
    useValue: () => value,
    useSet: () => setValue,
    use: () => [value, setValue] as const
  };
}

// 创建计算状态 hook
export function createComputedAtom<T>(compute: () => T) {
  const [value, setValue] = createSignal(compute());

  // 自动更新计算值
  createEffect(() => {
    setValue(() => compute());
  });

  return {
    useValue: () => value
  };
}

// 创建异步状态 hook
export function createAsyncAtom<T>(
  asyncFn: () => Promise<T>,
  initialValue?: T
) {
  const [value, setValue] = createSignal(initialValue as T);
  const [loading, setLoading] = createSignal(false);
  const [error, setError] = createSignal<Error | null>(null);

  const load = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await asyncFn();
      setValue(() => result);
      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    useValue: () => value,
    useLoading: () => loading,
    useError: () => error,
    load
  };
}

// 导出便捷函数
export { createAtomSignal, createComputedAtom, createAsyncAtom };
