{"$schema": "https://biomejs.dev/schemas/1.7.0/schema.json", "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off", "useImportType": "error"}, "suspicious": {"noExplicitAny": "warn"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf"}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "double", "trailingComma": "es5", "semicolons": "always"}}, "files": {"include": ["src/**/*"], "ignore": ["node_modules", "dist", "styled-system"]}}