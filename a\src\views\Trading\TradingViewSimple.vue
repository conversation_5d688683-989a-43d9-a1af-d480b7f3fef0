<template>
  <div class="trading-view-simple">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>💰 智能交易</h1>
      <p>专业交易工具与风险控制</p>
    </div>

    <el-row :gutter="20">
      <!-- 交易面板 -->
      <el-col :xs="24" :lg="16">
        <el-card>
          <template #header>
            <h2>交易下单</h2>
          </template>
          
          <el-form :model="orderForm" :rules="orderRules" ref="orderFormRef" label-width="80px">
            <el-form-item label="股票代码" prop="symbol">
              <el-input 
                v-model="orderForm.symbol" 
                placeholder="请输入6位股票代码"
                @blur="fetchStockInfo"
              >
                <template #append>
                  <el-button @click="openStockSelector">选择</el-button>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item label="股票名称">
              <el-input v-model="stockInfo.name" readonly />
            </el-form-item>

            <el-form-item label="当前价格">
              <el-input v-model="stockInfo.currentPrice" readonly>
                <template #append>元</template>
              </el-input>
            </el-form-item>

            <el-form-item label="交易方向" prop="side">
              <el-radio-group v-model="orderForm.side">
                <el-radio value="buy" size="large">
                  <span style="color: #f56c6c;">买入</span>
                </el-radio>
                <el-radio value="sell" size="large">
                  <span style="color: #67c23a;">卖出</span>
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="订单类型" prop="orderType">
              <el-select v-model="orderForm.orderType" style="width: 100%">
                <el-option label="市价单" value="market" />
                <el-option label="限价单" value="limit" />
              </el-select>
            </el-form-item>

            <el-form-item v-if="orderForm.orderType === 'limit'" label="委托价格" prop="price">
              <el-input-number
                v-model="orderForm.price"
                :precision="2"
                :step="0.01"
                :min="0.01"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="委托数量" prop="quantity">
              <el-input-number
                v-model="orderForm.quantity"
                :precision="0"
                :step="100"
                :min="100"
                style="width: 100%"
              />
              <div class="quantity-helpers">
                <el-button size="small" @click="setQuantity(100)">100股</el-button>
                <el-button size="small" @click="setQuantity(500)">500股</el-button>
                <el-button size="small" @click="setQuantity(1000)">1000股</el-button>
                <el-button size="small" @click="calculateMaxQuantity">最大</el-button>
              </div>
            </el-form-item>

            <el-form-item label="预估金额">
              <el-input v-model="estimatedAmount" readonly>
                <template #append>元</template>
              </el-input>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" size="large" @click="submitOrder" :loading="submitting">
                {{ orderForm.side === 'buy' ? '买入' : '卖出' }}
              </el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 账户信息和持仓 -->
      <el-col :xs="24" :lg="8">
        <!-- 账户资金 -->
        <el-card style="margin-bottom: 20px;">
          <template #header>
            <h3>账户资金</h3>
          </template>
          <div class="account-info">
            <div class="info-item">
              <span class="label">总资产:</span>
              <span class="value">¥{{ formatNumber(accountInfo.totalAssets) }}</span>
            </div>
            <div class="info-item">
              <span class="label">可用资金:</span>
              <span class="value">¥{{ formatNumber(accountInfo.availableFunds) }}</span>
            </div>
            <div class="info-item">
              <span class="label">冻结资金:</span>
              <span class="value">¥{{ formatNumber(accountInfo.frozenFunds) }}</span>
            </div>
            <div class="info-item">
              <span class="label">持仓市值:</span>
              <span class="value">¥{{ formatNumber(accountInfo.marketValue) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 当前持仓 -->
        <el-card>
          <template #header>
            <h3>当前持仓</h3>
          </template>
          <div v-if="positions.length === 0" class="empty-state">
            暂无持仓
          </div>
          <div v-else class="positions-list">
            <div v-for="position in positions" :key="position.symbol" class="position-item">
              <div class="position-header">
                <span class="stock-name">{{ position.name }}</span>
                <span class="stock-code">{{ position.symbol }}</span>
              </div>
              <div class="position-details">
                <div class="detail-row">
                  <span>持仓: {{ position.quantity }}股</span>
                  <span>现价: ¥{{ position.currentPrice.toFixed(2) }}</span>
                </div>
                <div class="detail-row">
                  <span>成本: ¥{{ position.avgPrice.toFixed(2) }}</span>
                  <span :class="['profit', position.profit >= 0 ? 'positive' : 'negative']">
                    盈亏: {{ (position.profit >= 0 ? '+' : '') }}{{ position.profit.toFixed(2) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 委托订单 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <h3>委托订单</h3>
          <el-button @click="refreshOrders" :loading="loadingOrders">刷新</el-button>
        </div>
      </template>
      
      <el-table :data="orders" v-loading="loadingOrders">
        <el-table-column prop="symbol" label="代码" width="80" />
        <el-table-column prop="side" label="方向" width="60">
          <template #default="{ row }">
            <span :style="{ color: row.side === 'buy' ? '#f56c6c' : '#67c23a' }">
              {{ row.side === 'buy' ? '买入' : '卖出' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="orderType" label="类型" width="80">
          <template #default="{ row }">
            {{ row.orderType === 'market' ? '市价' : '限价' }}
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="委托量" width="100" />
        <el-table-column prop="price" label="委托价" width="100">
          <template #default="{ row }">
            {{ row.price ? '¥' + row.price.toFixed(2) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="委托时间" width="160" />
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button 
              v-if="row.status === 'pending'"
              size="small" 
              type="danger" 
              @click="cancelOrder(row)"
            >
              撤单
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import axios from 'axios'

// 表单引用
const orderFormRef = ref<FormInstance>()

// 响应式数据
const submitting = ref(false)
const loadingOrders = ref(false)

// 订单表单
const orderForm = ref({
  symbol: '',
  side: 'buy',
  orderType: 'limit',
  quantity: 100,
  price: 0
})

// 股票信息
const stockInfo = ref({
  name: '',
  currentPrice: 0
})

// 账户信息
const accountInfo = ref({
  totalAssets: 1234567.89,
  availableFunds: 500000.00,
  frozenFunds: 50000.00,
  marketValue: 684567.89
})

// 持仓信息
const positions = ref([
  {
    symbol: '000001',
    name: '平安银行',
    quantity: 10000,
    avgPrice: 12.50,
    currentPrice: 13.19,
    profit: 6900
  },
  {
    symbol: '600036',
    name: '招商银行',
    quantity: 5000,
    avgPrice: 35.20,
    currentPrice: 36.80,
    profit: 8000
  }
])

// 订单列表
const orders = ref([
  {
    id: 'order_001',
    symbol: '000001',
    side: 'buy',
    orderType: 'limit',
    quantity: 1000,
    price: 13.00,
    status: 'pending',
    createTime: '2024-01-15 14:30:25'
  }
])

// 表单验证规则
const orderRules: FormRules = {
  symbol: [
    { required: true, message: '请输入股票代码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '股票代码格式不正确', trigger: 'blur' }
  ],
  side: [
    { required: true, message: '请选择交易方向', trigger: 'change' }
  ],
  orderType: [
    { required: true, message: '请选择订单类型', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入委托数量', trigger: 'blur' },
    { type: 'number', min: 100, message: '最小委托数量为100股', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入委托价格', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '委托价格必须大于0', trigger: 'blur' }
  ]
}

// 计算属性
const estimatedAmount = computed(() => {
  const price = orderForm.value.orderType === 'market' 
    ? stockInfo.value.currentPrice 
    : orderForm.value.price
  const amount = price * orderForm.value.quantity
  return amount ? amount.toFixed(2) : '0.00'
})

// 方法
async function fetchStockInfo() {
  if (!orderForm.value.symbol || orderForm.value.symbol.length !== 6) {
    return
  }
  
  try {
    const response = await axios.get(`/api/v1/market/detail/${orderForm.value.symbol}`)
    if (response.data.success) {
      const stock = response.data.data
      stockInfo.value = {
        name: stock.name,
        currentPrice: stock.price
      }
    }
  } catch (error) {
    // API失败时使用模拟数据
    const mockData: Record<string, any> = {
      '000001': { name: '平安银行', currentPrice: 13.19 },
      '600036': { name: '招商银行', currentPrice: 36.80 },
      '000858': { name: '五粮液', currentPrice: 178.90 },
      '600519': { name: '贵州茅台', currentPrice: 1680.50 }
    }
    
    if (mockData[orderForm.value.symbol]) {
      stockInfo.value = mockData[orderForm.value.symbol]
    } else {
      ElMessage.warning('未找到股票信息')
      stockInfo.value = { name: '', currentPrice: 0 }
    }
  }
}

function openStockSelector() {
  ElMessage.info('股票选择器功能开发中...')
}

function setQuantity(quantity: number) {
  orderForm.value.quantity = quantity
}

function calculateMaxQuantity() {
  if (orderForm.value.side === 'buy') {
    const price = orderForm.value.orderType === 'market' 
      ? stockInfo.value.currentPrice 
      : orderForm.value.price
    if (price > 0) {
      const maxQuantity = Math.floor(accountInfo.value.availableFunds / price / 100) * 100
      orderForm.value.quantity = Math.max(100, maxQuantity)
    }
  } else {
    // 卖出时计算最大可卖数量
    const position = positions.value.find(p => p.symbol === orderForm.value.symbol)
    if (position) {
      orderForm.value.quantity = position.quantity
    }
  }
}

async function submitOrder() {
  if (!orderFormRef.value) return
  
  try {
    await orderFormRef.value.validate()
    
    submitting.value = true
    
    // 提交到后端API
    try {
      const response = await axios.post('/api/v1/trading/order', {
        symbol: orderForm.value.symbol,
        side: orderForm.value.side,
        orderType: orderForm.value.orderType,
        quantity: orderForm.value.quantity,
        price: orderForm.value.orderType === 'limit' ? orderForm.value.price : null
      })
      
      if (response.data.success) {
        ElMessage.success('订单提交成功')
        resetForm()
        refreshOrders()
      } else {
        ElMessage.error(response.data.message || '订单提交失败')
      }
    } catch (error) {
      // API失败时添加到本地订单列表
      const newOrder = {
        id: `order_${Date.now()}`,
        symbol: orderForm.value.symbol,
        side: orderForm.value.side,
        orderType: orderForm.value.orderType,
        quantity: orderForm.value.quantity,
        price: orderForm.value.orderType === 'limit' ? orderForm.value.price : null,
        status: 'pending',
        createTime: new Date().toLocaleString()
      }
      
      orders.value.unshift(newOrder)
      ElMessage.success('订单提交成功（模拟）')
      resetForm()
    }
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

function resetForm() {
  if (!orderFormRef.value) return
  orderFormRef.value.resetFields()
  stockInfo.value = { name: '', currentPrice: 0 }
}

async function refreshOrders() {
  loadingOrders.value = true
  
  try {
    const response = await axios.get('/api/v1/trading/orders')
    if (response.data.success) {
      orders.value = response.data.data.orders || []
    }
  } catch (error) {
    console.log('刷新订单失败，使用本地数据')
  } finally {
    loadingOrders.value = false
  }
}

async function cancelOrder(order: any) {
  try {
    await ElMessageBox.confirm('确定要撤销这个订单吗？', '确认撤单', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    try {
      await axios.delete(`/api/v1/trading/order/${order.id}`)
      ElMessage.success('撤单成功')
    } catch (error) {
      // API失败时直接修改本地状态
      order.status = 'cancelled'
      ElMessage.success('撤单成功（模拟）')
    }
    
    refreshOrders()
    
  } catch (error) {
    console.log('用户取消撤单')
  }
}

function getStatusType(status: string) {
  switch (status) {
    case 'pending': return 'warning'
    case 'filled': return 'success'
    case 'cancelled': return 'info'
    case 'rejected': return 'danger'
    default: return ''
  }
}

function getStatusText(status: string) {
  switch (status) {
    case 'pending': return '待成交'
    case 'filled': return '已成交'
    case 'cancelled': return '已撤销'
    case 'rejected': return '已拒绝'
    default: return status
  }
}

function formatNumber(num: number) {
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(num)
}

// 监听股票代码变化
watch(() => orderForm.value.symbol, () => {
  if (orderForm.value.symbol.length === 6) {
    fetchStockInfo()
  }
})

// 监听订单类型变化
watch(() => orderForm.value.orderType, (newType) => {
  if (newType === 'market') {
    orderForm.value.price = 0
  } else if (stockInfo.value.currentPrice > 0) {
    orderForm.value.price = stockInfo.value.currentPrice
  }
})

// 生命周期
onMounted(() => {
  console.log('💰 交易页面已加载')
  refreshOrders()
})
</script>

<style scoped>
.trading-view-simple {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
}

.quantity-helpers {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.account-info {
  space-y: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.info-item .label {
  color: #666;
  font-size: 14px;
}

.info-item .value {
  font-weight: 600;
  color: #333;
}

.empty-state {
  text-align: center;
  color: #999;
  padding: 40px 0;
}

.positions-list {
  space-y: 16px;
}

.position-item {
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  background: #fafafa;
}

.position-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stock-name {
  font-weight: 600;
  color: #333;
}

.stock-code {
  color: #666;
  font-size: 14px;
}

.position-details {
  font-size: 14px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.profit.positive {
  color: #f56c6c;
}

.profit.negative {
  color: #67c23a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .trading-view-simple {
    padding: 10px;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .quantity-helpers {
    flex-wrap: wrap;
  }
}
</style>