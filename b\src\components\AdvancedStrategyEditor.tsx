import { createSignal, createEffect, onMount, onCleanup, Show, For } from 'solid-js';
import * as monaco from 'monaco-editor';
import loader from '@monaco-editor/loader';

export interface StrategyTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  code: string;
  parameters: StrategyParameter[];
}

export interface StrategyParameter {
  name: string;
  type: 'number' | 'string' | 'boolean' | 'select';
  default: any;
  description: string;
  options?: string[];
  min?: number;
  max?: number;
}

interface AdvancedStrategyEditorProps {
  initialCode?: string;
  templates?: StrategyTemplate[];
  onCodeChange?: (code: string) => void;
  onSave?: (code: string, parameters: Record<string, any>) => void;
  onRun?: (code: string, parameters: Record<string, any>) => void;
  onGenerateAI?: (prompt: string) => Promise<string>;
  theme?: 'light' | 'dark';
  height?: number;
  readOnly?: boolean;
}

export default function AdvancedStrategyEditor(props: AdvancedStrategyEditorProps) {
  let editorContainer: HTMLDivElement | undefined;
  const [editor, setEditor] = createSignal<monaco.editor.IStandaloneCodeEditor | null>(null);
  const [isLoading, setIsLoading] = createSignal(true);
  const [selectedTemplate, setSelectedTemplate] = createSignal<StrategyTemplate | null>(null);
  const [parameters, setParameters] = createSignal<Record<string, any>>({});
  const [aiPrompt, setAiPrompt] = createSignal('');
  const [isGenerating, setIsGenerating] = createSignal(false);
  const [showTemplates, setShowTemplates] = createSignal(false);
  const [showParameters, setShowParameters] = createSignal(true);
  const [errors, setErrors] = createSignal<string[]>([]);

  // 策略代码模板
  const defaultCode = `"""
量化交易策略模板
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional

class TradingStrategy:
    def __init__(self, params: Dict):
        self.params = params
        self.positions = {}
        self.cash = params.get('initial_cash', 100000)
        
    def initialize(self):
        """策略初始化"""
        print("策略初始化完成")
        
    def on_data(self, data: pd.DataFrame):
        """处理市场数据"""
        # 获取最新价格
        current_prices = data.iloc[-1]
        
        # 实现你的交易逻辑
        for symbol in data.columns:
            signal = self.generate_signal(data[symbol])
            if signal > 0:
                self.buy(symbol, current_prices[symbol])
            elif signal < 0:
                self.sell(symbol, current_prices[symbol])
                
    def generate_signal(self, price_series: pd.Series) -> float:
        """生成交易信号
        返回值: > 0 买入, < 0 卖出, = 0 持有
        """
        # 简单移动平均策略示例
        short_ma = price_series.rolling(5).mean().iloc[-1]
        long_ma = price_series.rolling(20).mean().iloc[-1]
        
        if short_ma > long_ma:
            return 1.0  # 买入信号
        elif short_ma < long_ma:
            return -1.0  # 卖出信号
        else:
            return 0.0  # 持有
            
    def buy(self, symbol: str, price: float, quantity: Optional[int] = None):
        """买入股票"""
        if quantity is None:
            # 使用固定比例资金
            quantity = int(self.cash * 0.1 / price)
            
        if quantity > 0 and self.cash >= price * quantity:
            self.positions[symbol] = self.positions.get(symbol, 0) + quantity
            self.cash -= price * quantity
            print(f"买入 {symbol}: {quantity}股, 价格: {price}")
            
    def sell(self, symbol: str, price: float, quantity: Optional[int] = None):
        """卖出股票"""
        current_pos = self.positions.get(symbol, 0)
        if current_pos <= 0:
            return
            
        if quantity is None:
            quantity = current_pos
            
        quantity = min(quantity, current_pos)
        if quantity > 0:
            self.positions[symbol] -= quantity
            self.cash += price * quantity
            print(f"卖出 {symbol}: {quantity}股, 价格: {price}")
            
    def get_portfolio_value(self, current_prices: Dict) -> float:
        """计算投资组合总价值"""
        total_value = self.cash
        for symbol, quantity in self.positions.items():
            if symbol in current_prices:
                total_value += quantity * current_prices[symbol]
        return total_value
`;

  // 初始化Monaco Editor
  onMount(async () => {
    if (!editorContainer) return;

    try {
      // 配置Monaco Editor
      loader.config({
        paths: {
          vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs'
        }
      });

      const monacoInstance = await loader.init();
      
      // 设置主题
      monacoInstance.editor.defineTheme('quantTheme', {
        base: props.theme === 'dark' ? 'vs-dark' : 'vs',
        inherit: true,
        rules: [
          { token: 'comment', foreground: '6A9955', fontStyle: 'italic' },
          { token: 'keyword', foreground: '569CD6' },
          { token: 'string', foreground: 'CE9178' },
          { token: 'number', foreground: 'B5CEA8' },
        ],
        colors: {
          'editor.background': props.theme === 'dark' ? '#1e1e1e' : '#ffffff',
          'editor.foreground': props.theme === 'dark' ? '#d4d4d4' : '#000000',
          'editorLineNumber.foreground': props.theme === 'dark' ? '#858585' : '#237893',
        }
      });

      // 创建编辑器实例
      const editorInstance = monacoInstance.editor.create(editorContainer, {
        value: props.initialCode || defaultCode,
        language: 'python',
        theme: 'quantTheme',
        fontSize: 14,
        minimap: { enabled: true },
        scrollBeyondLastLine: false,
        wordWrap: 'on',
        automaticLayout: true,
        readOnly: props.readOnly || false,
        suggestOnTriggerCharacters: true,
        quickSuggestions: true,
        parameterHints: { enabled: true },
      });

      setEditor(editorInstance);

      // 监听内容变化
      editorInstance.onDidChangeModelContent(() => {
        const code = editorInstance.getValue();
        if (props.onCodeChange) {
          props.onCodeChange(code);
        }
        // 简单的语法检查
        validateCode(code);
      });

      // 添加自定义命令
      editorInstance.addCommand(monacoInstance.KeyMod.CtrlCmd | monacoInstance.KeyCode.KeyS, () => {
        handleSave();
      });

      editorInstance.addCommand(monacoInstance.KeyMod.CtrlCmd | monacoInstance.KeyCode.Enter, () => {
        handleRun();
      });

      setIsLoading(false);
    } catch (error) {
      console.error('Monaco Editor 初始化失败:', error);
      setIsLoading(false);
    }
  });

  // 代码验证
  const validateCode = (code: string) => {
    const newErrors: string[] = [];
    
    // 简单的Python语法检查
    const lines = code.split('\n');
    lines.forEach((line, index) => {
      // 检查缩进
      if (line.trim() && line.search(/\S/) % 4 !== 0 && line.search(/\S/) !== 0) {
        newErrors.push(`第${index + 1}行: 缩进错误`);
      }
      
      // 检查必要的方法
      if (index === 0 && !code.includes('class TradingStrategy')) {
        newErrors.push('缺少 TradingStrategy 类定义');
      }
    });

    setErrors(newErrors);
  };

  // 应用模板
  const applyTemplate = (template: StrategyTemplate) => {
    const editorInstance = editor();
    if (!editorInstance) return;

    editorInstance.setValue(template.code);
    setSelectedTemplate(template);
    
    // 初始化参数
    const initialParams: Record<string, any> = {};
    template.parameters.forEach(param => {
      initialParams[param.name] = param.default;
    });
    setParameters(initialParams);
    setShowTemplates(false);
  };

  // AI生成代码
  const handleAIGenerate = async () => {
    const prompt = aiPrompt();
    if (!prompt.trim() || !props.onGenerateAI) return;

    setIsGenerating(true);
    try {
      const generatedCode = await props.onGenerateAI(prompt);
      const editorInstance = editor();
      if (editorInstance && generatedCode) {
        editorInstance.setValue(generatedCode);
      }
    } catch (error) {
      console.error('AI生成失败:', error);
    } finally {
      setIsGenerating(false);
      setAiPrompt('');
    }
  };

  // 保存策略
  const handleSave = () => {
    const editorInstance = editor();
    if (!editorInstance || !props.onSave) return;

    const code = editorInstance.getValue();
    props.onSave(code, parameters());
  };

  // 运行策略
  const handleRun = () => {
    const editorInstance = editor();
    if (!editorInstance || !props.onRun) return;

    const code = editorInstance.getValue();
    if (errors().length > 0) {
      alert('代码存在错误，请先修复');
      return;
    }
    
    props.onRun(code, parameters());
  };

  // 更新参数
  const updateParameter = (name: string, value: any) => {
    setParameters(prev => ({ ...prev, [name]: value }));
  };

  // 监听主题变化
  createEffect(() => {
    const editorInstance = editor();
    if (!editorInstance) return;

    monaco.editor.setTheme('quantTheme');
  });

  onCleanup(() => {
    const editorInstance = editor();
    if (editorInstance) {
      editorInstance.dispose();
    }
  });

  return (
    <div style={{
      display: 'flex',
      height: `${props.height || 600}px`,
      background: props.theme === 'dark' ? '#1f2937' : '#ffffff',
      'border-radius': '8px',
      'box-shadow': '0 1px 3px 0 rgb(0 0 0 / 0.1)',
      overflow: 'hidden',
    }}>
      {/* 左侧面板 */}
      <div style={{
        width: '300px',
        'border-right': `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
        display: 'flex',
        'flex-direction': 'column',
      }}>
        {/* 工具栏 */}
        <div style={{
          padding: '16px',
          'border-bottom': `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
        }}>
          <div style={{ 'margin-bottom': '12px' }}>
            <button
              onClick={() => setShowTemplates(!showTemplates())}
              style={{
                width: '100%',
                padding: '8px 12px',
                'border-radius': '6px',
                border: `1px solid ${props.theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                background: props.theme === 'dark' ? '#374151' : '#f9fafb',
                color: props.theme === 'dark' ? '#f9fafb' : '#374151',
                cursor: 'pointer',
                'font-size': '14px',
              }}
            >
              {showTemplates() ? '隐藏' : '显示'} 策略模板
            </button>
          </div>
          
          <div style={{ 'margin-bottom': '12px' }}>
            <input
              type="text"
              placeholder="描述你想要的策略..."
              value={aiPrompt()}
              onInput={(e) => setAiPrompt(e.currentTarget.value)}
              style={{
                width: '100%',
                padding: '8px 12px',
                'border-radius': '6px',
                border: `1px solid ${props.theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                background: props.theme === 'dark' ? '#111827' : '#ffffff',
                color: props.theme === 'dark' ? '#f9fafb' : '#374151',
                'font-size': '14px',
              }}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  handleAIGenerate();
                }
              }}
            />
            <button
              onClick={handleAIGenerate}
              disabled={isGenerating() || !aiPrompt().trim()}
              style={{
                width: '100%',
                padding: '8px 12px',
                'margin-top': '8px',
                'border-radius': '6px',
                border: 'none',
                background: isGenerating() ? '#6b7280' : '#3b82f6',
                color: 'white',
                cursor: isGenerating() ? 'not-allowed' : 'pointer',
                'font-size': '14px',
              }}
            >
              {isGenerating() ? 'AI生成中...' : 'AI生成策略'}
            </button>
          </div>
        </div>

        {/* 策略模板 */}
        <Show when={showTemplates() && props.templates}>
          <div style={{
            'max-height': '200px',
            'overflow-y': 'auto',
            padding: '16px',
            'border-bottom': `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
          }}>
            <h4 style={{
              margin: '0 0 12px 0',
              'font-size': '14px',
              'font-weight': '600',
              color: props.theme === 'dark' ? '#f9fafb' : '#374151',
            }}>
              策略模板
            </h4>
            <For each={props.templates}>
              {(template) => (
                <div
                  onClick={() => applyTemplate(template)}
                  style={{
                    padding: '8px',
                    'border-radius': '4px',
                    background: selectedTemplate()?.id === template.id 
                      ? (props.theme === 'dark' ? '#374151' : '#f3f4f6')
                      : 'transparent',
                    cursor: 'pointer',
                    'margin-bottom': '4px',
                  }}
                  onMouseEnter={(e) => {
                    if (selectedTemplate()?.id !== template.id) {
                      e.currentTarget.style.background = props.theme === 'dark' ? '#29313c' : '#f9fafb';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (selectedTemplate()?.id !== template.id) {
                      e.currentTarget.style.background = 'transparent';
                    }
                  }}
                >
                  <div style={{
                    'font-weight': '500',
                    'font-size': '12px',
                    color: props.theme === 'dark' ? '#f9fafb' : '#374151',
                  }}>
                    {template.name}
                  </div>
                  <div style={{
                    'font-size': '11px',
                    color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
                    'margin-top': '2px',
                  }}>
                    {template.description}
                  </div>
                </div>
              )}
            </For>
          </div>
        </Show>

        {/* 参数配置 */}
        <Show when={showParameters() && selectedTemplate()}>
          <div style={{
            flex: 1,
            padding: '16px',
            'overflow-y': 'auto',
          }}>
            <h4 style={{
              margin: '0 0 12px 0',
              'font-size': '14px',
              'font-weight': '600',
              color: props.theme === 'dark' ? '#f9fafb' : '#374151',
            }}>
              策略参数
            </h4>
            <For each={selectedTemplate()?.parameters}>
              {(param) => (
                <div style={{ 'margin-bottom': '12px' }}>
                  <label style={{
                    display: 'block',
                    'font-size': '12px',
                    'font-weight': '500',
                    color: props.theme === 'dark' ? '#d1d5db' : '#374151',
                    'margin-bottom': '4px',
                  }}>
                    {param.name}
                  </label>
                  <Show when={param.type === 'number'}>
                    <input
                      type="number"
                      value={parameters()[param.name] || param.default}
                      min={param.min}
                      max={param.max}
                      onInput={(e) => updateParameter(param.name, parseFloat(e.currentTarget.value))}
                      style={{
                        width: '100%',
                        padding: '6px 8px',
                        'border-radius': '4px',
                        border: `1px solid ${props.theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                        background: props.theme === 'dark' ? '#111827' : '#ffffff',
                        color: props.theme === 'dark' ? '#f9fafb' : '#374151',
                        'font-size': '12px',
                      }}
                    />
                  </Show>
                  <Show when={param.type === 'string'}>
                    <input
                      type="text"
                      value={parameters()[param.name] || param.default}
                      onInput={(e) => updateParameter(param.name, e.currentTarget.value)}
                      style={{
                        width: '100%',
                        padding: '6px 8px',
                        'border-radius': '4px',
                        border: `1px solid ${props.theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                        background: props.theme === 'dark' ? '#111827' : '#ffffff',
                        color: props.theme === 'dark' ? '#f9fafb' : '#374151',
                        'font-size': '12px',
                      }}
                    />
                  </Show>
                  <Show when={param.type === 'boolean'}>
                    <input
                      type="checkbox"
                      checked={parameters()[param.name] || param.default}
                      onChange={(e) => updateParameter(param.name, e.currentTarget.checked)}
                      style={{
                        'margin-right': '8px',
                      }}
                    />
                  </Show>
                  <Show when={param.type === 'select'}>
                    <select
                      value={parameters()[param.name] || param.default}
                      onChange={(e) => updateParameter(param.name, e.currentTarget.value)}
                      style={{
                        width: '100%',
                        padding: '6px 8px',
                        'border-radius': '4px',
                        border: `1px solid ${props.theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                        background: props.theme === 'dark' ? '#111827' : '#ffffff',
                        color: props.theme === 'dark' ? '#f9fafb' : '#374151',
                        'font-size': '12px',
                      }}
                    >
                      <For each={param.options}>
                        {(option) => <option value={option}>{option}</option>}
                      </For>
                    </select>
                  </Show>
                  <div style={{
                    'font-size': '11px',
                    color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
                    'margin-top': '2px',
                  }}>
                    {param.description}
                  </div>
                </div>
              )}
            </For>
          </div>
        </Show>

        {/* 错误提示 */}
        <Show when={errors().length > 0}>
          <div style={{
            padding: '12px 16px',
            background: '#fef2f2',
            'border-top': `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
          }}>
            <div style={{
              'font-size': '12px',
              'font-weight': '500',
              color: '#dc2626',
              'margin-bottom': '4px',
            }}>
              代码错误:
            </div>
            <For each={errors()}>
              {(error) => (
                <div style={{
                  'font-size': '11px',
                  color: '#991b1b',
                  'margin-bottom': '2px',
                }}>
                  • {error}
                </div>
              )}
            </For>
          </div>
        </Show>

        {/* 操作按钮 */}
        <div style={{
          padding: '16px',
          'border-top': `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
          display: 'flex',
          gap: '8px',
        }}>
          <button
            onClick={handleSave}
            style={{
              flex: 1,
              padding: '8px 12px',
              'border-radius': '6px',
              border: 'none',
              background: '#10b981',
              color: 'white',
              cursor: 'pointer',
              'font-size': '14px',
            }}
          >
            保存
          </button>
          <button
            onClick={handleRun}
            disabled={errors().length > 0}
            style={{
              flex: 1,
              padding: '8px 12px',
              'border-radius': '6px',
              border: 'none',
              background: errors().length > 0 ? '#6b7280' : '#3b82f6',
              color: 'white',
              cursor: errors().length > 0 ? 'not-allowed' : 'pointer',
              'font-size': '14px',
            }}
          >
            运行
          </button>
        </div>
      </div>

      {/* 右侧编辑器 */}
      <div style={{ flex: 1, position: 'relative' }}>
        {isLoading() && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            'z-index': 1,
            display: 'flex',
            'align-items': 'center',
            gap: '8px',
            color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
          }}>
            <div style={{
              width: '20px',
              height: '20px',
              border: '2px solid transparent',
              'border-top-color': 'currentColor',
              'border-radius': '50%',
              animation: 'spin 1s linear infinite',
            }}></div>
            <span>加载编辑器中...</span>
          </div>
        )}
        
        <div
          ref={editorContainer}
          style={{
            width: '100%',
            height: '100%',
          }}
        />
      </div>

      <style>{`
        @keyframes spin {
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}