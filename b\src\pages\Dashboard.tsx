export default function Dashboard() {
  return (
    <div style={{
      width: "100%",
      "max-width": "1280px",
      margin: "0 auto",
      padding: "0 16px 24px"
    }}>
      {/* 页面标题 */}
      <div style={{
        "margin-bottom": "32px"
      }}>
        <h1 style={{
          "font-size": "2.5rem",
          "font-weight": "bold",
          color: "#111827",
          "margin-bottom": "8px"
        }}>
          量化交易仪表盘
        </h1>
        <p style={{
          "font-size": "1.125rem",
          color: "#6b7280"
        }}>
          实时监控市场动态，管理您的量化策略
        </p>
      </div>

      {/* 关键指标卡片 */}
      <div style={{
        display: "grid",
        "grid-template-columns": "repeat(auto-fit, minmax(250px, 1fr))",
        gap: "24px",
        "margin-bottom": "32px"
      }}>
        {/* 总资产 */}
        <div style={{
          padding: "24px",
          background: "white",
          "border-radius": "12px",
          border: "1px solid #e5e7eb",
          "box-shadow": "0 1px 3px 0 rgba(0, 0, 0, 0.1)"
        }}>
          <div style={{
            display: "flex",
            "align-items": "center",
            "justify-content": "space-between",
            "margin-bottom": "16px"
          }}>
            <div style={{
              padding: "12px",
              background: "#dbeafe",
              "border-radius": "8px"
            }}>
              💰
            </div>
            <div style={{
              "font-size": "14px",
              color: "#22c55e",
              "font-weight": "500"
            }}>
              +12.5%
            </div>
          </div>
          <div style={{
            "font-size": "2rem",
            "font-weight": "bold",
            color: "#111827",
            "margin-bottom": "4px"
          }}>
            $125,000
          </div>
          <div style={{
            "font-size": "14px",
            color: "#6b7280"
          }}>
            总资产价值
          </div>
        </div>

        {/* 今日收益 */}
        <div style={{
          padding: "24px",
          background: "white",
          "border-radius": "12px",
          border: "1px solid #e5e7eb",
          "box-shadow": "0 1px 3px 0 rgba(0, 0, 0, 0.1)"
        }}>
          <div style={{
            display: "flex",
            "align-items": "center",
            "justify-content": "space-between",
            "margin-bottom": "16px"
          }}>
            <div style={{
              padding: "12px",
              background: "#dcfce7",
              "border-radius": "8px"
            }}>
              📈
            </div>
            <div style={{
              "font-size": "14px",
              color: "#22c55e",
              "font-weight": "500"
            }}>
              +2.8%
            </div>
          </div>
          <div style={{
            "font-size": "2rem",
            "font-weight": "bold",
            color: "#111827",
            "margin-bottom": "4px"
          }}>
            $3,500
          </div>
          <div style={{
            "font-size": "14px",
            color: "#6b7280"
          }}>
            今日收益
          </div>
        </div>

        {/* 活跃策略 */}
        <div style={{
          padding: "24px",
          background: "white",
          "border-radius": "12px",
          border: "1px solid #e5e7eb",
          "box-shadow": "0 1px 3px 0 rgba(0, 0, 0, 0.1)"
        }}>
          <div style={{
            display: "flex",
            "align-items": "center",
            "justify-content": "space-between",
            "margin-bottom": "16px"
          }}>
            <div style={{
              padding: "12px",
              background: "#fef3c7",
              "border-radius": "8px"
            }}>
              🤖
            </div>
            <div style={{
              "font-size": "14px",
              color: "#22c55e",
              "font-weight": "500"
            }}>
              运行中
            </div>
          </div>
          <div style={{
            "font-size": "2rem",
            "font-weight": "bold",
            color: "#111827",
            "margin-bottom": "4px"
          }}>
            5
          </div>
          <div style={{
            "font-size": "14px",
            color: "#6b7280"
          }}>
            活跃策略
          </div>
        </div>

        {/* 胜率 */}
        <div style={{
          padding: "24px",
          background: "white",
          "border-radius": "12px",
          border: "1px solid #e5e7eb",
          "box-shadow": "0 1px 3px 0 rgba(0, 0, 0, 0.1)"
        }}>
          <div style={{
            display: "flex",
            "align-items": "center",
            "justify-content": "space-between",
            "margin-bottom": "16px"
          }}>
            <div style={{
              padding: "12px",
              background: "#e0e7ff",
              "border-radius": "8px"
            }}>
              📊
            </div>
            <div style={{
              "font-size": "14px",
              color: "#22c55e",
              "font-weight": "500"
            }}>
              优秀
            </div>
          </div>
          <div style={{
            "font-size": "2rem",
            "font-weight": "bold",
            color: "#111827",
            "margin-bottom": "4px"
          }}>
            68.5%
          </div>
          <div style={{
            "font-size": "14px",
            color: "#6b7280"
          }}>
            策略胜率
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div style={{
        display: "grid",
        "grid-template-columns": "2fr 1fr",
        gap: "24px",
        "margin-bottom": "32px"
      }}>
        {/* 图表占位 */}
        <div style={{
          padding: "24px",
          background: "white",
          "border-radius": "12px",
          border: "1px solid #e5e7eb",
          height: "400px",
          display: "flex",
          "align-items": "center",
          "justify-content": "center"
        }}>
          <div style={{
            "text-align": "center",
            color: "#9ca3af"
          }}>
            <div style={{ "font-size": "3rem", "margin-bottom": "12px" }}>📈</div>
            <p>K线图表</p>
            <p style={{ "font-size": "14px", "margin-top": "4px" }}>
              (图表组件将在后续版本中实现)
            </p>
          </div>
        </div>

        {/* 侧边栏信息 */}
        <div style={{
          display: "flex",
          "flex-direction": "column",
          gap: "24px"
        }}>
          {/* 市场热点 */}
          <div style={{
            padding: "24px",
            background: "white",
            "border-radius": "12px",
            border: "1px solid #e5e7eb"
          }}>
            <h3 style={{
              "font-size": "1.125rem",
              "font-weight": "600",
              color: "#111827",
              "margin-bottom": "16px"
            }}>
              市场热点
            </h3>

            <div style={{
              display: "flex",
              "flex-direction": "column",
              gap: "12px"
            }}>
              <div style={{
                display: "flex",
                "align-items": "center",
                "justify-content": "space-between",
                padding: "12px",
                background: "#f9fafb",
                "border-radius": "8px"
              }}>
                <div>
                  <div style={{
                    "font-weight": "500",
                    "font-size": "14px",
                    color: "#111827"
                  }}>
                    科技股强势
                  </div>
                  <div style={{
                    "font-size": "12px",
                    color: "#6b7280"
                  }}>
                    AAPL, MSFT 领涨
                  </div>
                </div>
                📈
              </div>

              <div style={{
                display: "flex",
                "align-items": "center",
                "justify-content": "space-between",
                padding: "12px",
                background: "#f9fafb",
                "border-radius": "8px"
              }}>
                <div>
                  <div style={{
                    "font-weight": "500",
                    "font-size": "14px",
                    color: "#111827"
                  }}>
                    加密货币回调
                  </div>
                  <div style={{
                    "font-size": "12px",
                    color: "#6b7280"
                  }}>
                    BTC, ETH 下跌
                  </div>
                </div>
                📉
              </div>
            </div>
          </div>

          {/* 策略表现 */}
          <div style={{
            padding: "24px",
            background: "white",
            "border-radius": "12px",
            border: "1px solid #e5e7eb"
          }}>
            <h3 style={{
              "font-size": "1.125rem",
              "font-weight": "600",
              color: "#111827",
              "margin-bottom": "16px"
            }}>
              策略表现
            </h3>

            <div style={{
              display: "flex",
              "flex-direction": "column",
              gap: "16px"
            }}>
              <div style={{
                display: "flex",
                "align-items": "center",
                "justify-content": "space-between"
              }}>
                <span style={{
                  "font-size": "14px",
                  color: "#6b7280"
                }}>
                  总策略数
                </span>
                <span style={{
                  "font-weight": "600",
                  color: "#111827"
                }}>
                  5
                </span>
              </div>

              <div style={{
                display: "flex",
                "align-items": "center",
                "justify-content": "space-between"
              }}>
                <span style={{
                  "font-size": "14px",
                  color: "#6b7280"
                }}>
                  平均收益率
                </span>
                <span style={{
                  "font-weight": "600",
                  color: "#22c55e"
                }}>
                  +15.2%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// 这个文件已经被简化版本替换，删除旧代码
