import SlideVerify from '../components/SlideVerify';
import { createSignal } from 'solid-js';

export default function Dashboard() {
  console.log('Dashboard component is rendering...')

  const [verifyMessage, setVerifyMessage] = createSignal('');

  const handleVerifySuccess = () => {
    setVerifyMessage('✅ 滑动验证成功！');
    console.log('滑动验证成功');
  };

  const handleVerifyFail = () => {
    setVerifyMessage('❌ 滑动验证失败，请重试');
    console.log('滑动验证失败');
  };

  const handleVerifyRefresh = () => {
    setVerifyMessage('🔄 验证码已刷新');
    console.log('验证码已刷新');
  };

  return (
    <div>
      <h1>量化交易仪表盘</h1>
      <p>欢迎使用量化交易平台！</p>
      <p>当前时间: {new Date().toLocaleString('zh-CN')}</p>

      <div style={{ padding: '20px', background: '#f0f0f0', margin: '20px 0', borderRadius: '8px' }}>
        <h2>🎉 路由工作正常！</h2>
        <p>这是Dashboard页面</p>
        <p>SolidJS Router 已经正确配置</p>
      </div>

      <div style={{ padding: '20px', background: 'white', margin: '20px 0', borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
        <h2>滑动验证组件演示</h2>
        <p>参考开源项目实现的滑动验证组件：</p>

        <div style={{ margin: '20px 0' }}>
          <SlideVerify
            width={320}
            height={160}
            sliderText="向右滑动完成验证"
            onSuccess={handleVerifySuccess}
            onFail={handleVerifyFail}
            onRefresh={handleVerifyRefresh}
          />
        </div>

        {verifyMessage() && (
          <div style={{
            padding: '10px',
            marginTop: '10px',
            borderRadius: '4px',
            backgroundColor: verifyMessage().includes('成功') ? '#f6ffed' :
                           verifyMessage().includes('失败') ? '#fff2f0' : '#f0f9ff',
            border: `1px solid ${verifyMessage().includes('成功') ? '#b7eb8f' :
                                verifyMessage().includes('失败') ? '#ffccc7' : '#91d5ff'}`,
            color: verifyMessage().includes('成功') ? '#52c41a' :
                   verifyMessage().includes('失败') ? '#ff4d4f' : '#1890ff'
          }}>
            {verifyMessage()}
          </div>
        )}
      </div>
    </div>
  );
}
