import TestComponent from '../components/TestComponent'

export default function Dashboard() {
  // 简化的统计数据
  const totalAssets = () => 125000
  const todayReturn = () => 3500
  const activeStrategies = () => 5
  const winRate = () => 68.5

  return (
    <div style={{
      width: "100%",
      "max-width": "1280px",
      margin: "0 auto",
      padding: "0 16px 24px"
    }}>
      {/* 页面标题 */}
      <div style={{
        "margin-bottom": "32px"
      }}>
        <h1 style={{
          "font-size": "2.5rem",
          "font-weight": "bold",
          color: "#111827",
          "margin-bottom": "8px"
        }}>
          量化交易仪表盘
        </h1>
        <p style={{
          "font-size": "1.125rem",
          color: "#6b7280"
        }}>
          实时监控市场动态，管理您的量化策略
        </p>
        <p style={{
          "font-size": "0.875rem",
          color: "#9ca3af",
          "margin-top": "4px"
        }}>
          欢迎使用量化交易平台
        </p>
      </div>

      {/* 测试组件 */}
      <TestComponent />

      {/* 关键指标卡片 */}
      <div style={{
        display: "grid",
        "grid-template-columns": "repeat(auto-fit, minmax(250px, 1fr))",
        gap: "24px",
        "margin-bottom": "32px"
      }}>
        {/* 总资产 */}
        <div style={{
          padding: "24px",
          background: "white",
          "border-radius": "12px",
          border: "1px solid #e5e7eb",
          "box-shadow": "0 1px 3px 0 rgba(0, 0, 0, 0.1)"
        }}>
          <div style={{
            display: "flex",
            "align-items": "center",
            "justify-content": "space-between",
            "margin-bottom": "16px"
          }}>
            <div style={{
              padding: "12px",
              background: "#dbeafe",
              "border-radius": "8px"
            }}>
              💰
            </div>
            <div style={{
              "font-size": "14px",
              color: totalAssets() > 125000 ? "#22c55e" : "#ef4444",
              "font-weight": "500"
            }}>
              {totalAssets() > 125000 ? '+' : ''}{((totalAssets() - 125000) / 125000 * 100).toFixed(2)}%
            </div>
          </div>
          <div style={{
            "font-size": "2rem",
            "font-weight": "bold",
            color: "#111827",
            "margin-bottom": "4px"
          }}>
            ${totalAssets().toLocaleString('en-US', { maximumFractionDigits: 0 })}
          </div>
          <div style={{
            "font-size": "14px",
            color: "#6b7280"
          }}>
            总资产价值
          </div>
        </div>

        {/* 今日收益 */}
        <div style={{
          padding: "24px",
          background: "white",
          "border-radius": "12px",
          border: "1px solid #e5e7eb",
          "box-shadow": "0 1px 3px 0 rgba(0, 0, 0, 0.1)"
        }}>
          <div style={{
            display: "flex",
            "align-items": "center",
            "justify-content": "space-between",
            "margin-bottom": "16px"
          }}>
            <div style={{
              padding: "12px",
              background: todayReturn() > 3500 ? "#dcfce7" : "#fee2e2",
              "border-radius": "8px"
            }}>
              {todayReturn() > 3500 ? '📈' : '📉'}
            </div>
            <div style={{
              "font-size": "14px",
              color: todayReturn() > 3500 ? "#22c55e" : "#ef4444",
              "font-weight": "500"
            }}>
              {todayReturn() > 3500 ? '+' : ''}{((todayReturn() - 3500) / 3500 * 100).toFixed(1)}%
            </div>
          </div>
          <div style={{
            "font-size": "2rem",
            "font-weight": "bold",
            color: "#111827",
            "margin-bottom": "4px"
          }}>
            ${todayReturn().toLocaleString('en-US', { maximumFractionDigits: 0 })}
          </div>
          <div style={{
            "font-size": "14px",
            color: "#6b7280"
          }}>
            今日收益
          </div>
        </div>

        {/* 活跃策略 */}
        <div style={{
          padding: "24px",
          background: "white",
          "border-radius": "12px",
          border: "1px solid #e5e7eb",
          "box-shadow": "0 1px 3px 0 rgba(0, 0, 0, 0.1)"
        }}>
          <div style={{
            display: "flex",
            "align-items": "center",
            "justify-content": "space-between",
            "margin-bottom": "16px"
          }}>
            <div style={{
              padding: "12px",
              background: "#fef3c7",
              "border-radius": "8px"
            }}>
              🤖
            </div>
            <div style={{
              "font-size": "14px",
              color: activeStrategies() > 0 ? "#22c55e" : "#6b7280",
              "font-weight": "500"
            }}>
              {activeStrategies() > 0 ? '运行中' : '已停止'}
            </div>
          </div>
          <div style={{
            "font-size": "2rem",
            "font-weight": "bold",
            color: "#111827",
            "margin-bottom": "4px"
          }}>
            {activeStrategies()}
          </div>
          <div style={{
            "font-size": "14px",
            color: "#6b7280"
          }}>
            活跃策略
          </div>
        </div>

        {/* 胜率 */}
        <div style={{
          padding: "24px",
          background: "white",
          "border-radius": "12px",
          border: "1px solid #e5e7eb",
          "box-shadow": "0 1px 3px 0 rgba(0, 0, 0, 0.1)"
        }}>
          <div style={{
            display: "flex",
            "align-items": "center",
            "justify-content": "space-between",
            "margin-bottom": "16px"
          }}>
            <div style={{
              padding: "12px",
              background: "#e0e7ff",
              "border-radius": "8px"
            }}>
              📊
            </div>
            <div style={{
              "font-size": "14px",
              color: winRate() >= 60 ? "#22c55e" : winRate() >= 40 ? "#f59e0b" : "#ef4444",
              "font-weight": "500"
            }}>
              {winRate() >= 60 ? '优秀' : winRate() >= 40 ? '良好' : '需改进'}
            </div>
          </div>
          <div style={{
            "font-size": "2rem",
            "font-weight": "bold",
            color: "#111827",
            "margin-bottom": "4px"
          }}>
            {winRate().toFixed(1)}%
          </div>
          <div style={{
            "font-size": "14px",
            color: "#6b7280"
          }}>
            策略胜率
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div style={{
        display: "grid",
        "grid-template-columns": "2fr 1fr",
        gap: "24px",
        "margin-bottom": "32px"
      }}>
        {/* 图表占位 */}
        <div style={{
          padding: "24px",
          background: "white",
          "border-radius": "12px",
          border: "1px solid #e5e7eb",
          height: "400px",
          display: "flex",
          "align-items": "center",
          "justify-content": "center"
        }}>
          <div style={{
            "text-align": "center",
            color: "#9ca3af"
          }}>
            <div style={{ "font-size": "3rem", "margin-bottom": "12px" }}>📈</div>
            <p>K线图表</p>
            <p style={{ "font-size": "14px", "margin-top": "4px" }}>
              (图表组件将在后续版本中实现)
            </p>
          </div>
        </div>

        {/* 侧边栏信息 */}
        <div style={{
          display: "flex",
          "flex-direction": "column",
          gap: "24px"
        }}>
          {/* 市场热点 */}
          <div style={{
            padding: "24px",
            background: "white",
            "border-radius": "12px",
            border: "1px solid #e5e7eb"
          }}>
            <h3 style={{
              "font-size": "1.125rem",
              "font-weight": "600",
              color: "#111827",
              "margin-bottom": "16px"
            }}>
              市场热点
            </h3>

            <div style={{
              display: "flex",
              "flex-direction": "column",
              gap: "12px"
            }}>
              {[
                { name: '上证指数', value: 3200.50, changePercent: 1.2 },
                { name: '深证成指', value: 11500.30, changePercent: -0.8 },
                { name: '创业板指', value: 2400.80, changePercent: 2.1 }
              ].map((index) => (
                <div style={{
                  display: "flex",
                  "align-items": "center",
                  "justify-content": "space-between",
                  padding: "12px",
                  background: "#f9fafb",
                  "border-radius": "8px"
                }}>
                  <div>
                    <div style={{
                      "font-weight": "500",
                      "font-size": "14px",
                      color: "#111827"
                    }}>
                      {index.name}
                    </div>
                    <div style={{
                      "font-size": "12px",
                      color: "#6b7280"
                    }}>
                      {index.value.toFixed(2)} {index.changePercent >= 0 ? '+' : ''}{index.changePercent.toFixed(2)}%
                    </div>
                  </div>
                  <span style={{
                    color: index.changePercent >= 0 ? "#22c55e" : "#ef4444"
                  }}>
                    {index.changePercent >= 0 ? '📈' : '📉'}
                  </span>
                </div>
              )) || (
                <>
                  <div style={{
                    display: "flex",
                    "align-items": "center",
                    "justify-content": "space-between",
                    padding: "12px",
                    background: "#f9fafb",
                    "border-radius": "8px"
                  }}>
                    <div>
                      <div style={{
                        "font-weight": "500",
                        "font-size": "14px",
                        color: "#111827"
                      }}>
                        科技股强势
                      </div>
                      <div style={{
                        "font-size": "12px",
                        color: "#6b7280"
                      }}>
                        AAPL, MSFT 领涨
                      </div>
                    </div>
                    📈
                  </div>

                  <div style={{
                    display: "flex",
                    "align-items": "center",
                    "justify-content": "space-between",
                    padding: "12px",
                    background: "#f9fafb",
                    "border-radius": "8px"
                  }}>
                    <div>
                      <div style={{
                        "font-weight": "500",
                        "font-size": "14px",
                        color: "#111827"
                      }}>
                        加密货币回调
                      </div>
                      <div style={{
                        "font-size": "12px",
                        color: "#6b7280"
                      }}>
                        BTC, ETH 下跌
                      </div>
                    </div>
                    📉
                  </div>
                </>
              )}
            </div>
          </div>

          {/* 策略表现 */}
          <div style={{
            padding: "24px",
            background: "white",
            "border-radius": "12px",
            border: "1px solid #e5e7eb"
          }}>
            <h3 style={{
              "font-size": "1.125rem",
              "font-weight": "600",
              color: "#111827",
              "margin-bottom": "16px"
            }}>
              策略表现
            </h3>

            <div style={{
              display: "flex",
              "flex-direction": "column",
              gap: "16px"
            }}>
              <div style={{
                display: "flex",
                "align-items": "center",
                "justify-content": "space-between"
              }}>
                <span style={{
                  "font-size": "14px",
                  color: "#6b7280"
                }}>
                  总策略数
                </span>
                <span style={{
                  "font-weight": "600",
                  color: "#111827"
                }}>
                  5
                </span>
              </div>

              <div style={{
                display: "flex",
                "align-items": "center",
                "justify-content": "space-between"
              }}>
                <span style={{
                  "font-size": "14px",
                  color: "#6b7280"
                }}>
                  活跃策略
                </span>
                <span style={{
                  "font-weight": "600",
                  color: "#111827"
                }}>
                  {activeStrategies()}
                </span>
              </div>

              <div style={{
                display: "flex",
                "align-items": "center",
                "justify-content": "space-between"
              }}>
                <span style={{
                  "font-size": "14px",
                  color: "#6b7280"
                }}>
                  平均收益率
                </span>
                <span style={{
                  "font-weight": "600",
                  color: "#22c55e"
                }}>
                  +{(Math.random() * 20 + 5).toFixed(1)}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// 这个文件已经被简化版本替换，删除旧代码
