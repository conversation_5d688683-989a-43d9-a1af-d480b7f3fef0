import { createSignal, Show } from 'solid-js'
import { css } from '../../styled-system/css'
import { userStore } from '../stores'
import MetricCard from '../components/MetricCard'
import PortfolioOverview from '../components/PortfolioOverview'
import MarketNews from '../components/MarketNews'

export default function Dashboard() {
  const [currentTime, setCurrentTime] = createSignal(new Date().toLocaleString('zh-CN'))
  const isAuthenticated = () => userStore.state.isAuthenticated

  // 更新时间
  setInterval(() => {
    setCurrentTime(new Date().toLocaleString('zh-CN'))
  }, 1000)

  // 模拟账户数据
  const accountMetrics = {
    totalAssets: 1234567.89,
    dailyProfit: 12345.67,
    dailyProfitPercent: 2.34,
    totalProfit: 234567.89,
    totalProfitPercent: 24.56,
    positionCount: 8
  }

  const formatCurrency = (value: number) => {
    return value.toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  }

  return (
    <div class={css({ width: '100%', maxWidth: '1200px', margin: '0 auto' })}>
      {/* 欢迎区域 */}
      <div class={css({
        bg: 'white',
        border: '1px solid #e8e8e8',
        borderRadius: '8px',
        p: '20px',
        mb: '20px'
      })}>
        <div class={css({
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        })}>
          <div>
            <h1 class={css({
              fontSize: '24px',
              fontWeight: 'bold',
              color: '#333',
              mb: '8px'
            })}>
              📊 投资收益盘
            </h1>
            <p class={css({
              fontSize: '14px',
              color: '#666',
              margin: 0
            })}>
              {currentTime()} | 交易时间
            </p>
          </div>
          <div class={css({
            display: 'flex',
            gap: '12px'
          })}>
            <button class={css({
              px: '16px',
              py: '8px',
              bg: '#1890ff',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontSize: '14px',
              cursor: 'pointer',
              _hover: { bg: '#40a9ff' }
            })}>
              刷新数据
            </button>
            <button class={css({
              px: '16px',
              py: '8px',
              bg: 'transparent',
              color: '#1890ff',
              border: '1px solid #1890ff',
              borderRadius: '6px',
              fontSize: '14px',
              cursor: 'pointer',
              _hover: { bg: '#f0f8ff' }
            })}>
              导出报告
            </button>
          </div>
        </div>
      </div>

      {/* 核心指标卡片 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
        gap: '20px',
        mb: '20px'
      })}>
        <MetricCard
          title="总资产"
          value={formatCurrency(accountMetrics.totalAssets)}
          unit="¥"
          change={accountMetrics.totalProfit}
          changePercent={accountMetrics.totalProfitPercent}
          type="primary"
          icon="💰"
          showChart={true}
          clickable={true}
        />

        <MetricCard
          title="今日收益"
          value={formatCurrency(Math.abs(accountMetrics.dailyProfit))}
          unit="¥"
          change={accountMetrics.dailyProfit}
          changePercent={accountMetrics.dailyProfitPercent}
          type={accountMetrics.dailyProfit >= 0 ? 'success' : 'danger'}
          icon="📈"
          showChart={true}
          clickable={true}
        />

        <MetricCard
          title="总收益"
          value={formatCurrency(Math.abs(accountMetrics.totalProfit))}
          unit="¥"
          change={accountMetrics.totalProfit}
          changePercent={accountMetrics.totalProfitPercent}
          type={accountMetrics.totalProfit >= 0 ? 'success' : 'danger'}
          icon="📊"
          showChart={true}
          clickable={true}
        />

        <MetricCard
          title="持仓股票"
          value={accountMetrics.positionCount.toString()}
          unit="只"
          type="info"
          icon="🏢"
          clickable={true}
        />
      </div>

      {/* 主要内容区域 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: '2fr 1fr',
        gap: '20px'
      })}>
        {/* 左侧：投资组合概览 */}
        <div>
          <PortfolioOverview title="投资组合概览" />
        </div>

        {/* 右侧：市场资讯 */}
        <div>
          <MarketNews title="市场资讯" maxItems={4} />
        </div>
      </div>

      {/* 认证提示 */}
      <Show when={!isAuthenticated()}>
        <div class={css({
          bg: '#fef3c7',
          border: '1px solid #f59e0b',
          borderRadius: '8px',
          p: '16px',
          mt: '20px'
        })}>
          <p class={css({
            margin: 0,
            color: '#92400e',
            fontSize: '14px'
          })}>
            ⚠️ 您尚未登录，当前显示为模拟数据。请先
            <a href="/login" class={css({
              color: '#d97706',
              textDecoration: 'underline',
              _hover: { textDecoration: 'none' }
            })}>
              登录
            </a>
            以查看真实数据。
          </p>
        </div>
      </Show>
    </div>
  )
}
