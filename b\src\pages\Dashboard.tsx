import { useAtomValue } from 'jotai';
import { marketOverviewAtom, topMoversAtom, connectionStatusAtom } from '@/stores/market';
import { strategyStatsAtom } from '@/stores/strategy';
import RealTimeTicker from '@/components/RealTimeTicker';
import FinancialChart from '@/components/FinancialChart';
import { css } from '../../styled-system/css';
import { FiTrendingUp, FiTrendingDown, FiActivity, FiCode, FiBarChart3, FiDollarSign } from 'solid-icons/fi';

export default function Dashboard() {
  const marketOverview = useAtomValue(marketOverviewAtom);
  const topMovers = useAtomValue(topMoversAtom);
  const connectionStatus = useAtomValue(connectionStatusAtom);
  const strategyStats = useAtomValue(strategyStatsAtom);
  
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };
  
  const formatPercent = (value: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value / 100);
  };
  
  return (
    <div class={css({
      w: 'full',
      maxW: '7xl',
      mx: 'auto',
      px: 4,
      py: 6
    })}>
      {/* 页面标题 */}
      <div class={css({
        mb: 8
      })}>
        <h1 class={css({
          fontSize: '3xl',
          fontWeight: 'bold',
          color: 'gray.900',
          mb: 2,
          _dark: { color: 'gray.100' }
        })}>
          量化交易仪表盘
        </h1>
        <p class={css({
          fontSize: 'lg',
          color: 'gray.600',
          _dark: { color: 'gray.400' }
        })}>
          实时监控市场动态，管理您的量化策略
        </p>
      </div>
      
      {/* 关键指标卡片 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: { base: '1fr', sm: 'repeat(2, 1fr)', lg: 'repeat(4, 1fr)' },
        gap: 6,
        mb: 8
      })}>
        {/* 总资产 */}
        <div class={css({
          p: 6,
          bg: 'white',
          rounded: 'xl',
          border: '1px solid',
          borderColor: 'gray.200',
          shadow: 'sm',
          _dark: {
            bg: 'gray.800',
            borderColor: 'gray.700'
          }
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: 4
          })}>
            <div class={css({
              p: 3,
              bg: 'primary.100',
              rounded: 'lg',
              _dark: {
                bg: 'primary.900/30'
              }
            })}>
              <FiDollarSign size={24} class={css({ color: 'primary.600' })} />
            </div>
            <div class={css({
              fontSize: 'sm',
              color: 'success.600',
              fontWeight: 'medium'
            })}>
              +12.5%
            </div>
          </div>
          <div class={css({
            fontSize: '2xl',
            fontWeight: 'bold',
            color: 'gray.900',
            mb: 1,
            _dark: { color: 'gray.100' }
          })}>
            {formatCurrency(125000)}
          </div>
          <div class={css({
            fontSize: 'sm',
            color: 'gray.600',
            _dark: { color: 'gray.400' }
          })}>
            总资产价值
          </div>
        </div>
        
        {/* 今日收益 */}
        <div class={css({
          p: 6,
          bg: 'white',
          rounded: 'xl',
          border: '1px solid',
          borderColor: 'gray.200',
          shadow: 'sm',
          _dark: {
            bg: 'gray.800',
            borderColor: 'gray.700'
          }
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: 4
          })}>
            <div class={css({
              p: 3,
              bg: 'success.100',
              rounded: 'lg',
              _dark: {
                bg: 'success.900/30'
              }
            })}>
              <FiTrendingUp size={24} class={css({ color: 'success.600' })} />
            </div>
            <div class={css({
              fontSize: 'sm',
              color: 'success.600',
              fontWeight: 'medium'
            })}>
              +2.8%
            </div>
          </div>
          <div class={css({
            fontSize: '2xl',
            fontWeight: 'bold',
            color: 'gray.900',
            mb: 1,
            _dark: { color: 'gray.100' }
          })}>
            {formatCurrency(3500)}
          </div>
          <div class={css({
            fontSize: 'sm',
            color: 'gray.600',
            _dark: { color: 'gray.400' }
          })}>
            今日收益
          </div>
        </div>
        
        {/* 活跃策略 */}
        <div class={css({
          p: 6,
          bg: 'white',
          rounded: 'xl',
          border: '1px solid',
          borderColor: 'gray.200',
          shadow: 'sm',
          _dark: {
            bg: 'gray.800',
            borderColor: 'gray.700'
          }
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: 4
          })}>
            <div class={css({
              p: 3,
              bg: 'warning.100',
              rounded: 'lg',
              _dark: {
                bg: 'warning.900/30'
              }
            })}>
              <FiCode size={24} class={css({ color: 'warning.600' })} />
            </div>
            <div class={css({
              fontSize: 'sm',
              color: connectionStatus() ? 'success.600' : 'danger.600',
              fontWeight: 'medium'
            })}>
              {connectionStatus() ? '运行中' : '已停止'}
            </div>
          </div>
          <div class={css({
            fontSize: '2xl',
            fontWeight: 'bold',
            color: 'gray.900',
            mb: 1,
            _dark: { color: 'gray.100' }
          })}>
            {strategyStats().totalStrategies}
          </div>
          <div class={css({
            fontSize: 'sm',
            color: 'gray.600',
            _dark: { color: 'gray.400' }
          })}>
            活跃策略
          </div>
        </div>
        
        {/* 胜率 */}
        <div class={css({
          p: 6,
          bg: 'white',
          rounded: 'xl',
          border: '1px solid',
          borderColor: 'gray.200',
          shadow: 'sm',
          _dark: {
            bg: 'gray.800',
            borderColor: 'gray.700'
          }
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: 4
          })}>
            <div class={css({
              p: 3,
              bg: 'purple.100',
              rounded: 'lg',
              _dark: {
                bg: 'purple.900/30'
              }
            })}>
              <FiBarChart3 size={24} class={css({ color: 'purple.600' })} />
            </div>
            <div class={css({
              fontSize: 'sm',
              color: 'success.600',
              fontWeight: 'medium'
            })}>
              优秀
            </div>
          </div>
          <div class={css({
            fontSize: '2xl',
            fontWeight: 'bold',
            color: 'gray.900',
            mb: 1,
            _dark: { color: 'gray.100' }
          })}>
            68.5%
          </div>
          <div class={css({
            fontSize: 'sm',
            color: 'gray.600',
            _dark: { color: 'gray.400' }
          })}>
            策略胜率
          </div>
        </div>
      </div>
      
      {/* 实时行情组件 */}
      <RealTimeTicker />
      
      {/* 主要内容区域 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: { base: '1fr', xl: '2fr 1fr' },
        gap: 6,
        mb: 8
      })}>
        {/* K线图表 */}
        <div>
          <FinancialChart height={400} showVolume={true} />
        </div>
        
        {/* 侧边栏信息 */}
        <div class={css({
          display: 'flex',
          flexDirection: 'column',
          gap: 6
        })}>
          {/* 市场热点 */}
          <div class={css({
            p: 6,
            bg: 'white',
            rounded: 'xl',
            border: '1px solid',
            borderColor: 'gray.200',
            _dark: {
              bg: 'gray.800',
              borderColor: 'gray.700'
            }
          })}>
            <h3 class={css({
              fontSize: 'lg',
              fontWeight: 'semibold',
              color: 'gray.900',
              mb: 4,
              _dark: { color: 'gray.100' }
            })}>
              市场热点
            </h3>
            
            <div class={css({
              space: 'y-3'
            })}>
              <div class={css({
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                p: 3,
                bg: 'gray.50',
                rounded: 'lg',
                _dark: {
                  bg: 'gray.700'
                }
              })}>
                <div>
                  <div class={css({
                    fontWeight: 'medium',
                    fontSize: 'sm',
                    color: 'gray.900',
                    _dark: { color: 'gray.100' }
                  })}>
                    科技股强势
                  </div>
                  <div class={css({
                    fontSize: 'xs',
                    color: 'gray.600',
                    _dark: { color: 'gray.400' }
                  })}>
                    AAPL, MSFT 领涨
                  </div>
                </div>
                <FiTrendingUp size={16} class={css({ color: 'success.500' })} />
              </div>
              
              <div class={css({
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                p: 3,
                bg: 'gray.50',
                rounded: 'lg',
                _dark: {
                  bg: 'gray.700'
                }
              })}>
                <div>
                  <div class={css({
                    fontWeight: 'medium',
                    fontSize: 'sm',
                    color: 'gray.900',
                    _dark: { color: 'gray.100' }
                  })}>
                    加密货币回调
                  </div>
                  <div class={css({
                    fontSize: 'xs',
                    color: 'gray.600',
                    _dark: { color: 'gray.400' }
                  })}>
                    BTC, ETH 下跌
                  </div>
                </div>
                <FiTrendingDown size={16} class={css({ color: 'danger.500' })} />
              </div>
              
              <div class={css({
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                p: 3,
                bg: 'gray.50',
                rounded: 'lg',
                _dark: {
                  bg: 'gray.700'
                }
              })}>
                <div>
                  <div class={css({
                    fontWeight: 'medium',
                    fontSize: 'sm',
                    color: 'gray.900',
                    _dark: { color: 'gray.100' }
                  })}>
                    大宗商品震荡
                  </div>
                  <div class={css({
                    fontSize: 'xs',
                    color: 'gray.600',
                    _dark: { color: 'gray.400' }
                  })}>
                    黄金、原油横盘
                  </div>
                </div>
                <FiActivity size={16} class={css({ color: 'warning.500' })} />
              </div>
            </div>
          </div>
          
          {/* 策略表现 */}
          <div class={css({
            p: 6,
            bg: 'white',
            rounded: 'xl',
            border: '1px solid',
            borderColor: 'gray.200',
            _dark: {
              bg: 'gray.800',
              borderColor: 'gray.700'
            }
          })}>
            <h3 class={css({
              fontSize: 'lg',
              fontWeight: 'semibold',
              color: 'gray.900',
              mb: 4,
              _dark: { color: 'gray.100' }
            })}>
              策略表现
            </h3>
            
            <div class={css({
              space: 'y-4'
            })}>
              <div class={css({
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              })}>
                <span class={css({
                  fontSize: 'sm',
                  color: 'gray.600',
                  _dark: { color: 'gray.400' }
                })}>
                  总策略数
                </span>
                <span class={css({
                  fontWeight: 'semibold',
                  color: 'gray.900',
                  _dark: { color: 'gray.100' }
                })}>
                  {strategyStats().totalStrategies}
                </span>
              </div>
              
              <div class={css({
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              })}>
                <span class={css({
                  fontSize: 'sm',
                  color: 'gray.600',
                  _dark: { color: 'gray.400' }
                })}>
                  公开策略
                </span>
                <span class={css({
                  fontWeight: 'semibold',
                  color: 'gray.900',
                  _dark: { color: 'gray.100' }
                })}>
                  {strategyStats().publicStrategies}
                </span>
              </div>
              
              <div class={css({
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              })}>
                <span class={css({
                  fontSize: 'sm',
                  color: 'gray.600',
                  _dark: { color: 'gray.400' }
                })}>
                  回测次数
                </span>
                <span class={css({
                  fontWeight: 'semibold',
                  color: 'gray.900',
                  _dark: { color: 'gray.100' }
                })}>
                  {strategyStats().totalBacktests}
                </span>
              </div>
              
              <div class={css({
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              })}>
                <span class={css({
                  fontSize: 'sm',
                  color: 'gray.600',
                  _dark: { color: 'gray.400' }
                })}>
                  平均收益率
                </span>
                <span class={css({
                  fontWeight: 'semibold',
                  color: strategyStats().avgReturn >= 0 ? 'success.600' : 'danger.600'
                })}>
                  {formatPercent(strategyStats().avgReturn)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
