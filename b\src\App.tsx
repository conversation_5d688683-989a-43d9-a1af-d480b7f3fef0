import { lazy, Suspense, createEffect } from 'solid-js';
import { Router, Route } from '@solidjs/router';
import { ThemeProvider } from './context/ThemeContext';
import Header from './components/Header';
import Footer from './components/Footer';
import LoadingSpinner from './components/LoadingSpinner';
import { apiManager } from './api';
import globalStore from './stores';

// 懒加载页面组件以优化性能
const Dashboard = lazy(() => import('./pages/Dashboard'));
const StrategyEditor = lazy(() => import('./pages/StrategyEditor'));
const BacktestAnalysis = lazy(() => import('./pages/BacktestAnalysis'));
const MarketData = lazy(() => import('./pages/MarketData'));
const Login = lazy(() => import('./pages/Login'));

// 根布局组件
function AppLayout(props) {
  // 初始化API管理器和全局状态
  createEffect(() => {
    console.log('应用开始初始化...')
    try {
      // 简化初始化，避免异步问题
      console.log('应用初始化完成')
    } catch (error) {
      console.error('应用初始化失败:', error)
    }
  })

  return (
    <ThemeProvider>
      <div style={{
        "min-height": "100vh",
        display: "flex",
        "flex-direction": "column",
        background: "#f9fafb",
        color: "#111827"
      }}>
        {/* 顶部导航 */}
        <Header />

        {/* 主要内容区域 */}
        <main style={{
          flex: 1,
          "max-width": "1280px",
          margin: "0 auto",
          padding: "24px 16px",
          width: "100%"
        }}>
          <Suspense fallback={<LoadingSpinner />}>
            {props.children}
          </Suspense>
        </main>

        {/* 底部信息 */}
        <Footer />
      </div>
    </ThemeProvider>
  );
}

export default function App() {
  return (
    <Router root={AppLayout}>
      <Route path="/" component={Dashboard} />
      <Route path="/login" component={Login} />
      <Route path="/strategy" component={StrategyEditor} />
      <Route path="/backtest" component={BacktestAnalysis} />
      <Route path="/market" component={MarketData} />
      {/* 404 页面 */}
      <Route path="*" component={() => (
        <div style={{
          "text-align": "center",
          padding: "80px 0"
        }}>
          <h1 style={{
            "font-size": "2.5rem",
            "font-weight": "bold",
            "margin-bottom": "16px"
          }}>
            404 - 页面未找到
          </h1>
          <p style={{ color: "#6b7280" }}>
            您访问的页面不存在
          </p>
        </div>
      )} />
    </Router>
  );
}
