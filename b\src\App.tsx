import { lazy, Suspense } from 'solid-js';
import { Router, Route } from '@solidjs/router';
import AdvancedLayout from './components/AdvancedLayout';

// 懒加载页面组件以优化性能
const Dashboard = lazy(() => import('./pages/Dashboard'));
const StrategyEditor = lazy(() => import('./pages/StrategyEditor'));
const BacktestAnalysis = lazy(() => import('./pages/BacktestAnalysis'));
const MarketData = lazy(() => import('./pages/MarketData'));
const TradingCenter = lazy(() => import('./pages/TradingCenter'));
const TradingTerminal = lazy(() => import('./pages/TradingTerminal'));
const Login = lazy(() => import('./pages/Login'));

export default function App() {
  return (
    <Router root={AdvancedLayout}>
      <Route path="/" component={Dashboard} />
      <Route path="/login" component={Login} />
      <Route path="/strategy" component={StrategyEditor} />
      <Route path="/backtest" component={BacktestAnalysis} />
      <Route path="/market" component={MarketData} />
      <Route path="/trading" component={TradingCenter} />
      <Route path="/trading/terminal" component={TradingTerminal} />
    </Router>
  );
}
