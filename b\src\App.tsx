import { lazy, Suspense } from 'solid-js';
import { Routes, Route } from '@solidjs/router';
import { ThemeProvider } from '@/context/ThemeContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import LoadingSpinner from '@/components/LoadingSpinner';
import { css } from '../styled-system/css';

// 懒加载页面组件以优化性能
const Dashboard = lazy(() => import('@/pages/Dashboard'));
const StrategyEditor = lazy(() => import('@/pages/StrategyEditor'));
const BacktestAnalysis = lazy(() => import('@/pages/BacktestAnalysis'));
const MarketData = lazy(() => import('@/pages/MarketData'));

export default function App() {
  return (
    <ThemeProvider>
      <div class={css({
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        bg: 'gray.50',
        color: 'gray.900',
        transition: 'all 0.2s ease',
        _dark: {
          bg: 'gray.900',
          color: 'gray.100'
        }
      })}>
        {/* 顶部导航 */}
        <Header />
        
        {/* 主要内容区域 */}
        <main class={css({
          flex: 1,
          container: 'xl',
          mx: 'auto',
          px: 4,
          py: 6,
          w: 'full'
        })}>
          <Suspense fallback={<LoadingSpinner />}>
            <Routes>
              <Route path="/" component={Dashboard} />
              <Route path="/strategy" component={StrategyEditor} />
              <Route path="/backtest" component={BacktestAnalysis} />
              <Route path="/market" component={MarketData} />
              {/* 404 页面 */}
              <Route path="*" component={() => (
                <div class={css({
                  textAlign: 'center',
                  py: 20
                })}>
                  <h1 class={css({ fontSize: '4xl', fontWeight: 'bold', mb: 4 })}>
                    404 - 页面未找到
                  </h1>
                  <p class={css({ color: 'gray.600', _dark: { color: 'gray.400' } })}>
                    您访问的页面不存在
                  </p>
                </div>
              )} />
            </Routes>
          </Suspense>
        </main>
        
        {/* 底部信息 */}
        <Footer />
      </div>
    </ThemeProvider>
  );
}
