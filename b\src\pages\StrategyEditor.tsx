import { createEffect, createSignal, For, Show } from 'solid-js'
import { strategyStore } from '../stores'
import AdvancedStrategyEditor from '@/components/AdvancedStrategyEditor'
import { backtestApi } from '@/api'
import CodeEditor from '../components/CodeEditor'
import AIAssistant from '../components/AIAssistant'
import { useTheme } from '../context/ThemeContext'

export default function StrategyEditor() {
  const strategyState = () => strategyStore.state

  const [selectedStrategy, setSelectedStrategy] = createSignal<any>(null)
  const [isCreating, setIsCreating] = createSignal(false)

  // 初始化时获取数据
  createEffect(() => {
    strategyStore.fetchStrategies()
    strategyStore.fetchTemplates()
  })

  const handleCreateStrategy = () => {
    setIsCreating(true)
    setSelectedStrategy(null)
  }

  const handleSelectStrategy = (strategy: any) => {
    setSelectedStrategy(strategy)
    setIsCreating(false)
  }

  return (
    <div style={{
      width: "100%",
      "max-width": "1280px",
      margin: "0 auto",
      padding: "0 16px 24px"
    }}>
      <div style={{
        "margin-bottom": "32px"
      }}>
        <h1 style={{
          "font-size": "2.5rem",
          "font-weight": "bold",
          color: "#111827",
          "margin-bottom": "8px"
        }}>
          策略编辑器
        </h1>
        <p style={{
          "font-size": "1.125rem",
          color: "#6b7280"
        }}>
          创建和编辑您的量化交易策略
        </p>
        <div style={{
          display: "flex",
          "align-items": "center",
          gap: "16px",
          "margin-top": "16px"
        }}>
          <span style={{
            "font-size": "0.875rem",
            color: "#9ca3af"
          }}>
            总策略: {strategyState().strategies.length}
          </span>
          <span style={{
            "font-size": "0.875rem",
            color: "#9ca3af"
          }}>
            活跃: {strategyState().strategies.filter(s => s.status === 'active').length}
          </span>
          {strategyState().isLoading && (
            <span style={{
              "font-size": "0.875rem",
              color: "#3b82f6"
            }}>
              加载中...
            </span>
          )}
        </div>
      </div>

      <div style={{
        display: "grid",
        "grid-template-columns": "300px 1fr",
        gap: "24px"
      }}>
        {/* 策略列表 */}
        <div style={{
          padding: "24px",
          background: "white",
          "border-radius": "12px",
          border: "1px solid #e5e7eb"
        }}>
          <div style={{
            display: "flex",
            "align-items": "center",
            "justify-content": "space-between",
            "margin-bottom": "16px"
          }}>
            <h2 style={{
              "font-size": "1.25rem",
              "font-weight": "600",
              color: "#111827"
            }}>
              我的策略
            </h2>
            <button
              onClick={handleCreateStrategy}
              style={{
                padding: "8px 16px",
                background: "#3b82f6",
                color: "white",
                border: "none",
                "border-radius": "6px",
                "font-size": "14px",
                cursor: "pointer"
              }}
            >
              + 新建策略
            </button>
          </div>

          <div style={{
            display: "flex",
            "flex-direction": "column",
            gap: "8px"
          }}>
            <Show when={strategyState().strategies.length > 0} fallback={
              <div style={{
                padding: "24px",
                "text-align": "center",
                color: "#9ca3af"
              }}>
                <div style={{ "font-size": "2rem", "margin-bottom": "8px" }}>📝</div>
                <p>还没有策略</p>
                <p style={{ "font-size": "14px", "margin-top": "4px" }}>
                  点击"新建策略"开始创建您的第一个策略
                </p>
              </div>
            }>
              <For each={strategyState().strategies.slice(0, 10)}>
                {(strategy) => (
                  <div
                    style={{
                      padding: "12px",
                      background: selectedStrategy()?.id === strategy.id ? "#eff6ff" : "#f3f4f6",
                      "border-radius": "8px",
                      cursor: "pointer",
                      border: selectedStrategy()?.id === strategy.id ? "1px solid #3b82f6" : "1px solid transparent"
                    }}
                    onClick={() => handleSelectStrategy(strategy)}
                  >
                    <div style={{
                      display: "flex",
                      "align-items": "center",
                      "justify-content": "space-between",
                      "margin-bottom": "4px"
                    }}>
                      <div style={{
                        "font-weight": "500",
                        "font-size": "14px",
                        color: "#111827"
                      }}>
                        {strategy.name}
                      </div>
                      <div style={{
                        "font-size": "12px",
                        padding: "2px 8px",
                        "border-radius": "12px",
                        background: strategy.status === 'active' ? "#dcfce7" :
                                   strategy.status === 'paused' ? "#fef3c7" : "#f3f4f6",
                        color: strategy.status === 'active' ? "#166534" :
                               strategy.status === 'paused' ? "#92400e" : "#6b7280"
                      }}>
                        {strategy.status === 'active' ? '运行中' :
                         strategy.status === 'paused' ? '已暂停' : '已停止'}
                      </div>
                    </div>
                    <div style={{
                      "font-size": "12px",
                      color: "#6b7280",
                      "margin-bottom": "4px"
                    }}>
                      {strategy.description || '暂无描述'}
                    </div>
                    <div style={{
                      display: "flex",
                      "align-items": "center",
                      "justify-content": "space-between",
                      "font-size": "11px",
                      color: "#9ca3af"
                    }}>
                      <span>{strategy.type}</span>
                      <span>{new Date(strategy.updatedAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                )}
              </For>
            </Show>
          </div>
        </div>

        {/* 编辑器区域 */}
        <div style={{
          padding: "24px",
          background: "white",
          "border-radius": "12px",
          border: "1px solid #e5e7eb"
        }}>
          <div style={{
            display: "flex",
            "align-items": "center",
            "justify-content": "space-between",
            "margin-bottom": "24px"
          }}>
            <h2 style={{
              "font-size": "1.25rem",
              "font-weight": "600",
              color: "#111827"
            }}>
              策略编辑器
            </h2>

            <div style={{
              display: "flex",
              gap: "8px"
            }}>
              <button style={{
                padding: "8px 16px",
                background: "#22c55e",
                color: "white",
                border: "none",
                "border-radius": "6px",
                "font-size": "14px",
                cursor: "pointer"
              }}>
                保存
              </button>
              <button style={{
                padding: "8px 16px",
                background: "#3b82f6",
                color: "white",
                border: "none",
                "border-radius": "6px",
                "font-size": "14px",
                cursor: "pointer"
              }}>
                运行
              </button>
            </div>
          </div>

          {/* AI助手 */}
          <div style={{
            padding: "16px",
            background: "linear-gradient(to right, #dbeafe, #e0e7ff)",
            "border-radius": "8px",
            "margin-bottom": "24px"
          }}>
            <div style={{
              display: "flex",
              "align-items": "center",
              gap: "8px",
              "margin-bottom": "12px"
            }}>
              <span>🤖</span>
              <span style={{
                "font-size": "14px",
                "font-weight": "500",
                color: "#111827"
              }}>
                AI策略助手
              </span>
            </div>

            <div style={{
              display: "flex",
              gap: "8px"
            }}>
              <input
                type="text"
                placeholder="描述您想要的交易策略，例如：基于RSI的反转策略"
                style={{
                  flex: 1,
                  padding: "8px 12px",
                  border: "1px solid #d1d5db",
                  "border-radius": "6px",
                  "font-size": "14px"
                }}
              />
              <button style={{
                padding: "8px 16px",
                background: "#3b82f6",
                color: "white",
                border: "none",
                "border-radius": "6px",
                "font-size": "14px",
                cursor: "pointer"
              }}>
                生成策略
              </button>
            </div>
          </div>

          {/* 高级策略编辑器 */}
          <AdvancedStrategyEditor
            height={500}
            templates={strategyState().templates}
            onSave={(code, params) => {
              // 这里可以调用 strategyStore.updateStrategy 或 createStrategy
              console.log('保存策略', { code, params })
            }}
            onRun={async (code, params) => {
              // 最小闭环：创建一个回测任务（mock 或 API）
              try {
                await backtestApi.createBacktest({
                  name: `临时回测-${Date.now()}`,
                  strategyId: selectedStrategy()?.id || 'temp',
                  symbol: '000001',
                  startDate: '2023-01-01',
                  endDate: '2024-01-01',
                  parameters: params,
                } as any)
                alert('已提交回测任务')
              } catch (e) {
                console.error(e)
                alert('提交回测失败')
              }
            }}
          />
        </div>
      </div>
    </div>
  );
}


