export default function StrategyEditor() {
  return (
    <div style={{
      width: "100%",
      "max-width": "1280px",
      margin: "0 auto",
      padding: "0 16px 24px"
    }}>
      <div style={{
        "margin-bottom": "32px"
      }}>
        <h1 style={{
          "font-size": "2.5rem",
          "font-weight": "bold",
          color: "#111827",
          "margin-bottom": "8px"
        }}>
          策略编辑器
        </h1>
        <p style={{
          "font-size": "1.125rem",
          color: "#6b7280"
        }}>
          创建和编辑您的量化交易策略
        </p>
      </div>

      <div style={{
        display: "grid",
        "grid-template-columns": "300px 1fr",
        gap: "24px"
      }}>
        {/* 策略列表 */}
        <div style={{
          padding: "24px",
          background: "white",
          "border-radius": "12px",
          border: "1px solid #e5e7eb"
        }}>
          <h2 style={{
            "font-size": "1.25rem",
            "font-weight": "600",
            color: "#111827",
            "margin-bottom": "16px"
          }}>
            我的策略
          </h2>

          <div style={{
            display: "flex",
            "flex-direction": "column",
            gap: "8px"
          }}>
            <div style={{
              padding: "12px",
              background: "#f3f4f6",
              "border-radius": "8px",
              cursor: "pointer"
            }}>
              <div style={{
                "font-weight": "500",
                "font-size": "14px",
                color: "#111827"
              }}>
                双均线策略
              </div>
              <div style={{
                "font-size": "12px",
                color: "#6b7280"
              }}>
                基于移动平均线的趋势跟踪
              </div>
            </div>

            <div style={{
              padding: "12px",
              background: "#f3f4f6",
              "border-radius": "8px",
              cursor: "pointer"
            }}>
              <div style={{
                "font-weight": "500",
                "font-size": "14px",
                color: "#111827"
              }}>
                RSI反转策略
              </div>
              <div style={{
                "font-size": "12px",
                color: "#6b7280"
              }}>
                基于RSI指标的均值回归
              </div>
            </div>
          </div>
        </div>

        {/* 编辑器区域 */}
        <div style={{
          padding: "24px",
          background: "white",
          "border-radius": "12px",
          border: "1px solid #e5e7eb"
        }}>
          <div style={{
            display: "flex",
            "align-items": "center",
            "justify-content": "space-between",
            "margin-bottom": "24px"
          }}>
            <h2 style={{
              "font-size": "1.25rem",
              "font-weight": "600",
              color: "#111827"
            }}>
              策略编辑器
            </h2>

            <div style={{
              display: "flex",
              gap: "8px"
            }}>
              <button style={{
                padding: "8px 16px",
                background: "#22c55e",
                color: "white",
                border: "none",
                "border-radius": "6px",
                "font-size": "14px",
                cursor: "pointer"
              }}>
                保存
              </button>
              <button style={{
                padding: "8px 16px",
                background: "#3b82f6",
                color: "white",
                border: "none",
                "border-radius": "6px",
                "font-size": "14px",
                cursor: "pointer"
              }}>
                运行
              </button>
            </div>
          </div>

          {/* AI助手 */}
          <div style={{
            padding: "16px",
            background: "linear-gradient(to right, #dbeafe, #e0e7ff)",
            "border-radius": "8px",
            "margin-bottom": "24px"
          }}>
            <div style={{
              display: "flex",
              "align-items": "center",
              gap: "8px",
              "margin-bottom": "12px"
            }}>
              <span>🤖</span>
              <span style={{
                "font-size": "14px",
                "font-weight": "500",
                color: "#111827"
              }}>
                AI策略助手
              </span>
            </div>

            <div style={{
              display: "flex",
              gap: "8px"
            }}>
              <input
                type="text"
                placeholder="描述您想要的交易策略，例如：基于RSI的反转策略"
                style={{
                  flex: 1,
                  padding: "8px 12px",
                  border: "1px solid #d1d5db",
                  "border-radius": "6px",
                  "font-size": "14px"
                }}
              />
              <button style={{
                padding: "8px 16px",
                background: "#3b82f6",
                color: "white",
                border: "none",
                "border-radius": "6px",
                "font-size": "14px",
                cursor: "pointer"
              }}>
                生成策略
              </button>
            </div>
          </div>

          {/* 代码编辑器占位 */}
          <div style={{
            height: "400px",
            background: "#f9fafb",
            "border-radius": "8px",
            border: "1px solid #e5e7eb",
            display: "flex",
            "align-items": "center",
            "justify-content": "center"
          }}>
            <div style={{
              "text-align": "center",
              color: "#9ca3af"
            }}>
              <div style={{ "font-size": "3rem", "margin-bottom": "12px" }}>💻</div>
              <p>代码编辑器</p>
              <p style={{ "font-size": "14px", "margin-top": "4px" }}>
                (代码编辑器将在后续版本中实现)
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}


