import { useAtom, useAtomValue } from 'jotai';
import { createSignal, For, Show } from 'solid-js';
import { 
  strategiesAtom, 
  currentStrategyAtom, 
  deleteStrategyAtom,
  strategyTemplatesAtom 
} from '@/stores/strategy';
import AIStrategyEditor from '@/components/AIStrategyEditor';
import { css } from '../../styled-system/css';
import { FiPlus, FiEdit3, FiTrash2, FiCode, FiPlay, FiCopy } from 'solid-icons/fi';

export default function StrategyEditor() {
  const strategies = useAtomValue(strategiesAtom);
  const [currentStrategy, setCurrentStrategy] = useAtom(currentStrategyAtom);
  const [, deleteStrategy] = useAtom(deleteStrategyAtom);
  const templates = useAtomValue(strategyTemplatesAtom);
  
  const [showSidebar, setShowSidebar] = createSignal(true);
  
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  const handleDeleteStrategy = (id: string) => {
    if (confirm('确定要删除这个策略吗？')) {
      deleteStrategy(id);
    }
  };
  
  const createNewStrategy = () => {
    setCurrentStrategy(null);
  };
  
  return (
    <div class={css({
      display: 'flex',
      h: 'calc(100vh - 120px)', // 减去header和padding的高度
      gap: 6
    })}>
      {/* 侧边栏 */}
      <Show when={showSidebar()}>
        <div class={css({
          w: '320px',
          bg: 'white',
          rounded: 'xl',
          border: '1px solid',
          borderColor: 'gray.200',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          _dark: {
            bg: 'gray.800',
            borderColor: 'gray.700'
          }
        })}>
          {/* 侧边栏头部 */}
          <div class={css({
            p: 4,
            borderBottom: '1px solid',
            borderColor: 'gray.200',
            _dark: {
              borderColor: 'gray.700'
            }
          })}>
            <div class={css({
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              mb: 3
            })}>
              <h2 class={css({
                fontSize: 'lg',
                fontWeight: 'semibold',
                color: 'gray.900',
                _dark: { color: 'gray.100' }
              })}>
                我的策略
              </h2>
              
              <button
                onClick={createNewStrategy}
                class={css({
                  p: 2,
                  bg: 'primary.500',
                  color: 'white',
                  rounded: 'md',
                  transition: 'all 0.2s ease',
                  _hover: {
                    bg: 'primary.600'
                  }
                })}
                title="新建策略"
              >
                <FiPlus size={16} />
              </button>
            </div>
            
            <div class={css({
              fontSize: 'sm',
              color: 'gray.600',
              _dark: { color: 'gray.400' }
            })}>
              共 {strategies.length} 个策略
            </div>
          </div>
          
          {/* 策略列表 */}
          <div class={css({
            flex: 1,
            overflow: 'auto'
          })}>
            <Show 
              when={strategies.length > 0}
              fallback={
                <div class={css({
                  p: 6,
                  textAlign: 'center',
                  color: 'gray.500',
                  _dark: { color: 'gray.400' }
                })}>
                  <FiCode size={48} class={css({ mx: 'auto', mb: 3, opacity: 0.5 })} />
                  <p class={css({ fontSize: 'sm' })}>
                    还没有策略
                  </p>
                  <p class={css({ fontSize: 'xs', mt: 1 })}>
                    点击右上角的 + 号创建第一个策略
                  </p>
                </div>
              }
            >
              <div class={css({ p: 2 })}>
                <For each={strategies}>
                  {(strategy) => {
                    const isSelected = () => currentStrategy()?.id === strategy.id;
                    
                    return (
                      <div
                        class={css({
                          p: 3,
                          mb: 2,
                          rounded: 'lg',
                          cursor: 'pointer',
                          transition: 'all 0.2s ease',
                          border: '2px solid',
                          borderColor: isSelected() ? 'primary.500' : 'transparent',
                          bg: isSelected() ? 'primary.50' : 'transparent',
                          _hover: {
                            bg: isSelected() ? 'primary.100' : 'gray.50'
                          },
                          _dark: {
                            bg: isSelected() ? 'primary.900/30' : 'transparent',
                            _hover: {
                              bg: isSelected() ? 'primary.900/50' : 'gray.700'
                            }
                          }
                        })}
                        onClick={() => setCurrentStrategy(strategy)}
                      >
                        <div class={css({
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          mb: 2
                        })}>
                          <h3 class={css({
                            fontSize: 'sm',
                            fontWeight: 'medium',
                            color: 'gray.900',
                            truncate: true,
                            _dark: { color: 'gray.100' }
                          })}>
                            {strategy.name}
                          </h3>
                          
                          <div class={css({
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1
                          })}>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setCurrentStrategy(strategy);
                              }}
                              class={css({
                                p: 1,
                                color: 'gray.500',
                                rounded: 'sm',
                                transition: 'all 0.2s ease',
                                _hover: {
                                  color: 'primary.600',
                                  bg: 'primary.100'
                                }
                              })}
                              title="编辑策略"
                            >
                              <FiEdit3 size={12} />
                            </button>
                            
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteStrategy(strategy.id);
                              }}
                              class={css({
                                p: 1,
                                color: 'gray.500',
                                rounded: 'sm',
                                transition: 'all 0.2s ease',
                                _hover: {
                                  color: 'danger.600',
                                  bg: 'danger.100'
                                }
                              })}
                              title="删除策略"
                            >
                              <FiTrash2 size={12} />
                            </button>
                          </div>
                        </div>
                        
                        <p class={css({
                          fontSize: 'xs',
                          color: 'gray.600',
                          mb: 2,
                          lineHeight: 1.4,
                          _dark: { color: 'gray.400' }
                        })}>
                          {strategy.description || '暂无描述'}
                        </p>
                        
                        <div class={css({
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          fontSize: 'xs',
                          color: 'gray.500',
                          _dark: { color: 'gray.500' }
                        })}>
                          <span>{formatDate(strategy.updatedAt)}</span>
                          <div class={css({
                            display: 'flex',
                            alignItems: 'center',
                            gap: 2
                          })}>
                            <span class={css({
                              px: 2,
                              py: 0.5,
                              bg: 'gray.200',
                              rounded: 'full',
                              fontSize: '10px',
                              _dark: {
                                bg: 'gray.600'
                              }
                            })}>
                              {strategy.language}
                            </span>
                            {strategy.isPublic && (
                              <span class={css({
                                px: 2,
                                py: 0.5,
                                bg: 'success.200',
                                color: 'success.800',
                                rounded: 'full',
                                fontSize: '10px',
                                _dark: {
                                  bg: 'success.900/30',
                                  color: 'success.300'
                                }
                              })}>
                                公开
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  }}
                </For>
              </div>
            </Show>
          </div>
          
          {/* 策略模板快捷入口 */}
          <div class={css({
            p: 4,
            borderTop: '1px solid',
            borderColor: 'gray.200',
            _dark: {
              borderColor: 'gray.700'
            }
          })}>
            <h3 class={css({
              fontSize: 'sm',
              fontWeight: 'medium',
              color: 'gray.900',
              mb: 3,
              _dark: { color: 'gray.100' }
            })}>
              策略模板
            </h3>
            
            <div class={css({
              display: 'grid',
              gridTemplateColumns: 'repeat(2, 1fr)',
              gap: 2
            })}>
              <For each={templates.slice(0, 4)}>
                {(template) => (
                  <button
                    class={css({
                      p: 2,
                      bg: 'gray.50',
                      rounded: 'md',
                      fontSize: 'xs',
                      color: 'gray.700',
                      textAlign: 'left',
                      transition: 'all 0.2s ease',
                      _hover: {
                        bg: 'gray.100'
                      },
                      _dark: {
                        bg: 'gray.700',
                        color: 'gray.300',
                        _hover: {
                          bg: 'gray.600'
                        }
                      }
                    })}
                    title={template.description}
                  >
                    <FiCopy size={12} class={css({ mb: 1 })} />
                    <div class={css({ truncate: true })}>
                      {template.name}
                    </div>
                  </button>
                )}
              </For>
            </div>
          </div>
        </div>
      </Show>
      
      {/* 主编辑区域 */}
      <div class={css({
        flex: 1,
        display: 'flex',
        flexDirection: 'column'
      })}>
        {/* 工具栏 */}
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: 4
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            gap: 3
          })}>
            <button
              onClick={() => setShowSidebar(!showSidebar())}
              class={css({
                px: 3,
                py: 2,
                bg: 'gray.100',
                color: 'gray.700',
                rounded: 'md',
                fontSize: 'sm',
                transition: 'all 0.2s ease',
                _hover: {
                  bg: 'gray.200'
                },
                _dark: {
                  bg: 'gray.700',
                  color: 'gray.300',
                  _hover: {
                    bg: 'gray.600'
                  }
                }
              })}
            >
              {showSidebar() ? '隐藏' : '显示'} 侧边栏
            </button>
            
            <Show when={currentStrategy()}>
              <div class={css({
                fontSize: 'sm',
                color: 'gray.600',
                _dark: { color: 'gray.400' }
              })}>
                正在编辑: <span class={css({ fontWeight: 'medium' })}>{currentStrategy()!.name}</span>
              </div>
            </Show>
          </div>
          
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            gap: 2
          })}>
            <button
              class={css({
                px: 4,
                py: 2,
                bg: 'success.500',
                color: 'white',
                rounded: 'md',
                fontSize: 'sm',
                fontWeight: 'medium',
                transition: 'all 0.2s ease',
                _hover: {
                  bg: 'success.600'
                }
              })}
            >
              <FiPlay size={14} class={css({ mr: 2 })} />
              运行回测
            </button>
          </div>
        </div>
        
        {/* AI策略编辑器 */}
        <div class={css({ flex: 1 })}>
          <AIStrategyEditor />
        </div>
      </div>
    </div>
  );
}
