import { A } from '@solidjs/router';
import { useTheme } from '@/context/ThemeContext';
import { css } from '../../styled-system/css';
import { FiSun, FiMoon, FiBarChart3, FiTrendingUp, FiCode, FiPieChart } from 'solid-icons/fi';

export default function Header() {
  const { theme, toggleTheme } = useTheme();
  
  const navItems = [
    { path: '/', label: '仪表盘', icon: FiBarChart3 },
    { path: '/market', label: '市场数据', icon: FiTrendingUp },
    { path: '/strategy', label: '策略编辑', icon: FiCode },
    { path: '/backtest', label: '回测分析', icon: FiPieChart },
  ];
  
  return (
    <header class={css({
      bg: 'white',
      borderBottom: '1px solid',
      borderColor: 'gray.200',
      shadow: 'sm',
      position: 'sticky',
      top: 0,
      zIndex: 50,
      _dark: {
        bg: 'gray.800',
        borderColor: 'gray.700'
      }
    })}>
      <div class={css({
        container: 'xl',
        mx: 'auto',
        px: 4,
        py: 3,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      })}>
        {/* Logo */}
        <A href="/" class={css({
          display: 'flex',
          alignItems: 'center',
          gap: 3,
          fontSize: 'xl',
          fontWeight: 'bold',
          color: 'primary.600',
          textDecoration: 'none',
          _dark: {
            color: 'primary.400'
          }
        })}>
          <div class={css({
            w: 8,
            h: 8,
            bg: 'gradient-to-br',
            gradientFrom: 'primary.500',
            gradientTo: 'primary.700',
            rounded: 'lg',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: 'sm',
            fontWeight: 'bold'
          })}>
            Q
          </div>
          量化交易平台
        </A>
        
        {/* 导航菜单 */}
        <nav class={css({
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          hideBelow: 'md'
        })}>
          {navItems.map((item) => (
            <A
              href={item.path}
              class={css({
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                px: 3,
                py: 2,
                rounded: 'md',
                fontSize: 'sm',
                fontWeight: 'medium',
                color: 'gray.600',
                textDecoration: 'none',
                transition: 'all 0.2s ease',
                _hover: {
                  bg: 'gray.100',
                  color: 'gray.900'
                },
                _dark: {
                  color: 'gray.300',
                  _hover: {
                    bg: 'gray.700',
                    color: 'white'
                  }
                },
                '&.active': {
                  bg: 'primary.50',
                  color: 'primary.700',
                  _dark: {
                    bg: 'primary.900/30',
                    color: 'primary.300'
                  }
                }
              })}
            >
              <item.icon size={16} />
              {item.label}
            </A>
          ))}
        </nav>
        
        {/* 右侧操作区 */}
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          gap: 3
        })}>
          {/* 连接状态指示器 */}
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            px: 2,
            py: 1,
            rounded: 'full',
            bg: 'success.50',
            color: 'success.700',
            fontSize: 'xs',
            fontWeight: 'medium',
            _dark: {
              bg: 'success.900/30',
              color: 'success.300'
            }
          })}>
            <div class={css({
              w: 2,
              h: 2,
              bg: 'success.500',
              rounded: 'full',
              animation: 'pulse-slow'
            })} />
            已连接
          </div>
          
          {/* 主题切换按钮 */}
          <button
            onClick={toggleTheme}
            class={css({
              p: 2,
              rounded: 'md',
              color: 'gray.600',
              transition: 'all 0.2s ease',
              _hover: {
                bg: 'gray.100',
                color: 'gray.900'
              },
              _dark: {
                color: 'gray.300',
                _hover: {
                  bg: 'gray.700',
                  color: 'white'
                }
              }
            })}
            title={theme() === 'light' ? '切换到深色模式' : '切换到浅色模式'}
          >
            {theme() === 'light' ? <FiMoon size={18} /> : <FiSun size={18} />}
          </button>
          
          {/* 移动端菜单按钮 */}
          <button class={css({
            p: 2,
            rounded: 'md',
            color: 'gray.600',
            transition: 'all 0.2s ease',
            hideFrom: 'md',
            _hover: {
              bg: 'gray.100',
              color: 'gray.900'
            },
            _dark: {
              color: 'gray.300',
              _hover: {
                bg: 'gray.700',
                color: 'white'
              }
            }
          })}>
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <line x1="3" y1="12" x2="21" y2="12"></line>
              <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>
    </header>
  );
}
