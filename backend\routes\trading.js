/**
 * 交易功能路由
 */
import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { authenticateToken } from './auth.js';

const router = express.Router();

// 模拟交易数据
const orders = new Map();
const positions = new Map();
const tradeHistory = [];

// 初始化一些持仓数据
positions.set('pos_1', {
  id: 'pos_1',
  userId: 1,
  symbol: '000001',
  name: '平安银行',
  quantity: 10000,
  avgPrice: 12.50,
  currentPrice: 13.19,
  marketValue: 131900,
  profit: 6900,
  profitPercent: 5.52,
  side: 'long',
  openTime: '2024-01-15T09:30:00Z',
  lastUpdate: new Date().toISOString()
});

positions.set('pos_2', {
  id: 'pos_2',
  userId: 1,
  symbol: '600036',
  name: '招商银行',
  quantity: 5000,
  avgPrice: 35.20,
  currentPrice: 36.80,
  marketValue: 184000,
  profit: 8000,
  profitPercent: 4.55,
  side: 'long',
  openTime: '2024-01-20T14:15:00Z',
  lastUpdate: new Date().toISOString()
});

/**
 * 获取持仓列表
 */
router.get('/positions', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    
    // 筛选用户持仓
    const userPositions = Array.from(positions.values())
      .filter(pos => pos.userId === userId);
    
    // 计算总计数据
    const totalMarketValue = userPositions.reduce((sum, pos) => sum + pos.marketValue, 0);
    const totalProfit = userPositions.reduce((sum, pos) => sum + pos.profit, 0);
    const totalProfitPercent = totalMarketValue > 0 ? (totalProfit / (totalMarketValue - totalProfit)) * 100 : 0;
    
    res.json({
      success: true,
      data: {
        positions: userPositions,
        summary: {
          totalPositions: userPositions.length,
          totalMarketValue: Number(totalMarketValue.toFixed(2)),
          totalProfit: Number(totalProfit.toFixed(2)),
          totalProfitPercent: Number(totalProfitPercent.toFixed(2))
        }
      }
    });
    
  } catch (error) {
    console.error('获取持仓错误:', error);
    res.status(500).json({
      success: false,
      message: '获取持仓信息失败',
      code: 'POSITIONS_ERROR'
    });
  }
});

/**
 * 获取订单列表
 */
router.get('/orders', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { status, symbol, startDate, endDate, page = 1, limit = 20 } = req.query;
    
    let userOrders = Array.from(orders.values())
      .filter(order => order.userId === userId);
    
    // 筛选条件
    if (status) {
      userOrders = userOrders.filter(order => order.status === status);
    }
    
    if (symbol) {
      userOrders = userOrders.filter(order => order.symbol === symbol);
    }
    
    if (startDate) {
      userOrders = userOrders.filter(order => 
        new Date(order.createTime) >= new Date(startDate)
      );
    }
    
    if (endDate) {
      userOrders = userOrders.filter(order => 
        new Date(order.createTime) <= new Date(endDate)
      );
    }
    
    // 排序（最新的在前）
    userOrders.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
    
    // 分页
    const offset = (parseInt(page) - 1) * parseInt(limit);
    const paginatedOrders = userOrders.slice(offset, offset + parseInt(limit));
    
    res.json({
      success: true,
      data: {
        orders: paginatedOrders,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: userOrders.length,
          pages: Math.ceil(userOrders.length / parseInt(limit))
        }
      }
    });
    
  } catch (error) {
    console.error('获取订单错误:', error);
    res.status(500).json({
      success: false,
      message: '获取订单信息失败',
      code: 'ORDERS_ERROR'
    });
  }
});

/**
 * 下单
 */
router.post('/order', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { symbol, side, orderType, quantity, price, stopPrice, timeInForce = 'GTC' } = req.body;
    
    // 验证参数
    if (!symbol || !side || !orderType || !quantity) {
      return res.status(400).json({
        success: false,
        message: '订单参数不完整',
        code: 'INVALID_ORDER_PARAMS'
      });
    }
    
    if (!['buy', 'sell'].includes(side)) {
      return res.status(400).json({
        success: false,
        message: '无效的交易方向',
        code: 'INVALID_SIDE'
      });
    }
    
    if (!['market', 'limit', 'stop', 'stop_limit'].includes(orderType)) {
      return res.status(400).json({
        success: false,
        message: '无效的订单类型',
        code: 'INVALID_ORDER_TYPE'
      });
    }
    
    if (quantity <= 0) {
      return res.status(400).json({
        success: false,
        message: '订单数量必须大于0',
        code: 'INVALID_QUANTITY'
      });
    }
    
    // 限价和止损订单需要价格
    if (['limit', 'stop_limit'].includes(orderType) && !price) {
      return res.status(400).json({
        success: false,
        message: '限价订单需要指定价格',
        code: 'PRICE_REQUIRED'
      });
    }
    
    // 止损订单需要止损价格
    if (['stop', 'stop_limit'].includes(orderType) && !stopPrice) {
      return res.status(400).json({
        success: false,
        message: '止损订单需要指定止损价格',
        code: 'STOP_PRICE_REQUIRED'
      });
    }
    
    // 创建订单
    const orderId = `order_${uuidv4()}`;
    const order = {
      id: orderId,
      userId,
      symbol,
      side,
      orderType,
      quantity: parseInt(quantity),
      price: price ? parseFloat(price) : null,
      stopPrice: stopPrice ? parseFloat(stopPrice) : null,
      timeInForce,
      status: 'pending',
      filledQuantity: 0,
      remainingQuantity: parseInt(quantity),
      avgFillPrice: 0,
      commission: 0,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      trades: []
    };
    
    // 模拟订单处理
    setTimeout(() => {
      processOrder(order);
    }, 1000 + Math.random() * 3000); // 1-4秒后处理
    
    orders.set(orderId, order);
    
    res.status(201).json({
      success: true,
      message: '订单提交成功',
      data: {
        order
      }
    });
    
  } catch (error) {
    console.error('下单错误:', error);
    res.status(500).json({
      success: false,
      message: '订单提交失败',
      code: 'ORDER_SUBMIT_ERROR'
    });
  }
});

/**
 * 模拟订单处理
 */
function processOrder(order) {
  try {
    // 90% 概率成功，10% 概率失败
    const success = Math.random() > 0.1;
    
    if (success) {
      // 模拟成交
      const fillPrice = order.price || (10 + Math.random() * 50); // 模拟市价
      const commission = order.quantity * fillPrice * 0.0003; // 0.03% 手续费
      
      order.status = 'filled';
      order.filledQuantity = order.quantity;
      order.remainingQuantity = 0;
      order.avgFillPrice = fillPrice;
      order.commission = commission;
      order.updateTime = new Date().toISOString();
      
      // 创建成交记录
      const trade = {
        id: `trade_${uuidv4()}`,
        orderId: order.id,
        symbol: order.symbol,
        side: order.side,
        quantity: order.quantity,
        price: fillPrice,
        commission,
        timestamp: new Date().toISOString()
      };
      
      order.trades.push(trade);
      tradeHistory.push(trade);
      
      console.log(`✅ 订单成交: ${order.id} - ${order.symbol} ${order.side} ${order.quantity}@${fillPrice}`);
      
    } else {
      // 订单失败
      order.status = 'rejected';
      order.updateTime = new Date().toISOString();
      
      console.log(`❌ 订单被拒: ${order.id} - 市场条件不满足`);
    }
    
    orders.set(order.id, order);
    
  } catch (error) {
    console.error('订单处理错误:', error);
    order.status = 'error';
    order.updateTime = new Date().toISOString();
    orders.set(order.id, order);
  }
}

/**
 * 取消订单
 */
router.delete('/order/:orderId', authenticateToken, async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.id;
    
    const order = orders.get(orderId);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在',
        code: 'ORDER_NOT_FOUND'
      });
    }
    
    if (order.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: '无权操作此订单',
        code: 'INSUFFICIENT_PERMISSION'
      });
    }
    
    if (!['pending', 'partial_filled'].includes(order.status)) {
      return res.status(400).json({
        success: false,
        message: '订单状态不允许取消',
        code: 'CANNOT_CANCEL'
      });
    }
    
    // 取消订单
    order.status = 'cancelled';
    order.updateTime = new Date().toISOString();
    orders.set(orderId, order);
    
    res.json({
      success: true,
      message: '订单取消成功',
      data: {
        order
      }
    });
    
  } catch (error) {
    console.error('取消订单错误:', error);
    res.status(500).json({
      success: false,
      message: '取消订单失败',
      code: 'CANCEL_ORDER_ERROR'
    });
  }
});

/**
 * 获取订单详情
 */
router.get('/order/:orderId', authenticateToken, async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.id;
    
    const order = orders.get(orderId);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在',
        code: 'ORDER_NOT_FOUND'
      });
    }
    
    if (order.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: '无权查看此订单',
        code: 'INSUFFICIENT_PERMISSION'
      });
    }
    
    res.json({
      success: true,
      data: {
        order
      }
    });
    
  } catch (error) {
    console.error('获取订单详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取订单详情失败',
      code: 'ORDER_DETAIL_ERROR'
    });
  }
});

/**
 * 获取成交历史
 */
router.get('/trades', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { symbol, startDate, endDate, page = 1, limit = 50 } = req.query;
    
    // 获取用户的成交记录
    let userTrades = tradeHistory.filter(trade => {
      const order = orders.get(trade.orderId);
      return order && order.userId === userId;
    });
    
    // 筛选条件
    if (symbol) {
      userTrades = userTrades.filter(trade => trade.symbol === symbol);
    }
    
    if (startDate) {
      userTrades = userTrades.filter(trade => 
        new Date(trade.timestamp) >= new Date(startDate)
      );
    }
    
    if (endDate) {
      userTrades = userTrades.filter(trade => 
        new Date(trade.timestamp) <= new Date(endDate)
      );
    }
    
    // 排序（最新的在前）
    userTrades.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    // 分页
    const offset = (parseInt(page) - 1) * parseInt(limit);
    const paginatedTrades = userTrades.slice(offset, offset + parseInt(limit));
    
    res.json({
      success: true,
      data: {
        trades: paginatedTrades,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: userTrades.length,
          pages: Math.ceil(userTrades.length / parseInt(limit))
        }
      }
    });
    
  } catch (error) {
    console.error('获取成交历史错误:', error);
    res.status(500).json({
      success: false,
      message: '获取成交历史失败',
      code: 'TRADES_ERROR'
    });
  }
});

/**
 * 获取账户资金信息
 */
router.get('/account', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    
    // 模拟账户资金数据
    const account = {
      userId,
      totalAssets: 1234567.89,      // 总资产
      availableFunds: 500000.00,    // 可用资金
      frozenFunds: 50000.00,        // 冻结资金
      marketValue: 684567.89,       // 持仓市值
      totalProfit: 84567.89,        // 总盈亏
      todayProfit: 8765.43,         // 今日盈亏
      profitPercent: 7.35,          // 盈亏比例
      riskLevel: 'medium',          // 风险等级
      margin: 0,                    // 保证金
      currency: 'CNY',              // 货币单位
      updateTime: new Date().toISOString()
    };
    
    res.json({
      success: true,
      data: {
        account
      }
    });
    
  } catch (error) {
    console.error('获取账户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '获取账户信息失败',
      code: 'ACCOUNT_ERROR'
    });
  }
});

export default router;