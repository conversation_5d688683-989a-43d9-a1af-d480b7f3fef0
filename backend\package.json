{"name": "quant-platform-backend", "version": "1.0.0", "description": "量化投资平台后端API服务", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js"}, "keywords": ["quantitative-trading", "api", "nodejs", "express", "websocket"], "author": "Quant Platform Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2", "uuid": "^9.0.1", "csv-parser": "^3.0.0", "fs-extra": "^11.1.1", "lodash": "^4.17.21", "moment": "^2.29.4", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "engines": {"node": ">=18.0.0"}}