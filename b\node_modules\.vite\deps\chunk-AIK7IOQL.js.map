{"version": 3, "sources": ["../../@codemirror/search/dist/index.js"], "sourcesContent": ["import { show<PERSON><PERSON>l, Editor<PERSON>iew, getPanel, Decoration, ViewPlugin, runScopeHandlers } from '@codemirror/view';\nimport { codePointAt, fromCodePoint, codePointSize, StateEffect, StateField, EditorSelection, Facet, combineConfig, CharCategory, RangeSetBuilder, Prec, EditorState, findClusterBreak } from '@codemirror/state';\nimport elt from 'crelt';\n\nconst basicNormalize = typeof String.prototype.normalize == \"function\"\n    ? x => x.normalize(\"NFKD\") : x => x;\n/**\nA search cursor provides an iterator over text matches in a\ndocument.\n*/\nclass SearchCursor {\n    /**\n    Create a text cursor. The query is the search string, `from` to\n    `to` provides the region to search.\n    \n    When `normalize` is given, it will be called, on both the query\n    string and the content it is matched against, before comparing.\n    You can, for example, create a case-insensitive search by\n    passing `s => s.toLowerCase()`.\n    \n    Text is always normalized with\n    [`.normalize(\"NFKD\")`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/normalize)\n    (when supported).\n    */\n    constructor(text, query, from = 0, to = text.length, normalize, test) {\n        this.test = test;\n        /**\n        The current match (only holds a meaningful value after\n        [`next`](https://codemirror.net/6/docs/ref/#search.SearchCursor.next) has been called and when\n        `done` is false).\n        */\n        this.value = { from: 0, to: 0 };\n        /**\n        Whether the end of the iterated region has been reached.\n        */\n        this.done = false;\n        this.matches = [];\n        this.buffer = \"\";\n        this.bufferPos = 0;\n        this.iter = text.iterRange(from, to);\n        this.bufferStart = from;\n        this.normalize = normalize ? x => normalize(basicNormalize(x)) : basicNormalize;\n        this.query = this.normalize(query);\n    }\n    peek() {\n        if (this.bufferPos == this.buffer.length) {\n            this.bufferStart += this.buffer.length;\n            this.iter.next();\n            if (this.iter.done)\n                return -1;\n            this.bufferPos = 0;\n            this.buffer = this.iter.value;\n        }\n        return codePointAt(this.buffer, this.bufferPos);\n    }\n    /**\n    Look for the next match. Updates the iterator's\n    [`value`](https://codemirror.net/6/docs/ref/#search.SearchCursor.value) and\n    [`done`](https://codemirror.net/6/docs/ref/#search.SearchCursor.done) properties. Should be called\n    at least once before using the cursor.\n    */\n    next() {\n        while (this.matches.length)\n            this.matches.pop();\n        return this.nextOverlapping();\n    }\n    /**\n    The `next` method will ignore matches that partially overlap a\n    previous match. This method behaves like `next`, but includes\n    such matches.\n    */\n    nextOverlapping() {\n        for (;;) {\n            let next = this.peek();\n            if (next < 0) {\n                this.done = true;\n                return this;\n            }\n            let str = fromCodePoint(next), start = this.bufferStart + this.bufferPos;\n            this.bufferPos += codePointSize(next);\n            let norm = this.normalize(str);\n            if (norm.length)\n                for (let i = 0, pos = start;; i++) {\n                    let code = norm.charCodeAt(i);\n                    let match = this.match(code, pos, this.bufferPos + this.bufferStart);\n                    if (i == norm.length - 1) {\n                        if (match) {\n                            this.value = match;\n                            return this;\n                        }\n                        break;\n                    }\n                    if (pos == start && i < str.length && str.charCodeAt(i) == code)\n                        pos++;\n                }\n        }\n    }\n    match(code, pos, end) {\n        let match = null;\n        for (let i = 0; i < this.matches.length; i += 2) {\n            let index = this.matches[i], keep = false;\n            if (this.query.charCodeAt(index) == code) {\n                if (index == this.query.length - 1) {\n                    match = { from: this.matches[i + 1], to: end };\n                }\n                else {\n                    this.matches[i]++;\n                    keep = true;\n                }\n            }\n            if (!keep) {\n                this.matches.splice(i, 2);\n                i -= 2;\n            }\n        }\n        if (this.query.charCodeAt(0) == code) {\n            if (this.query.length == 1)\n                match = { from: pos, to: end };\n            else\n                this.matches.push(1, pos);\n        }\n        if (match && this.test && !this.test(match.from, match.to, this.buffer, this.bufferStart))\n            match = null;\n        return match;\n    }\n}\nif (typeof Symbol != \"undefined\")\n    SearchCursor.prototype[Symbol.iterator] = function () { return this; };\n\nconst empty = { from: -1, to: -1, match: /*@__PURE__*//.*/.exec(\"\") };\nconst baseFlags = \"gm\" + (/x/.unicode == null ? \"\" : \"u\");\n/**\nThis class is similar to [`SearchCursor`](https://codemirror.net/6/docs/ref/#search.SearchCursor)\nbut searches for a regular expression pattern instead of a plain\nstring.\n*/\nclass RegExpCursor {\n    /**\n    Create a cursor that will search the given range in the given\n    document. `query` should be the raw pattern (as you'd pass it to\n    `new RegExp`).\n    */\n    constructor(text, query, options, from = 0, to = text.length) {\n        this.text = text;\n        this.to = to;\n        this.curLine = \"\";\n        /**\n        Set to `true` when the cursor has reached the end of the search\n        range.\n        */\n        this.done = false;\n        /**\n        Will contain an object with the extent of the match and the\n        match object when [`next`](https://codemirror.net/6/docs/ref/#search.RegExpCursor.next)\n        sucessfully finds a match.\n        */\n        this.value = empty;\n        if (/\\\\[sWDnr]|\\n|\\r|\\[\\^/.test(query))\n            return new MultilineRegExpCursor(text, query, options, from, to);\n        this.re = new RegExp(query, baseFlags + ((options === null || options === void 0 ? void 0 : options.ignoreCase) ? \"i\" : \"\"));\n        this.test = options === null || options === void 0 ? void 0 : options.test;\n        this.iter = text.iter();\n        let startLine = text.lineAt(from);\n        this.curLineStart = startLine.from;\n        this.matchPos = toCharEnd(text, from);\n        this.getLine(this.curLineStart);\n    }\n    getLine(skip) {\n        this.iter.next(skip);\n        if (this.iter.lineBreak) {\n            this.curLine = \"\";\n        }\n        else {\n            this.curLine = this.iter.value;\n            if (this.curLineStart + this.curLine.length > this.to)\n                this.curLine = this.curLine.slice(0, this.to - this.curLineStart);\n            this.iter.next();\n        }\n    }\n    nextLine() {\n        this.curLineStart = this.curLineStart + this.curLine.length + 1;\n        if (this.curLineStart > this.to)\n            this.curLine = \"\";\n        else\n            this.getLine(0);\n    }\n    /**\n    Move to the next match, if there is one.\n    */\n    next() {\n        for (let off = this.matchPos - this.curLineStart;;) {\n            this.re.lastIndex = off;\n            let match = this.matchPos <= this.to && this.re.exec(this.curLine);\n            if (match) {\n                let from = this.curLineStart + match.index, to = from + match[0].length;\n                this.matchPos = toCharEnd(this.text, to + (from == to ? 1 : 0));\n                if (from == this.curLineStart + this.curLine.length)\n                    this.nextLine();\n                if ((from < to || from > this.value.to) && (!this.test || this.test(from, to, match))) {\n                    this.value = { from, to, match };\n                    return this;\n                }\n                off = this.matchPos - this.curLineStart;\n            }\n            else if (this.curLineStart + this.curLine.length < this.to) {\n                this.nextLine();\n                off = 0;\n            }\n            else {\n                this.done = true;\n                return this;\n            }\n        }\n    }\n}\nconst flattened = /*@__PURE__*/new WeakMap();\n// Reusable (partially) flattened document strings\nclass FlattenedDoc {\n    constructor(from, text) {\n        this.from = from;\n        this.text = text;\n    }\n    get to() { return this.from + this.text.length; }\n    static get(doc, from, to) {\n        let cached = flattened.get(doc);\n        if (!cached || cached.from >= to || cached.to <= from) {\n            let flat = new FlattenedDoc(from, doc.sliceString(from, to));\n            flattened.set(doc, flat);\n            return flat;\n        }\n        if (cached.from == from && cached.to == to)\n            return cached;\n        let { text, from: cachedFrom } = cached;\n        if (cachedFrom > from) {\n            text = doc.sliceString(from, cachedFrom) + text;\n            cachedFrom = from;\n        }\n        if (cached.to < to)\n            text += doc.sliceString(cached.to, to);\n        flattened.set(doc, new FlattenedDoc(cachedFrom, text));\n        return new FlattenedDoc(from, text.slice(from - cachedFrom, to - cachedFrom));\n    }\n}\nclass MultilineRegExpCursor {\n    constructor(text, query, options, from, to) {\n        this.text = text;\n        this.to = to;\n        this.done = false;\n        this.value = empty;\n        this.matchPos = toCharEnd(text, from);\n        this.re = new RegExp(query, baseFlags + ((options === null || options === void 0 ? void 0 : options.ignoreCase) ? \"i\" : \"\"));\n        this.test = options === null || options === void 0 ? void 0 : options.test;\n        this.flat = FlattenedDoc.get(text, from, this.chunkEnd(from + 5000 /* Chunk.Base */));\n    }\n    chunkEnd(pos) {\n        return pos >= this.to ? this.to : this.text.lineAt(pos).to;\n    }\n    next() {\n        for (;;) {\n            let off = this.re.lastIndex = this.matchPos - this.flat.from;\n            let match = this.re.exec(this.flat.text);\n            // Skip empty matches directly after the last match\n            if (match && !match[0] && match.index == off) {\n                this.re.lastIndex = off + 1;\n                match = this.re.exec(this.flat.text);\n            }\n            if (match) {\n                let from = this.flat.from + match.index, to = from + match[0].length;\n                // If a match goes almost to the end of a noncomplete chunk, try\n                // again, since it'll likely be able to match more\n                if ((this.flat.to >= this.to || match.index + match[0].length <= this.flat.text.length - 10) &&\n                    (!this.test || this.test(from, to, match))) {\n                    this.value = { from, to, match };\n                    this.matchPos = toCharEnd(this.text, to + (from == to ? 1 : 0));\n                    return this;\n                }\n            }\n            if (this.flat.to == this.to) {\n                this.done = true;\n                return this;\n            }\n            // Grow the flattened doc\n            this.flat = FlattenedDoc.get(this.text, this.flat.from, this.chunkEnd(this.flat.from + this.flat.text.length * 2));\n        }\n    }\n}\nif (typeof Symbol != \"undefined\") {\n    RegExpCursor.prototype[Symbol.iterator] = MultilineRegExpCursor.prototype[Symbol.iterator] =\n        function () { return this; };\n}\nfunction validRegExp(source) {\n    try {\n        new RegExp(source, baseFlags);\n        return true;\n    }\n    catch (_a) {\n        return false;\n    }\n}\nfunction toCharEnd(text, pos) {\n    if (pos >= text.length)\n        return pos;\n    let line = text.lineAt(pos), next;\n    while (pos < line.to && (next = line.text.charCodeAt(pos - line.from)) >= 0xDC00 && next < 0xE000)\n        pos++;\n    return pos;\n}\n\nfunction createLineDialog(view) {\n    let line = String(view.state.doc.lineAt(view.state.selection.main.head).number);\n    let input = elt(\"input\", { class: \"cm-textfield\", name: \"line\", value: line });\n    let dom = elt(\"form\", {\n        class: \"cm-gotoLine\",\n        onkeydown: (event) => {\n            if (event.keyCode == 27) { // Escape\n                event.preventDefault();\n                view.dispatch({ effects: dialogEffect.of(false) });\n                view.focus();\n            }\n            else if (event.keyCode == 13) { // Enter\n                event.preventDefault();\n                go();\n            }\n        },\n        onsubmit: (event) => {\n            event.preventDefault();\n            go();\n        }\n    }, elt(\"label\", view.state.phrase(\"Go to line\"), \": \", input), \" \", elt(\"button\", { class: \"cm-button\", type: \"submit\" }, view.state.phrase(\"go\")), elt(\"button\", {\n        name: \"close\",\n        onclick: () => {\n            view.dispatch({ effects: dialogEffect.of(false) });\n            view.focus();\n        },\n        \"aria-label\": view.state.phrase(\"close\"),\n        type: \"button\"\n    }, [\"×\"]));\n    function go() {\n        let match = /^([+-])?(\\d+)?(:\\d+)?(%)?$/.exec(input.value);\n        if (!match)\n            return;\n        let { state } = view, startLine = state.doc.lineAt(state.selection.main.head);\n        let [, sign, ln, cl, percent] = match;\n        let col = cl ? +cl.slice(1) : 0;\n        let line = ln ? +ln : startLine.number;\n        if (ln && percent) {\n            let pc = line / 100;\n            if (sign)\n                pc = pc * (sign == \"-\" ? -1 : 1) + (startLine.number / state.doc.lines);\n            line = Math.round(state.doc.lines * pc);\n        }\n        else if (ln && sign) {\n            line = line * (sign == \"-\" ? -1 : 1) + startLine.number;\n        }\n        let docLine = state.doc.line(Math.max(1, Math.min(state.doc.lines, line)));\n        let selection = EditorSelection.cursor(docLine.from + Math.max(0, Math.min(col, docLine.length)));\n        view.dispatch({\n            effects: [dialogEffect.of(false), EditorView.scrollIntoView(selection.from, { y: 'center' })],\n            selection,\n        });\n        view.focus();\n    }\n    return { dom };\n}\nconst dialogEffect = /*@__PURE__*/StateEffect.define();\nconst dialogField = /*@__PURE__*/StateField.define({\n    create() { return true; },\n    update(value, tr) {\n        for (let e of tr.effects)\n            if (e.is(dialogEffect))\n                value = e.value;\n        return value;\n    },\n    provide: f => showPanel.from(f, val => val ? createLineDialog : null)\n});\n/**\nCommand that shows a dialog asking the user for a line number, and\nwhen a valid position is provided, moves the cursor to that line.\n\nSupports line numbers, relative line offsets prefixed with `+` or\n`-`, document percentages suffixed with `%`, and an optional\ncolumn position by adding `:` and a second number after the line\nnumber.\n*/\nconst gotoLine = view => {\n    let panel = getPanel(view, createLineDialog);\n    if (!panel) {\n        let effects = [dialogEffect.of(true)];\n        if (view.state.field(dialogField, false) == null)\n            effects.push(StateEffect.appendConfig.of([dialogField, baseTheme$1]));\n        view.dispatch({ effects });\n        panel = getPanel(view, createLineDialog);\n    }\n    if (panel)\n        panel.dom.querySelector(\"input\").select();\n    return true;\n};\nconst baseTheme$1 = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-panel.cm-gotoLine\": {\n        padding: \"2px 6px 4px\",\n        position: \"relative\",\n        \"& label\": { fontSize: \"80%\" },\n        \"& [name=close]\": {\n            position: \"absolute\",\n            top: \"0\", bottom: \"0\",\n            right: \"4px\",\n            backgroundColor: \"inherit\",\n            border: \"none\",\n            font: \"inherit\",\n            padding: \"0\"\n        }\n    }\n});\n\nconst defaultHighlightOptions = {\n    highlightWordAroundCursor: false,\n    minSelectionLength: 1,\n    maxMatches: 100,\n    wholeWords: false\n};\nconst highlightConfig = /*@__PURE__*/Facet.define({\n    combine(options) {\n        return combineConfig(options, defaultHighlightOptions, {\n            highlightWordAroundCursor: (a, b) => a || b,\n            minSelectionLength: Math.min,\n            maxMatches: Math.min\n        });\n    }\n});\n/**\nThis extension highlights text that matches the selection. It uses\nthe `\"cm-selectionMatch\"` class for the highlighting. When\n`highlightWordAroundCursor` is enabled, the word at the cursor\nitself will be highlighted with `\"cm-selectionMatch-main\"`.\n*/\nfunction highlightSelectionMatches(options) {\n    let ext = [defaultTheme, matchHighlighter];\n    if (options)\n        ext.push(highlightConfig.of(options));\n    return ext;\n}\nconst matchDeco = /*@__PURE__*/Decoration.mark({ class: \"cm-selectionMatch\" });\nconst mainMatchDeco = /*@__PURE__*/Decoration.mark({ class: \"cm-selectionMatch cm-selectionMatch-main\" });\n// Whether the characters directly outside the given positions are non-word characters\nfunction insideWordBoundaries(check, state, from, to) {\n    return (from == 0 || check(state.sliceDoc(from - 1, from)) != CharCategory.Word) &&\n        (to == state.doc.length || check(state.sliceDoc(to, to + 1)) != CharCategory.Word);\n}\n// Whether the characters directly at the given positions are word characters\nfunction insideWord(check, state, from, to) {\n    return check(state.sliceDoc(from, from + 1)) == CharCategory.Word\n        && check(state.sliceDoc(to - 1, to)) == CharCategory.Word;\n}\nconst matchHighlighter = /*@__PURE__*/ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.decorations = this.getDeco(view);\n    }\n    update(update) {\n        if (update.selectionSet || update.docChanged || update.viewportChanged)\n            this.decorations = this.getDeco(update.view);\n    }\n    getDeco(view) {\n        let conf = view.state.facet(highlightConfig);\n        let { state } = view, sel = state.selection;\n        if (sel.ranges.length > 1)\n            return Decoration.none;\n        let range = sel.main, query, check = null;\n        if (range.empty) {\n            if (!conf.highlightWordAroundCursor)\n                return Decoration.none;\n            let word = state.wordAt(range.head);\n            if (!word)\n                return Decoration.none;\n            check = state.charCategorizer(range.head);\n            query = state.sliceDoc(word.from, word.to);\n        }\n        else {\n            let len = range.to - range.from;\n            if (len < conf.minSelectionLength || len > 200)\n                return Decoration.none;\n            if (conf.wholeWords) {\n                query = state.sliceDoc(range.from, range.to); // TODO: allow and include leading/trailing space?\n                check = state.charCategorizer(range.head);\n                if (!(insideWordBoundaries(check, state, range.from, range.to) &&\n                    insideWord(check, state, range.from, range.to)))\n                    return Decoration.none;\n            }\n            else {\n                query = state.sliceDoc(range.from, range.to);\n                if (!query)\n                    return Decoration.none;\n            }\n        }\n        let deco = [];\n        for (let part of view.visibleRanges) {\n            let cursor = new SearchCursor(state.doc, query, part.from, part.to);\n            while (!cursor.next().done) {\n                let { from, to } = cursor.value;\n                if (!check || insideWordBoundaries(check, state, from, to)) {\n                    if (range.empty && from <= range.from && to >= range.to)\n                        deco.push(mainMatchDeco.range(from, to));\n                    else if (from >= range.to || to <= range.from)\n                        deco.push(matchDeco.range(from, to));\n                    if (deco.length > conf.maxMatches)\n                        return Decoration.none;\n                }\n            }\n        }\n        return Decoration.set(deco);\n    }\n}, {\n    decorations: v => v.decorations\n});\nconst defaultTheme = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-selectionMatch\": { backgroundColor: \"#99ff7780\" },\n    \".cm-searchMatch .cm-selectionMatch\": { backgroundColor: \"transparent\" }\n});\n// Select the words around the cursors.\nconst selectWord = ({ state, dispatch }) => {\n    let { selection } = state;\n    let newSel = EditorSelection.create(selection.ranges.map(range => state.wordAt(range.head) || EditorSelection.cursor(range.head)), selection.mainIndex);\n    if (newSel.eq(selection))\n        return false;\n    dispatch(state.update({ selection: newSel }));\n    return true;\n};\n// Find next occurrence of query relative to last cursor. Wrap around\n// the document if there are no more matches.\nfunction findNextOccurrence(state, query) {\n    let { main, ranges } = state.selection;\n    let word = state.wordAt(main.head), fullWord = word && word.from == main.from && word.to == main.to;\n    for (let cycled = false, cursor = new SearchCursor(state.doc, query, ranges[ranges.length - 1].to);;) {\n        cursor.next();\n        if (cursor.done) {\n            if (cycled)\n                return null;\n            cursor = new SearchCursor(state.doc, query, 0, Math.max(0, ranges[ranges.length - 1].from - 1));\n            cycled = true;\n        }\n        else {\n            if (cycled && ranges.some(r => r.from == cursor.value.from))\n                continue;\n            if (fullWord) {\n                let word = state.wordAt(cursor.value.from);\n                if (!word || word.from != cursor.value.from || word.to != cursor.value.to)\n                    continue;\n            }\n            return cursor.value;\n        }\n    }\n}\n/**\nSelect next occurrence of the current selection. Expand selection\nto the surrounding word when the selection is empty.\n*/\nconst selectNextOccurrence = ({ state, dispatch }) => {\n    let { ranges } = state.selection;\n    if (ranges.some(sel => sel.from === sel.to))\n        return selectWord({ state, dispatch });\n    let searchedText = state.sliceDoc(ranges[0].from, ranges[0].to);\n    if (state.selection.ranges.some(r => state.sliceDoc(r.from, r.to) != searchedText))\n        return false;\n    let range = findNextOccurrence(state, searchedText);\n    if (!range)\n        return false;\n    dispatch(state.update({\n        selection: state.selection.addRange(EditorSelection.range(range.from, range.to), false),\n        effects: EditorView.scrollIntoView(range.to)\n    }));\n    return true;\n};\n\nconst searchConfigFacet = /*@__PURE__*/Facet.define({\n    combine(configs) {\n        return combineConfig(configs, {\n            top: false,\n            caseSensitive: false,\n            literal: false,\n            regexp: false,\n            wholeWord: false,\n            createPanel: view => new SearchPanel(view),\n            scrollToMatch: range => EditorView.scrollIntoView(range)\n        });\n    }\n});\n/**\nAdd search state to the editor configuration, and optionally\nconfigure the search extension.\n([`openSearchPanel`](https://codemirror.net/6/docs/ref/#search.openSearchPanel) will automatically\nenable this if it isn't already on).\n*/\nfunction search(config) {\n    return config ? [searchConfigFacet.of(config), searchExtensions] : searchExtensions;\n}\n/**\nA search query. Part of the editor's search state.\n*/\nclass SearchQuery {\n    /**\n    Create a query object.\n    */\n    constructor(config) {\n        this.search = config.search;\n        this.caseSensitive = !!config.caseSensitive;\n        this.literal = !!config.literal;\n        this.regexp = !!config.regexp;\n        this.replace = config.replace || \"\";\n        this.valid = !!this.search && (!this.regexp || validRegExp(this.search));\n        this.unquoted = this.unquote(this.search);\n        this.wholeWord = !!config.wholeWord;\n    }\n    /**\n    @internal\n    */\n    unquote(text) {\n        return this.literal ? text :\n            text.replace(/\\\\([nrt\\\\])/g, (_, ch) => ch == \"n\" ? \"\\n\" : ch == \"r\" ? \"\\r\" : ch == \"t\" ? \"\\t\" : \"\\\\\");\n    }\n    /**\n    Compare this query to another query.\n    */\n    eq(other) {\n        return this.search == other.search && this.replace == other.replace &&\n            this.caseSensitive == other.caseSensitive && this.regexp == other.regexp &&\n            this.wholeWord == other.wholeWord;\n    }\n    /**\n    @internal\n    */\n    create() {\n        return this.regexp ? new RegExpQuery(this) : new StringQuery(this);\n    }\n    /**\n    Get a search cursor for this query, searching through the given\n    range in the given state.\n    */\n    getCursor(state, from = 0, to) {\n        let st = state.doc ? state : EditorState.create({ doc: state });\n        if (to == null)\n            to = st.doc.length;\n        return this.regexp ? regexpCursor(this, st, from, to) : stringCursor(this, st, from, to);\n    }\n}\nclass QueryType {\n    constructor(spec) {\n        this.spec = spec;\n    }\n}\nfunction stringCursor(spec, state, from, to) {\n    return new SearchCursor(state.doc, spec.unquoted, from, to, spec.caseSensitive ? undefined : x => x.toLowerCase(), spec.wholeWord ? stringWordTest(state.doc, state.charCategorizer(state.selection.main.head)) : undefined);\n}\nfunction stringWordTest(doc, categorizer) {\n    return (from, to, buf, bufPos) => {\n        if (bufPos > from || bufPos + buf.length < to) {\n            bufPos = Math.max(0, from - 2);\n            buf = doc.sliceString(bufPos, Math.min(doc.length, to + 2));\n        }\n        return (categorizer(charBefore(buf, from - bufPos)) != CharCategory.Word ||\n            categorizer(charAfter(buf, from - bufPos)) != CharCategory.Word) &&\n            (categorizer(charAfter(buf, to - bufPos)) != CharCategory.Word ||\n                categorizer(charBefore(buf, to - bufPos)) != CharCategory.Word);\n    };\n}\nclass StringQuery extends QueryType {\n    constructor(spec) {\n        super(spec);\n    }\n    nextMatch(state, curFrom, curTo) {\n        let cursor = stringCursor(this.spec, state, curTo, state.doc.length).nextOverlapping();\n        if (cursor.done) {\n            let end = Math.min(state.doc.length, curFrom + this.spec.unquoted.length);\n            cursor = stringCursor(this.spec, state, 0, end).nextOverlapping();\n        }\n        return cursor.done || cursor.value.from == curFrom && cursor.value.to == curTo ? null : cursor.value;\n    }\n    // Searching in reverse is, rather than implementing an inverted search\n    // cursor, done by scanning chunk after chunk forward.\n    prevMatchInRange(state, from, to) {\n        for (let pos = to;;) {\n            let start = Math.max(from, pos - 10000 /* FindPrev.ChunkSize */ - this.spec.unquoted.length);\n            let cursor = stringCursor(this.spec, state, start, pos), range = null;\n            while (!cursor.nextOverlapping().done)\n                range = cursor.value;\n            if (range)\n                return range;\n            if (start == from)\n                return null;\n            pos -= 10000 /* FindPrev.ChunkSize */;\n        }\n    }\n    prevMatch(state, curFrom, curTo) {\n        let found = this.prevMatchInRange(state, 0, curFrom);\n        if (!found)\n            found = this.prevMatchInRange(state, Math.max(0, curTo - this.spec.unquoted.length), state.doc.length);\n        return found && (found.from != curFrom || found.to != curTo) ? found : null;\n    }\n    getReplacement(_result) { return this.spec.unquote(this.spec.replace); }\n    matchAll(state, limit) {\n        let cursor = stringCursor(this.spec, state, 0, state.doc.length), ranges = [];\n        while (!cursor.next().done) {\n            if (ranges.length >= limit)\n                return null;\n            ranges.push(cursor.value);\n        }\n        return ranges;\n    }\n    highlight(state, from, to, add) {\n        let cursor = stringCursor(this.spec, state, Math.max(0, from - this.spec.unquoted.length), Math.min(to + this.spec.unquoted.length, state.doc.length));\n        while (!cursor.next().done)\n            add(cursor.value.from, cursor.value.to);\n    }\n}\nfunction regexpCursor(spec, state, from, to) {\n    return new RegExpCursor(state.doc, spec.search, {\n        ignoreCase: !spec.caseSensitive,\n        test: spec.wholeWord ? regexpWordTest(state.charCategorizer(state.selection.main.head)) : undefined\n    }, from, to);\n}\nfunction charBefore(str, index) {\n    return str.slice(findClusterBreak(str, index, false), index);\n}\nfunction charAfter(str, index) {\n    return str.slice(index, findClusterBreak(str, index));\n}\nfunction regexpWordTest(categorizer) {\n    return (_from, _to, match) => !match[0].length ||\n        (categorizer(charBefore(match.input, match.index)) != CharCategory.Word ||\n            categorizer(charAfter(match.input, match.index)) != CharCategory.Word) &&\n            (categorizer(charAfter(match.input, match.index + match[0].length)) != CharCategory.Word ||\n                categorizer(charBefore(match.input, match.index + match[0].length)) != CharCategory.Word);\n}\nclass RegExpQuery extends QueryType {\n    nextMatch(state, curFrom, curTo) {\n        let cursor = regexpCursor(this.spec, state, curTo, state.doc.length).next();\n        if (cursor.done)\n            cursor = regexpCursor(this.spec, state, 0, curFrom).next();\n        return cursor.done ? null : cursor.value;\n    }\n    prevMatchInRange(state, from, to) {\n        for (let size = 1;; size++) {\n            let start = Math.max(from, to - size * 10000 /* FindPrev.ChunkSize */);\n            let cursor = regexpCursor(this.spec, state, start, to), range = null;\n            while (!cursor.next().done)\n                range = cursor.value;\n            if (range && (start == from || range.from > start + 10))\n                return range;\n            if (start == from)\n                return null;\n        }\n    }\n    prevMatch(state, curFrom, curTo) {\n        return this.prevMatchInRange(state, 0, curFrom) ||\n            this.prevMatchInRange(state, curTo, state.doc.length);\n    }\n    getReplacement(result) {\n        return this.spec.unquote(this.spec.replace).replace(/\\$([$&]|\\d+)/g, (m, i) => {\n            if (i == \"&\")\n                return result.match[0];\n            if (i == \"$\")\n                return \"$\";\n            for (let l = i.length; l > 0; l--) {\n                let n = +i.slice(0, l);\n                if (n > 0 && n < result.match.length)\n                    return result.match[n] + i.slice(l);\n            }\n            return m;\n        });\n    }\n    matchAll(state, limit) {\n        let cursor = regexpCursor(this.spec, state, 0, state.doc.length), ranges = [];\n        while (!cursor.next().done) {\n            if (ranges.length >= limit)\n                return null;\n            ranges.push(cursor.value);\n        }\n        return ranges;\n    }\n    highlight(state, from, to, add) {\n        let cursor = regexpCursor(this.spec, state, Math.max(0, from - 250 /* RegExp.HighlightMargin */), Math.min(to + 250 /* RegExp.HighlightMargin */, state.doc.length));\n        while (!cursor.next().done)\n            add(cursor.value.from, cursor.value.to);\n    }\n}\n/**\nA state effect that updates the current search query. Note that\nthis only has an effect if the search state has been initialized\n(by including [`search`](https://codemirror.net/6/docs/ref/#search.search) in your configuration or\nby running [`openSearchPanel`](https://codemirror.net/6/docs/ref/#search.openSearchPanel) at least\nonce).\n*/\nconst setSearchQuery = /*@__PURE__*/StateEffect.define();\nconst togglePanel = /*@__PURE__*/StateEffect.define();\nconst searchState = /*@__PURE__*/StateField.define({\n    create(state) {\n        return new SearchState(defaultQuery(state).create(), null);\n    },\n    update(value, tr) {\n        for (let effect of tr.effects) {\n            if (effect.is(setSearchQuery))\n                value = new SearchState(effect.value.create(), value.panel);\n            else if (effect.is(togglePanel))\n                value = new SearchState(value.query, effect.value ? createSearchPanel : null);\n        }\n        return value;\n    },\n    provide: f => showPanel.from(f, val => val.panel)\n});\n/**\nGet the current search query from an editor state.\n*/\nfunction getSearchQuery(state) {\n    let curState = state.field(searchState, false);\n    return curState ? curState.query.spec : defaultQuery(state);\n}\n/**\nQuery whether the search panel is open in the given editor state.\n*/\nfunction searchPanelOpen(state) {\n    var _a;\n    return ((_a = state.field(searchState, false)) === null || _a === void 0 ? void 0 : _a.panel) != null;\n}\nclass SearchState {\n    constructor(query, panel) {\n        this.query = query;\n        this.panel = panel;\n    }\n}\nconst matchMark = /*@__PURE__*/Decoration.mark({ class: \"cm-searchMatch\" }), selectedMatchMark = /*@__PURE__*/Decoration.mark({ class: \"cm-searchMatch cm-searchMatch-selected\" });\nconst searchHighlighter = /*@__PURE__*/ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.view = view;\n        this.decorations = this.highlight(view.state.field(searchState));\n    }\n    update(update) {\n        let state = update.state.field(searchState);\n        if (state != update.startState.field(searchState) || update.docChanged || update.selectionSet || update.viewportChanged)\n            this.decorations = this.highlight(state);\n    }\n    highlight({ query, panel }) {\n        if (!panel || !query.spec.valid)\n            return Decoration.none;\n        let { view } = this;\n        let builder = new RangeSetBuilder();\n        for (let i = 0, ranges = view.visibleRanges, l = ranges.length; i < l; i++) {\n            let { from, to } = ranges[i];\n            while (i < l - 1 && to > ranges[i + 1].from - 2 * 250 /* RegExp.HighlightMargin */)\n                to = ranges[++i].to;\n            query.highlight(view.state, from, to, (from, to) => {\n                let selected = view.state.selection.ranges.some(r => r.from == from && r.to == to);\n                builder.add(from, to, selected ? selectedMatchMark : matchMark);\n            });\n        }\n        return builder.finish();\n    }\n}, {\n    decorations: v => v.decorations\n});\nfunction searchCommand(f) {\n    return view => {\n        let state = view.state.field(searchState, false);\n        return state && state.query.spec.valid ? f(view, state) : openSearchPanel(view);\n    };\n}\n/**\nOpen the search panel if it isn't already open, and move the\nselection to the first match after the current main selection.\nWill wrap around to the start of the document when it reaches the\nend.\n*/\nconst findNext = /*@__PURE__*/searchCommand((view, { query }) => {\n    let { to } = view.state.selection.main;\n    let next = query.nextMatch(view.state, to, to);\n    if (!next)\n        return false;\n    let selection = EditorSelection.single(next.from, next.to);\n    let config = view.state.facet(searchConfigFacet);\n    view.dispatch({\n        selection,\n        effects: [announceMatch(view, next), config.scrollToMatch(selection.main, view)],\n        userEvent: \"select.search\"\n    });\n    selectSearchInput(view);\n    return true;\n});\n/**\nMove the selection to the previous instance of the search query,\nbefore the current main selection. Will wrap past the start\nof the document to start searching at the end again.\n*/\nconst findPrevious = /*@__PURE__*/searchCommand((view, { query }) => {\n    let { state } = view, { from } = state.selection.main;\n    let prev = query.prevMatch(state, from, from);\n    if (!prev)\n        return false;\n    let selection = EditorSelection.single(prev.from, prev.to);\n    let config = view.state.facet(searchConfigFacet);\n    view.dispatch({\n        selection,\n        effects: [announceMatch(view, prev), config.scrollToMatch(selection.main, view)],\n        userEvent: \"select.search\"\n    });\n    selectSearchInput(view);\n    return true;\n});\n/**\nSelect all instances of the search query.\n*/\nconst selectMatches = /*@__PURE__*/searchCommand((view, { query }) => {\n    let ranges = query.matchAll(view.state, 1000);\n    if (!ranges || !ranges.length)\n        return false;\n    view.dispatch({\n        selection: EditorSelection.create(ranges.map(r => EditorSelection.range(r.from, r.to))),\n        userEvent: \"select.search.matches\"\n    });\n    return true;\n});\n/**\nSelect all instances of the currently selected text.\n*/\nconst selectSelectionMatches = ({ state, dispatch }) => {\n    let sel = state.selection;\n    if (sel.ranges.length > 1 || sel.main.empty)\n        return false;\n    let { from, to } = sel.main;\n    let ranges = [], main = 0;\n    for (let cur = new SearchCursor(state.doc, state.sliceDoc(from, to)); !cur.next().done;) {\n        if (ranges.length > 1000)\n            return false;\n        if (cur.value.from == from)\n            main = ranges.length;\n        ranges.push(EditorSelection.range(cur.value.from, cur.value.to));\n    }\n    dispatch(state.update({\n        selection: EditorSelection.create(ranges, main),\n        userEvent: \"select.search.matches\"\n    }));\n    return true;\n};\n/**\nReplace the current match of the search query.\n*/\nconst replaceNext = /*@__PURE__*/searchCommand((view, { query }) => {\n    let { state } = view, { from, to } = state.selection.main;\n    if (state.readOnly)\n        return false;\n    let match = query.nextMatch(state, from, from);\n    if (!match)\n        return false;\n    let next = match;\n    let changes = [], selection, replacement;\n    let effects = [];\n    if (next.from == from && next.to == to) {\n        replacement = state.toText(query.getReplacement(next));\n        changes.push({ from: next.from, to: next.to, insert: replacement });\n        next = query.nextMatch(state, next.from, next.to);\n        effects.push(EditorView.announce.of(state.phrase(\"replaced match on line $\", state.doc.lineAt(from).number) + \".\"));\n    }\n    let changeSet = view.state.changes(changes);\n    if (next) {\n        selection = EditorSelection.single(next.from, next.to).map(changeSet);\n        effects.push(announceMatch(view, next));\n        effects.push(state.facet(searchConfigFacet).scrollToMatch(selection.main, view));\n    }\n    view.dispatch({\n        changes: changeSet,\n        selection,\n        effects,\n        userEvent: \"input.replace\"\n    });\n    return true;\n});\n/**\nReplace all instances of the search query with the given\nreplacement.\n*/\nconst replaceAll = /*@__PURE__*/searchCommand((view, { query }) => {\n    if (view.state.readOnly)\n        return false;\n    let changes = query.matchAll(view.state, 1e9).map(match => {\n        let { from, to } = match;\n        return { from, to, insert: query.getReplacement(match) };\n    });\n    if (!changes.length)\n        return false;\n    let announceText = view.state.phrase(\"replaced $ matches\", changes.length) + \".\";\n    view.dispatch({\n        changes,\n        effects: EditorView.announce.of(announceText),\n        userEvent: \"input.replace.all\"\n    });\n    return true;\n});\nfunction createSearchPanel(view) {\n    return view.state.facet(searchConfigFacet).createPanel(view);\n}\nfunction defaultQuery(state, fallback) {\n    var _a, _b, _c, _d, _e;\n    let sel = state.selection.main;\n    let selText = sel.empty || sel.to > sel.from + 100 ? \"\" : state.sliceDoc(sel.from, sel.to);\n    if (fallback && !selText)\n        return fallback;\n    let config = state.facet(searchConfigFacet);\n    return new SearchQuery({\n        search: ((_a = fallback === null || fallback === void 0 ? void 0 : fallback.literal) !== null && _a !== void 0 ? _a : config.literal) ? selText : selText.replace(/\\n/g, \"\\\\n\"),\n        caseSensitive: (_b = fallback === null || fallback === void 0 ? void 0 : fallback.caseSensitive) !== null && _b !== void 0 ? _b : config.caseSensitive,\n        literal: (_c = fallback === null || fallback === void 0 ? void 0 : fallback.literal) !== null && _c !== void 0 ? _c : config.literal,\n        regexp: (_d = fallback === null || fallback === void 0 ? void 0 : fallback.regexp) !== null && _d !== void 0 ? _d : config.regexp,\n        wholeWord: (_e = fallback === null || fallback === void 0 ? void 0 : fallback.wholeWord) !== null && _e !== void 0 ? _e : config.wholeWord\n    });\n}\nfunction getSearchInput(view) {\n    let panel = getPanel(view, createSearchPanel);\n    return panel && panel.dom.querySelector(\"[main-field]\");\n}\nfunction selectSearchInput(view) {\n    let input = getSearchInput(view);\n    if (input && input == view.root.activeElement)\n        input.select();\n}\n/**\nMake sure the search panel is open and focused.\n*/\nconst openSearchPanel = view => {\n    let state = view.state.field(searchState, false);\n    if (state && state.panel) {\n        let searchInput = getSearchInput(view);\n        if (searchInput && searchInput != view.root.activeElement) {\n            let query = defaultQuery(view.state, state.query.spec);\n            if (query.valid)\n                view.dispatch({ effects: setSearchQuery.of(query) });\n            searchInput.focus();\n            searchInput.select();\n        }\n    }\n    else {\n        view.dispatch({ effects: [\n                togglePanel.of(true),\n                state ? setSearchQuery.of(defaultQuery(view.state, state.query.spec)) : StateEffect.appendConfig.of(searchExtensions)\n            ] });\n    }\n    return true;\n};\n/**\nClose the search panel.\n*/\nconst closeSearchPanel = view => {\n    let state = view.state.field(searchState, false);\n    if (!state || !state.panel)\n        return false;\n    let panel = getPanel(view, createSearchPanel);\n    if (panel && panel.dom.contains(view.root.activeElement))\n        view.focus();\n    view.dispatch({ effects: togglePanel.of(false) });\n    return true;\n};\n/**\nDefault search-related key bindings.\n\n - Mod-f: [`openSearchPanel`](https://codemirror.net/6/docs/ref/#search.openSearchPanel)\n - F3, Mod-g: [`findNext`](https://codemirror.net/6/docs/ref/#search.findNext)\n - Shift-F3, Shift-Mod-g: [`findPrevious`](https://codemirror.net/6/docs/ref/#search.findPrevious)\n - Mod-Alt-g: [`gotoLine`](https://codemirror.net/6/docs/ref/#search.gotoLine)\n - Mod-d: [`selectNextOccurrence`](https://codemirror.net/6/docs/ref/#search.selectNextOccurrence)\n*/\nconst searchKeymap = [\n    { key: \"Mod-f\", run: openSearchPanel, scope: \"editor search-panel\" },\n    { key: \"F3\", run: findNext, shift: findPrevious, scope: \"editor search-panel\", preventDefault: true },\n    { key: \"Mod-g\", run: findNext, shift: findPrevious, scope: \"editor search-panel\", preventDefault: true },\n    { key: \"Escape\", run: closeSearchPanel, scope: \"editor search-panel\" },\n    { key: \"Mod-Shift-l\", run: selectSelectionMatches },\n    { key: \"Mod-Alt-g\", run: gotoLine },\n    { key: \"Mod-d\", run: selectNextOccurrence, preventDefault: true },\n];\nclass SearchPanel {\n    constructor(view) {\n        this.view = view;\n        let query = this.query = view.state.field(searchState).query.spec;\n        this.commit = this.commit.bind(this);\n        this.searchField = elt(\"input\", {\n            value: query.search,\n            placeholder: phrase(view, \"Find\"),\n            \"aria-label\": phrase(view, \"Find\"),\n            class: \"cm-textfield\",\n            name: \"search\",\n            form: \"\",\n            \"main-field\": \"true\",\n            onchange: this.commit,\n            onkeyup: this.commit\n        });\n        this.replaceField = elt(\"input\", {\n            value: query.replace,\n            placeholder: phrase(view, \"Replace\"),\n            \"aria-label\": phrase(view, \"Replace\"),\n            class: \"cm-textfield\",\n            name: \"replace\",\n            form: \"\",\n            onchange: this.commit,\n            onkeyup: this.commit\n        });\n        this.caseField = elt(\"input\", {\n            type: \"checkbox\",\n            name: \"case\",\n            form: \"\",\n            checked: query.caseSensitive,\n            onchange: this.commit\n        });\n        this.reField = elt(\"input\", {\n            type: \"checkbox\",\n            name: \"re\",\n            form: \"\",\n            checked: query.regexp,\n            onchange: this.commit\n        });\n        this.wordField = elt(\"input\", {\n            type: \"checkbox\",\n            name: \"word\",\n            form: \"\",\n            checked: query.wholeWord,\n            onchange: this.commit\n        });\n        function button(name, onclick, content) {\n            return elt(\"button\", { class: \"cm-button\", name, onclick, type: \"button\" }, content);\n        }\n        this.dom = elt(\"div\", { onkeydown: (e) => this.keydown(e), class: \"cm-search\" }, [\n            this.searchField,\n            button(\"next\", () => findNext(view), [phrase(view, \"next\")]),\n            button(\"prev\", () => findPrevious(view), [phrase(view, \"previous\")]),\n            button(\"select\", () => selectMatches(view), [phrase(view, \"all\")]),\n            elt(\"label\", null, [this.caseField, phrase(view, \"match case\")]),\n            elt(\"label\", null, [this.reField, phrase(view, \"regexp\")]),\n            elt(\"label\", null, [this.wordField, phrase(view, \"by word\")]),\n            ...view.state.readOnly ? [] : [\n                elt(\"br\"),\n                this.replaceField,\n                button(\"replace\", () => replaceNext(view), [phrase(view, \"replace\")]),\n                button(\"replaceAll\", () => replaceAll(view), [phrase(view, \"replace all\")])\n            ],\n            elt(\"button\", {\n                name: \"close\",\n                onclick: () => closeSearchPanel(view),\n                \"aria-label\": phrase(view, \"close\"),\n                type: \"button\"\n            }, [\"×\"])\n        ]);\n    }\n    commit() {\n        let query = new SearchQuery({\n            search: this.searchField.value,\n            caseSensitive: this.caseField.checked,\n            regexp: this.reField.checked,\n            wholeWord: this.wordField.checked,\n            replace: this.replaceField.value,\n        });\n        if (!query.eq(this.query)) {\n            this.query = query;\n            this.view.dispatch({ effects: setSearchQuery.of(query) });\n        }\n    }\n    keydown(e) {\n        if (runScopeHandlers(this.view, e, \"search-panel\")) {\n            e.preventDefault();\n        }\n        else if (e.keyCode == 13 && e.target == this.searchField) {\n            e.preventDefault();\n            (e.shiftKey ? findPrevious : findNext)(this.view);\n        }\n        else if (e.keyCode == 13 && e.target == this.replaceField) {\n            e.preventDefault();\n            replaceNext(this.view);\n        }\n    }\n    update(update) {\n        for (let tr of update.transactions)\n            for (let effect of tr.effects) {\n                if (effect.is(setSearchQuery) && !effect.value.eq(this.query))\n                    this.setQuery(effect.value);\n            }\n    }\n    setQuery(query) {\n        this.query = query;\n        this.searchField.value = query.search;\n        this.replaceField.value = query.replace;\n        this.caseField.checked = query.caseSensitive;\n        this.reField.checked = query.regexp;\n        this.wordField.checked = query.wholeWord;\n    }\n    mount() {\n        this.searchField.select();\n    }\n    get pos() { return 80; }\n    get top() { return this.view.state.facet(searchConfigFacet).top; }\n}\nfunction phrase(view, phrase) { return view.state.phrase(phrase); }\nconst AnnounceMargin = 30;\nconst Break = /[\\s\\.,:;?!]/;\nfunction announceMatch(view, { from, to }) {\n    let line = view.state.doc.lineAt(from), lineEnd = view.state.doc.lineAt(to).to;\n    let start = Math.max(line.from, from - AnnounceMargin), end = Math.min(lineEnd, to + AnnounceMargin);\n    let text = view.state.sliceDoc(start, end);\n    if (start != line.from) {\n        for (let i = 0; i < AnnounceMargin; i++)\n            if (!Break.test(text[i + 1]) && Break.test(text[i])) {\n                text = text.slice(i);\n                break;\n            }\n    }\n    if (end != lineEnd) {\n        for (let i = text.length - 1; i > text.length - AnnounceMargin; i--)\n            if (!Break.test(text[i - 1]) && Break.test(text[i])) {\n                text = text.slice(0, i);\n                break;\n            }\n    }\n    return EditorView.announce.of(`${view.state.phrase(\"current match\")}. ${text} ${view.state.phrase(\"on line\")} ${line.number}.`);\n}\nconst baseTheme = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-panel.cm-search\": {\n        padding: \"2px 6px 4px\",\n        position: \"relative\",\n        \"& [name=close]\": {\n            position: \"absolute\",\n            top: \"0\",\n            right: \"4px\",\n            backgroundColor: \"inherit\",\n            border: \"none\",\n            font: \"inherit\",\n            padding: 0,\n            margin: 0\n        },\n        \"& input, & button, & label\": {\n            margin: \".2em .6em .2em 0\"\n        },\n        \"& input[type=checkbox]\": {\n            marginRight: \".2em\"\n        },\n        \"& label\": {\n            fontSize: \"80%\",\n            whiteSpace: \"pre\"\n        }\n    },\n    \"&light .cm-searchMatch\": { backgroundColor: \"#ffff0054\" },\n    \"&dark .cm-searchMatch\": { backgroundColor: \"#00ffff8a\" },\n    \"&light .cm-searchMatch-selected\": { backgroundColor: \"#ff6a0054\" },\n    \"&dark .cm-searchMatch-selected\": { backgroundColor: \"#ff00ff8a\" }\n});\nconst searchExtensions = [\n    searchState,\n    /*@__PURE__*/Prec.low(searchHighlighter),\n    baseTheme\n];\n\nexport { RegExpCursor, SearchCursor, SearchQuery, closeSearchPanel, findNext, findPrevious, getSearchQuery, gotoLine, highlightSelectionMatches, openSearchPanel, replaceAll, replaceNext, search, searchKeymap, searchPanelOpen, selectMatches, selectNextOccurrence, selectSelectionMatches, setSearchQuery };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,iBAAiB,OAAO,OAAO,UAAU,aAAa,aACtD,OAAK,EAAE,UAAU,MAAM,IAAI,OAAK;AAKtC,IAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcf,YAAY,MAAM,OAAO,OAAO,GAAG,KAAK,KAAK,QAAQ,WAAW,MAAM;AAClE,SAAK,OAAO;AAMZ,SAAK,QAAQ,EAAE,MAAM,GAAG,IAAI,EAAE;AAI9B,SAAK,OAAO;AACZ,SAAK,UAAU,CAAC;AAChB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,OAAO,KAAK,UAAU,MAAM,EAAE;AACnC,SAAK,cAAc;AACnB,SAAK,YAAY,YAAY,OAAK,UAAU,eAAe,CAAC,CAAC,IAAI;AACjE,SAAK,QAAQ,KAAK,UAAU,KAAK;AAAA,EACrC;AAAA,EACA,OAAO;AACH,QAAI,KAAK,aAAa,KAAK,OAAO,QAAQ;AACtC,WAAK,eAAe,KAAK,OAAO;AAChC,WAAK,KAAK,KAAK;AACf,UAAI,KAAK,KAAK;AACV,eAAO;AACX,WAAK,YAAY;AACjB,WAAK,SAAS,KAAK,KAAK;AAAA,IAC5B;AACA,WAAO,YAAY,KAAK,QAAQ,KAAK,SAAS;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO;AACH,WAAO,KAAK,QAAQ;AAChB,WAAK,QAAQ,IAAI;AACrB,WAAO,KAAK,gBAAgB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AACd,eAAS;AACL,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,OAAO,GAAG;AACV,aAAK,OAAO;AACZ,eAAO;AAAA,MACX;AACA,UAAI,MAAM,cAAc,IAAI,GAAG,QAAQ,KAAK,cAAc,KAAK;AAC/D,WAAK,aAAa,cAAc,IAAI;AACpC,UAAI,OAAO,KAAK,UAAU,GAAG;AAC7B,UAAI,KAAK;AACL,iBAAS,IAAI,GAAG,MAAM,SAAQ,KAAK;AAC/B,cAAI,OAAO,KAAK,WAAW,CAAC;AAC5B,cAAI,QAAQ,KAAK,MAAM,MAAM,KAAK,KAAK,YAAY,KAAK,WAAW;AACnE,cAAI,KAAK,KAAK,SAAS,GAAG;AACtB,gBAAI,OAAO;AACP,mBAAK,QAAQ;AACb,qBAAO;AAAA,YACX;AACA;AAAA,UACJ;AACA,cAAI,OAAO,SAAS,IAAI,IAAI,UAAU,IAAI,WAAW,CAAC,KAAK;AACvD;AAAA,QACR;AAAA,IACR;AAAA,EACJ;AAAA,EACA,MAAM,MAAM,KAAK,KAAK;AAClB,QAAI,QAAQ;AACZ,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK,GAAG;AAC7C,UAAI,QAAQ,KAAK,QAAQ,CAAC,GAAG,OAAO;AACpC,UAAI,KAAK,MAAM,WAAW,KAAK,KAAK,MAAM;AACtC,YAAI,SAAS,KAAK,MAAM,SAAS,GAAG;AAChC,kBAAQ,EAAE,MAAM,KAAK,QAAQ,IAAI,CAAC,GAAG,IAAI,IAAI;AAAA,QACjD,OACK;AACD,eAAK,QAAQ,CAAC;AACd,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,UAAI,CAAC,MAAM;AACP,aAAK,QAAQ,OAAO,GAAG,CAAC;AACxB,aAAK;AAAA,MACT;AAAA,IACJ;AACA,QAAI,KAAK,MAAM,WAAW,CAAC,KAAK,MAAM;AAClC,UAAI,KAAK,MAAM,UAAU;AACrB,gBAAQ,EAAE,MAAM,KAAK,IAAI,IAAI;AAAA;AAE7B,aAAK,QAAQ,KAAK,GAAG,GAAG;AAAA,IAChC;AACA,QAAI,SAAS,KAAK,QAAQ,CAAC,KAAK,KAAK,MAAM,MAAM,MAAM,IAAI,KAAK,QAAQ,KAAK,WAAW;AACpF,cAAQ;AACZ,WAAO;AAAA,EACX;AACJ;AACA,IAAI,OAAO,UAAU;AACjB,eAAa,UAAU,OAAO,QAAQ,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM;AAEzE,IAAM,QAAQ,EAAE,MAAM,IAAI,IAAI,IAAI,OAAoB,KAAK,KAAK,EAAE,EAAE;AACpE,IAAM,YAAY,QAAQ,IAAI,WAAW,OAAO,KAAK;AAMrD,IAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMf,YAAY,MAAM,OAAO,SAAS,OAAO,GAAG,KAAK,KAAK,QAAQ;AAC1D,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,UAAU;AAKf,SAAK,OAAO;AAMZ,SAAK,QAAQ;AACb,QAAI,uBAAuB,KAAK,KAAK;AACjC,aAAO,IAAI,sBAAsB,MAAM,OAAO,SAAS,MAAM,EAAE;AACnE,SAAK,KAAK,IAAI,OAAO,OAAO,cAAc,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,MAAM,GAAG;AAC3H,SAAK,OAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AACtE,SAAK,OAAO,KAAK,KAAK;AACtB,QAAI,YAAY,KAAK,OAAO,IAAI;AAChC,SAAK,eAAe,UAAU;AAC9B,SAAK,WAAW,UAAU,MAAM,IAAI;AACpC,SAAK,QAAQ,KAAK,YAAY;AAAA,EAClC;AAAA,EACA,QAAQ,MAAM;AACV,SAAK,KAAK,KAAK,IAAI;AACnB,QAAI,KAAK,KAAK,WAAW;AACrB,WAAK,UAAU;AAAA,IACnB,OACK;AACD,WAAK,UAAU,KAAK,KAAK;AACzB,UAAI,KAAK,eAAe,KAAK,QAAQ,SAAS,KAAK;AAC/C,aAAK,UAAU,KAAK,QAAQ,MAAM,GAAG,KAAK,KAAK,KAAK,YAAY;AACpE,WAAK,KAAK,KAAK;AAAA,IACnB;AAAA,EACJ;AAAA,EACA,WAAW;AACP,SAAK,eAAe,KAAK,eAAe,KAAK,QAAQ,SAAS;AAC9D,QAAI,KAAK,eAAe,KAAK;AACzB,WAAK,UAAU;AAAA;AAEf,WAAK,QAAQ,CAAC;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACH,aAAS,MAAM,KAAK,WAAW,KAAK,kBAAgB;AAChD,WAAK,GAAG,YAAY;AACpB,UAAI,QAAQ,KAAK,YAAY,KAAK,MAAM,KAAK,GAAG,KAAK,KAAK,OAAO;AACjE,UAAI,OAAO;AACP,YAAI,OAAO,KAAK,eAAe,MAAM,OAAO,KAAK,OAAO,MAAM,CAAC,EAAE;AACjE,aAAK,WAAW,UAAU,KAAK,MAAM,MAAM,QAAQ,KAAK,IAAI,EAAE;AAC9D,YAAI,QAAQ,KAAK,eAAe,KAAK,QAAQ;AACzC,eAAK,SAAS;AAClB,aAAK,OAAO,MAAM,OAAO,KAAK,MAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI;AACnF,eAAK,QAAQ,EAAE,MAAM,IAAI,MAAM;AAC/B,iBAAO;AAAA,QACX;AACA,cAAM,KAAK,WAAW,KAAK;AAAA,MAC/B,WACS,KAAK,eAAe,KAAK,QAAQ,SAAS,KAAK,IAAI;AACxD,aAAK,SAAS;AACd,cAAM;AAAA,MACV,OACK;AACD,aAAK,OAAO;AACZ,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,IAAM,YAAyB,oBAAI,QAAQ;AAE3C,IAAM,eAAN,MAAM,cAAa;AAAA,EACf,YAAY,MAAM,MAAM;AACpB,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,KAAK,OAAO,KAAK,KAAK;AAAA,EAAQ;AAAA,EAChD,OAAO,IAAI,KAAK,MAAM,IAAI;AACtB,QAAI,SAAS,UAAU,IAAI,GAAG;AAC9B,QAAI,CAAC,UAAU,OAAO,QAAQ,MAAM,OAAO,MAAM,MAAM;AACnD,UAAI,OAAO,IAAI,cAAa,MAAM,IAAI,YAAY,MAAM,EAAE,CAAC;AAC3D,gBAAU,IAAI,KAAK,IAAI;AACvB,aAAO;AAAA,IACX;AACA,QAAI,OAAO,QAAQ,QAAQ,OAAO,MAAM;AACpC,aAAO;AACX,QAAI,EAAE,MAAM,MAAM,WAAW,IAAI;AACjC,QAAI,aAAa,MAAM;AACnB,aAAO,IAAI,YAAY,MAAM,UAAU,IAAI;AAC3C,mBAAa;AAAA,IACjB;AACA,QAAI,OAAO,KAAK;AACZ,cAAQ,IAAI,YAAY,OAAO,IAAI,EAAE;AACzC,cAAU,IAAI,KAAK,IAAI,cAAa,YAAY,IAAI,CAAC;AACrD,WAAO,IAAI,cAAa,MAAM,KAAK,MAAM,OAAO,YAAY,KAAK,UAAU,CAAC;AAAA,EAChF;AACJ;AACA,IAAM,wBAAN,MAA4B;AAAA,EACxB,YAAY,MAAM,OAAO,SAAS,MAAM,IAAI;AACxC,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,WAAW,UAAU,MAAM,IAAI;AACpC,SAAK,KAAK,IAAI,OAAO,OAAO,cAAc,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,MAAM,GAAG;AAC3H,SAAK,OAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AACtE,SAAK,OAAO,aAAa,IAAI,MAAM,MAAM,KAAK;AAAA,MAAS,OAAO;AAAA;AAAA,IAAqB,CAAC;AAAA,EACxF;AAAA,EACA,SAAS,KAAK;AACV,WAAO,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,EAAE;AAAA,EAC5D;AAAA,EACA,OAAO;AACH,eAAS;AACL,UAAI,MAAM,KAAK,GAAG,YAAY,KAAK,WAAW,KAAK,KAAK;AACxD,UAAI,QAAQ,KAAK,GAAG,KAAK,KAAK,KAAK,IAAI;AAEvC,UAAI,SAAS,CAAC,MAAM,CAAC,KAAK,MAAM,SAAS,KAAK;AAC1C,aAAK,GAAG,YAAY,MAAM;AAC1B,gBAAQ,KAAK,GAAG,KAAK,KAAK,KAAK,IAAI;AAAA,MACvC;AACA,UAAI,OAAO;AACP,YAAI,OAAO,KAAK,KAAK,OAAO,MAAM,OAAO,KAAK,OAAO,MAAM,CAAC,EAAE;AAG9D,aAAK,KAAK,KAAK,MAAM,KAAK,MAAM,MAAM,QAAQ,MAAM,CAAC,EAAE,UAAU,KAAK,KAAK,KAAK,SAAS,QACpF,CAAC,KAAK,QAAQ,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI;AAC5C,eAAK,QAAQ,EAAE,MAAM,IAAI,MAAM;AAC/B,eAAK,WAAW,UAAU,KAAK,MAAM,MAAM,QAAQ,KAAK,IAAI,EAAE;AAC9D,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,UAAI,KAAK,KAAK,MAAM,KAAK,IAAI;AACzB,aAAK,OAAO;AACZ,eAAO;AAAA,MACX;AAEA,WAAK,OAAO,aAAa,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,SAAS,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,SAAS,CAAC,CAAC;AAAA,IACrH;AAAA,EACJ;AACJ;AACA,IAAI,OAAO,UAAU,aAAa;AAC9B,eAAa,UAAU,OAAO,QAAQ,IAAI,sBAAsB,UAAU,OAAO,QAAQ,IACrF,WAAY;AAAE,WAAO;AAAA,EAAM;AACnC;AACA,SAAS,YAAY,QAAQ;AACzB,MAAI;AACA,QAAI,OAAO,QAAQ,SAAS;AAC5B,WAAO;AAAA,EACX,SACO,IAAI;AACP,WAAO;AAAA,EACX;AACJ;AACA,SAAS,UAAU,MAAM,KAAK;AAC1B,MAAI,OAAO,KAAK;AACZ,WAAO;AACX,MAAI,OAAO,KAAK,OAAO,GAAG,GAAG;AAC7B,SAAO,MAAM,KAAK,OAAO,OAAO,KAAK,KAAK,WAAW,MAAM,KAAK,IAAI,MAAM,SAAU,OAAO;AACvF;AACJ,SAAO;AACX;AAEA,SAAS,iBAAiB,MAAM;AAC5B,MAAI,OAAO,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM,UAAU,KAAK,IAAI,EAAE,MAAM;AAC9E,MAAI,QAAQ,MAAI,SAAS,EAAE,OAAO,gBAAgB,MAAM,QAAQ,OAAO,KAAK,CAAC;AAC7E,MAAI,MAAM,MAAI,QAAQ;AAAA,IAClB,OAAO;AAAA,IACP,WAAW,CAAC,UAAU;AAClB,UAAI,MAAM,WAAW,IAAI;AACrB,cAAM,eAAe;AACrB,aAAK,SAAS,EAAE,SAAS,aAAa,GAAG,KAAK,EAAE,CAAC;AACjD,aAAK,MAAM;AAAA,MACf,WACS,MAAM,WAAW,IAAI;AAC1B,cAAM,eAAe;AACrB,WAAG;AAAA,MACP;AAAA,IACJ;AAAA,IACA,UAAU,CAAC,UAAU;AACjB,YAAM,eAAe;AACrB,SAAG;AAAA,IACP;AAAA,EACJ,GAAG,MAAI,SAAS,KAAK,MAAM,OAAO,YAAY,GAAG,MAAM,KAAK,GAAG,KAAK,MAAI,UAAU,EAAE,OAAO,aAAa,MAAM,SAAS,GAAG,KAAK,MAAM,OAAO,IAAI,CAAC,GAAG,MAAI,UAAU;AAAA,IAC9J,MAAM;AAAA,IACN,SAAS,MAAM;AACX,WAAK,SAAS,EAAE,SAAS,aAAa,GAAG,KAAK,EAAE,CAAC;AACjD,WAAK,MAAM;AAAA,IACf;AAAA,IACA,cAAc,KAAK,MAAM,OAAO,OAAO;AAAA,IACvC,MAAM;AAAA,EACV,GAAG,CAAC,GAAG,CAAC,CAAC;AACT,WAAS,KAAK;AACV,QAAI,QAAQ,6BAA6B,KAAK,MAAM,KAAK;AACzD,QAAI,CAAC;AACD;AACJ,QAAI,EAAE,MAAM,IAAI,MAAM,YAAY,MAAM,IAAI,OAAO,MAAM,UAAU,KAAK,IAAI;AAC5E,QAAI,CAAC,EAAE,MAAM,IAAI,IAAI,OAAO,IAAI;AAChC,QAAI,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI;AAC9B,QAAIA,QAAO,KAAK,CAAC,KAAK,UAAU;AAChC,QAAI,MAAM,SAAS;AACf,UAAI,KAAKA,QAAO;AAChB,UAAI;AACA,aAAK,MAAM,QAAQ,MAAM,KAAK,KAAM,UAAU,SAAS,MAAM,IAAI;AACrE,MAAAA,QAAO,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE;AAAA,IAC1C,WACS,MAAM,MAAM;AACjB,MAAAA,QAAOA,SAAQ,QAAQ,MAAM,KAAK,KAAK,UAAU;AAAA,IACrD;AACA,QAAI,UAAU,MAAM,IAAI,KAAK,KAAK,IAAI,GAAG,KAAK,IAAI,MAAM,IAAI,OAAOA,KAAI,CAAC,CAAC;AACzE,QAAI,YAAY,gBAAgB,OAAO,QAAQ,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,QAAQ,MAAM,CAAC,CAAC;AAChG,SAAK,SAAS;AAAA,MACV,SAAS,CAAC,aAAa,GAAG,KAAK,GAAG,WAAW,eAAe,UAAU,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC;AAAA,MAC5F;AAAA,IACJ,CAAC;AACD,SAAK,MAAM;AAAA,EACf;AACA,SAAO,EAAE,IAAI;AACjB;AACA,IAAM,eAA4B,YAAY,OAAO;AACrD,IAAM,cAA2B,WAAW,OAAO;AAAA,EAC/C,SAAS;AAAE,WAAO;AAAA,EAAM;AAAA,EACxB,OAAO,OAAO,IAAI;AACd,aAAS,KAAK,GAAG;AACb,UAAI,EAAE,GAAG,YAAY;AACjB,gBAAQ,EAAE;AAClB,WAAO;AAAA,EACX;AAAA,EACA,SAAS,OAAK,UAAU,KAAK,GAAG,SAAO,MAAM,mBAAmB,IAAI;AACxE,CAAC;AAUD,IAAM,WAAW,UAAQ;AACrB,MAAI,QAAQ,SAAS,MAAM,gBAAgB;AAC3C,MAAI,CAAC,OAAO;AACR,QAAI,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC;AACpC,QAAI,KAAK,MAAM,MAAM,aAAa,KAAK,KAAK;AACxC,cAAQ,KAAK,YAAY,aAAa,GAAG,CAAC,aAAa,WAAW,CAAC,CAAC;AACxE,SAAK,SAAS,EAAE,QAAQ,CAAC;AACzB,YAAQ,SAAS,MAAM,gBAAgB;AAAA,EAC3C;AACA,MAAI;AACA,UAAM,IAAI,cAAc,OAAO,EAAE,OAAO;AAC5C,SAAO;AACX;AACA,IAAM,cAA2B,WAAW,UAAU;AAAA,EAClD,yBAAyB;AAAA,IACrB,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW,EAAE,UAAU,MAAM;AAAA,IAC7B,kBAAkB;AAAA,MACd,UAAU;AAAA,MACV,KAAK;AAAA,MAAK,QAAQ;AAAA,MAClB,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,EACJ;AACJ,CAAC;AAED,IAAM,0BAA0B;AAAA,EAC5B,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,YAAY;AAAA,EACZ,YAAY;AAChB;AACA,IAAM,kBAA+B,MAAM,OAAO;AAAA,EAC9C,QAAQ,SAAS;AACb,WAAO,cAAc,SAAS,yBAAyB;AAAA,MACnD,2BAA2B,CAAC,GAAG,MAAM,KAAK;AAAA,MAC1C,oBAAoB,KAAK;AAAA,MACzB,YAAY,KAAK;AAAA,IACrB,CAAC;AAAA,EACL;AACJ,CAAC;AAOD,SAAS,0BAA0B,SAAS;AACxC,MAAI,MAAM,CAAC,cAAc,gBAAgB;AACzC,MAAI;AACA,QAAI,KAAK,gBAAgB,GAAG,OAAO,CAAC;AACxC,SAAO;AACX;AACA,IAAM,YAAyB,WAAW,KAAK,EAAE,OAAO,oBAAoB,CAAC;AAC7E,IAAM,gBAA6B,WAAW,KAAK,EAAE,OAAO,2CAA2C,CAAC;AAExG,SAAS,qBAAqB,OAAO,OAAO,MAAM,IAAI;AAClD,UAAQ,QAAQ,KAAK,MAAM,MAAM,SAAS,OAAO,GAAG,IAAI,CAAC,KAAK,aAAa,UACtE,MAAM,MAAM,IAAI,UAAU,MAAM,MAAM,SAAS,IAAI,KAAK,CAAC,CAAC,KAAK,aAAa;AACrF;AAEA,SAAS,WAAW,OAAO,OAAO,MAAM,IAAI;AACxC,SAAO,MAAM,MAAM,SAAS,MAAM,OAAO,CAAC,CAAC,KAAK,aAAa,QACtD,MAAM,MAAM,SAAS,KAAK,GAAG,EAAE,CAAC,KAAK,aAAa;AAC7D;AACA,IAAM,mBAAgC,WAAW,UAAU,MAAM;AAAA,EAC7D,YAAY,MAAM;AACd,SAAK,cAAc,KAAK,QAAQ,IAAI;AAAA,EACxC;AAAA,EACA,OAAO,QAAQ;AACX,QAAI,OAAO,gBAAgB,OAAO,cAAc,OAAO;AACnD,WAAK,cAAc,KAAK,QAAQ,OAAO,IAAI;AAAA,EACnD;AAAA,EACA,QAAQ,MAAM;AACV,QAAI,OAAO,KAAK,MAAM,MAAM,eAAe;AAC3C,QAAI,EAAE,MAAM,IAAI,MAAM,MAAM,MAAM;AAClC,QAAI,IAAI,OAAO,SAAS;AACpB,aAAO,WAAW;AACtB,QAAI,QAAQ,IAAI,MAAM,OAAO,QAAQ;AACrC,QAAI,MAAM,OAAO;AACb,UAAI,CAAC,KAAK;AACN,eAAO,WAAW;AACtB,UAAI,OAAO,MAAM,OAAO,MAAM,IAAI;AAClC,UAAI,CAAC;AACD,eAAO,WAAW;AACtB,cAAQ,MAAM,gBAAgB,MAAM,IAAI;AACxC,cAAQ,MAAM,SAAS,KAAK,MAAM,KAAK,EAAE;AAAA,IAC7C,OACK;AACD,UAAI,MAAM,MAAM,KAAK,MAAM;AAC3B,UAAI,MAAM,KAAK,sBAAsB,MAAM;AACvC,eAAO,WAAW;AACtB,UAAI,KAAK,YAAY;AACjB,gBAAQ,MAAM,SAAS,MAAM,MAAM,MAAM,EAAE;AAC3C,gBAAQ,MAAM,gBAAgB,MAAM,IAAI;AACxC,YAAI,EAAE,qBAAqB,OAAO,OAAO,MAAM,MAAM,MAAM,EAAE,KACzD,WAAW,OAAO,OAAO,MAAM,MAAM,MAAM,EAAE;AAC7C,iBAAO,WAAW;AAAA,MAC1B,OACK;AACD,gBAAQ,MAAM,SAAS,MAAM,MAAM,MAAM,EAAE;AAC3C,YAAI,CAAC;AACD,iBAAO,WAAW;AAAA,MAC1B;AAAA,IACJ;AACA,QAAI,OAAO,CAAC;AACZ,aAAS,QAAQ,KAAK,eAAe;AACjC,UAAI,SAAS,IAAI,aAAa,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK,EAAE;AAClE,aAAO,CAAC,OAAO,KAAK,EAAE,MAAM;AACxB,YAAI,EAAE,MAAM,GAAG,IAAI,OAAO;AAC1B,YAAI,CAAC,SAAS,qBAAqB,OAAO,OAAO,MAAM,EAAE,GAAG;AACxD,cAAI,MAAM,SAAS,QAAQ,MAAM,QAAQ,MAAM,MAAM;AACjD,iBAAK,KAAK,cAAc,MAAM,MAAM,EAAE,CAAC;AAAA,mBAClC,QAAQ,MAAM,MAAM,MAAM,MAAM;AACrC,iBAAK,KAAK,UAAU,MAAM,MAAM,EAAE,CAAC;AACvC,cAAI,KAAK,SAAS,KAAK;AACnB,mBAAO,WAAW;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,WAAW,IAAI,IAAI;AAAA,EAC9B;AACJ,GAAG;AAAA,EACC,aAAa,OAAK,EAAE;AACxB,CAAC;AACD,IAAM,eAA4B,WAAW,UAAU;AAAA,EACnD,sBAAsB,EAAE,iBAAiB,YAAY;AAAA,EACrD,sCAAsC,EAAE,iBAAiB,cAAc;AAC3E,CAAC;AAED,IAAM,aAAa,CAAC,EAAE,OAAO,SAAS,MAAM;AACxC,MAAI,EAAE,UAAU,IAAI;AACpB,MAAI,SAAS,gBAAgB,OAAO,UAAU,OAAO,IAAI,WAAS,MAAM,OAAO,MAAM,IAAI,KAAK,gBAAgB,OAAO,MAAM,IAAI,CAAC,GAAG,UAAU,SAAS;AACtJ,MAAI,OAAO,GAAG,SAAS;AACnB,WAAO;AACX,WAAS,MAAM,OAAO,EAAE,WAAW,OAAO,CAAC,CAAC;AAC5C,SAAO;AACX;AAGA,SAAS,mBAAmB,OAAO,OAAO;AACtC,MAAI,EAAE,MAAM,OAAO,IAAI,MAAM;AAC7B,MAAI,OAAO,MAAM,OAAO,KAAK,IAAI,GAAG,WAAW,QAAQ,KAAK,QAAQ,KAAK,QAAQ,KAAK,MAAM,KAAK;AACjG,WAAS,SAAS,OAAO,SAAS,IAAI,aAAa,MAAM,KAAK,OAAO,OAAO,OAAO,SAAS,CAAC,EAAE,EAAE,OAAK;AAClG,WAAO,KAAK;AACZ,QAAI,OAAO,MAAM;AACb,UAAI;AACA,eAAO;AACX,eAAS,IAAI,aAAa,MAAM,KAAK,OAAO,GAAG,KAAK,IAAI,GAAG,OAAO,OAAO,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;AAC9F,eAAS;AAAA,IACb,OACK;AACD,UAAI,UAAU,OAAO,KAAK,OAAK,EAAE,QAAQ,OAAO,MAAM,IAAI;AACtD;AACJ,UAAI,UAAU;AACV,YAAIC,QAAO,MAAM,OAAO,OAAO,MAAM,IAAI;AACzC,YAAI,CAACA,SAAQA,MAAK,QAAQ,OAAO,MAAM,QAAQA,MAAK,MAAM,OAAO,MAAM;AACnE;AAAA,MACR;AACA,aAAO,OAAO;AAAA,IAClB;AAAA,EACJ;AACJ;AAKA,IAAM,uBAAuB,CAAC,EAAE,OAAO,SAAS,MAAM;AAClD,MAAI,EAAE,OAAO,IAAI,MAAM;AACvB,MAAI,OAAO,KAAK,SAAO,IAAI,SAAS,IAAI,EAAE;AACtC,WAAO,WAAW,EAAE,OAAO,SAAS,CAAC;AACzC,MAAI,eAAe,MAAM,SAAS,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE;AAC9D,MAAI,MAAM,UAAU,OAAO,KAAK,OAAK,MAAM,SAAS,EAAE,MAAM,EAAE,EAAE,KAAK,YAAY;AAC7E,WAAO;AACX,MAAI,QAAQ,mBAAmB,OAAO,YAAY;AAClD,MAAI,CAAC;AACD,WAAO;AACX,WAAS,MAAM,OAAO;AAAA,IAClB,WAAW,MAAM,UAAU,SAAS,gBAAgB,MAAM,MAAM,MAAM,MAAM,EAAE,GAAG,KAAK;AAAA,IACtF,SAAS,WAAW,eAAe,MAAM,EAAE;AAAA,EAC/C,CAAC,CAAC;AACF,SAAO;AACX;AAEA,IAAM,oBAAiC,MAAM,OAAO;AAAA,EAChD,QAAQ,SAAS;AACb,WAAO,cAAc,SAAS;AAAA,MAC1B,KAAK;AAAA,MACL,eAAe;AAAA,MACf,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa,UAAQ,IAAI,YAAY,IAAI;AAAA,MACzC,eAAe,WAAS,WAAW,eAAe,KAAK;AAAA,IAC3D,CAAC;AAAA,EACL;AACJ,CAAC;AAOD,SAAS,OAAO,QAAQ;AACpB,SAAO,SAAS,CAAC,kBAAkB,GAAG,MAAM,GAAG,gBAAgB,IAAI;AACvE;AAIA,IAAM,cAAN,MAAkB;AAAA;AAAA;AAAA;AAAA,EAId,YAAY,QAAQ;AAChB,SAAK,SAAS,OAAO;AACrB,SAAK,gBAAgB,CAAC,CAAC,OAAO;AAC9B,SAAK,UAAU,CAAC,CAAC,OAAO;AACxB,SAAK,SAAS,CAAC,CAAC,OAAO;AACvB,SAAK,UAAU,OAAO,WAAW;AACjC,SAAK,QAAQ,CAAC,CAAC,KAAK,WAAW,CAAC,KAAK,UAAU,YAAY,KAAK,MAAM;AACtE,SAAK,WAAW,KAAK,QAAQ,KAAK,MAAM;AACxC,SAAK,YAAY,CAAC,CAAC,OAAO;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,MAAM;AACV,WAAO,KAAK,UAAU,OAClB,KAAK,QAAQ,gBAAgB,CAAC,GAAG,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,MAAO,IAAI;AAAA,EAC7G;AAAA;AAAA;AAAA;AAAA,EAIA,GAAG,OAAO;AACN,WAAO,KAAK,UAAU,MAAM,UAAU,KAAK,WAAW,MAAM,WACxD,KAAK,iBAAiB,MAAM,iBAAiB,KAAK,UAAU,MAAM,UAClE,KAAK,aAAa,MAAM;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,WAAO,KAAK,SAAS,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,IAAI;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO,OAAO,GAAG,IAAI;AAC3B,QAAI,KAAK,MAAM,MAAM,QAAQ,YAAY,OAAO,EAAE,KAAK,MAAM,CAAC;AAC9D,QAAI,MAAM;AACN,WAAK,GAAG,IAAI;AAChB,WAAO,KAAK,SAAS,aAAa,MAAM,IAAI,MAAM,EAAE,IAAI,aAAa,MAAM,IAAI,MAAM,EAAE;AAAA,EAC3F;AACJ;AACA,IAAM,YAAN,MAAgB;AAAA,EACZ,YAAY,MAAM;AACd,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,SAAS,aAAa,MAAM,OAAO,MAAM,IAAI;AACzC,SAAO,IAAI,aAAa,MAAM,KAAK,KAAK,UAAU,MAAM,IAAI,KAAK,gBAAgB,SAAY,OAAK,EAAE,YAAY,GAAG,KAAK,YAAY,eAAe,MAAM,KAAK,MAAM,gBAAgB,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,MAAS;AAC/N;AACA,SAAS,eAAe,KAAK,aAAa;AACtC,SAAO,CAAC,MAAM,IAAI,KAAK,WAAW;AAC9B,QAAI,SAAS,QAAQ,SAAS,IAAI,SAAS,IAAI;AAC3C,eAAS,KAAK,IAAI,GAAG,OAAO,CAAC;AAC7B,YAAM,IAAI,YAAY,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC9D;AACA,YAAQ,YAAY,WAAW,KAAK,OAAO,MAAM,CAAC,KAAK,aAAa,QAChE,YAAY,UAAU,KAAK,OAAO,MAAM,CAAC,KAAK,aAAa,UAC1D,YAAY,UAAU,KAAK,KAAK,MAAM,CAAC,KAAK,aAAa,QACtD,YAAY,WAAW,KAAK,KAAK,MAAM,CAAC,KAAK,aAAa;AAAA,EACtE;AACJ;AACA,IAAM,cAAN,cAA0B,UAAU;AAAA,EAChC,YAAY,MAAM;AACd,UAAM,IAAI;AAAA,EACd;AAAA,EACA,UAAU,OAAO,SAAS,OAAO;AAC7B,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,OAAO,MAAM,IAAI,MAAM,EAAE,gBAAgB;AACrF,QAAI,OAAO,MAAM;AACb,UAAI,MAAM,KAAK,IAAI,MAAM,IAAI,QAAQ,UAAU,KAAK,KAAK,SAAS,MAAM;AACxE,eAAS,aAAa,KAAK,MAAM,OAAO,GAAG,GAAG,EAAE,gBAAgB;AAAA,IACpE;AACA,WAAO,OAAO,QAAQ,OAAO,MAAM,QAAQ,WAAW,OAAO,MAAM,MAAM,QAAQ,OAAO,OAAO;AAAA,EACnG;AAAA;AAAA;AAAA,EAGA,iBAAiB,OAAO,MAAM,IAAI;AAC9B,aAAS,MAAM,QAAM;AACjB,UAAI,QAAQ,KAAK,IAAI,MAAM,MAAM,MAAiC,KAAK,KAAK,SAAS,MAAM;AAC3F,UAAI,SAAS,aAAa,KAAK,MAAM,OAAO,OAAO,GAAG,GAAG,QAAQ;AACjE,aAAO,CAAC,OAAO,gBAAgB,EAAE;AAC7B,gBAAQ,OAAO;AACnB,UAAI;AACA,eAAO;AACX,UAAI,SAAS;AACT,eAAO;AACX,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,UAAU,OAAO,SAAS,OAAO;AAC7B,QAAI,QAAQ,KAAK,iBAAiB,OAAO,GAAG,OAAO;AACnD,QAAI,CAAC;AACD,cAAQ,KAAK,iBAAiB,OAAO,KAAK,IAAI,GAAG,QAAQ,KAAK,KAAK,SAAS,MAAM,GAAG,MAAM,IAAI,MAAM;AACzG,WAAO,UAAU,MAAM,QAAQ,WAAW,MAAM,MAAM,SAAS,QAAQ;AAAA,EAC3E;AAAA,EACA,eAAe,SAAS;AAAE,WAAO,KAAK,KAAK,QAAQ,KAAK,KAAK,OAAO;AAAA,EAAG;AAAA,EACvE,SAAS,OAAO,OAAO;AACnB,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,GAAG,MAAM,IAAI,MAAM,GAAG,SAAS,CAAC;AAC5E,WAAO,CAAC,OAAO,KAAK,EAAE,MAAM;AACxB,UAAI,OAAO,UAAU;AACjB,eAAO;AACX,aAAO,KAAK,OAAO,KAAK;AAAA,IAC5B;AACA,WAAO;AAAA,EACX;AAAA,EACA,UAAU,OAAO,MAAM,IAAI,KAAK;AAC5B,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,KAAK,IAAI,GAAG,OAAO,KAAK,KAAK,SAAS,MAAM,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,SAAS,QAAQ,MAAM,IAAI,MAAM,CAAC;AACrJ,WAAO,CAAC,OAAO,KAAK,EAAE;AAClB,UAAI,OAAO,MAAM,MAAM,OAAO,MAAM,EAAE;AAAA,EAC9C;AACJ;AACA,SAAS,aAAa,MAAM,OAAO,MAAM,IAAI;AACzC,SAAO,IAAI,aAAa,MAAM,KAAK,KAAK,QAAQ;AAAA,IAC5C,YAAY,CAAC,KAAK;AAAA,IAClB,MAAM,KAAK,YAAY,eAAe,MAAM,gBAAgB,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI;AAAA,EAC9F,GAAG,MAAM,EAAE;AACf;AACA,SAAS,WAAW,KAAK,OAAO;AAC5B,SAAO,IAAI,MAAM,iBAAiB,KAAK,OAAO,KAAK,GAAG,KAAK;AAC/D;AACA,SAAS,UAAU,KAAK,OAAO;AAC3B,SAAO,IAAI,MAAM,OAAO,iBAAiB,KAAK,KAAK,CAAC;AACxD;AACA,SAAS,eAAe,aAAa;AACjC,SAAO,CAAC,OAAO,KAAK,UAAU,CAAC,MAAM,CAAC,EAAE,WACnC,YAAY,WAAW,MAAM,OAAO,MAAM,KAAK,CAAC,KAAK,aAAa,QAC/D,YAAY,UAAU,MAAM,OAAO,MAAM,KAAK,CAAC,KAAK,aAAa,UAChE,YAAY,UAAU,MAAM,OAAO,MAAM,QAAQ,MAAM,CAAC,EAAE,MAAM,CAAC,KAAK,aAAa,QAChF,YAAY,WAAW,MAAM,OAAO,MAAM,QAAQ,MAAM,CAAC,EAAE,MAAM,CAAC,KAAK,aAAa;AACpG;AACA,IAAM,cAAN,cAA0B,UAAU;AAAA,EAChC,UAAU,OAAO,SAAS,OAAO;AAC7B,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,OAAO,MAAM,IAAI,MAAM,EAAE,KAAK;AAC1E,QAAI,OAAO;AACP,eAAS,aAAa,KAAK,MAAM,OAAO,GAAG,OAAO,EAAE,KAAK;AAC7D,WAAO,OAAO,OAAO,OAAO,OAAO;AAAA,EACvC;AAAA,EACA,iBAAiB,OAAO,MAAM,IAAI;AAC9B,aAAS,OAAO,KAAI,QAAQ;AACxB,UAAI,QAAQ,KAAK;AAAA,QAAI;AAAA,QAAM,KAAK,OAAO;AAAA;AAAA,MAA8B;AACrE,UAAI,SAAS,aAAa,KAAK,MAAM,OAAO,OAAO,EAAE,GAAG,QAAQ;AAChE,aAAO,CAAC,OAAO,KAAK,EAAE;AAClB,gBAAQ,OAAO;AACnB,UAAI,UAAU,SAAS,QAAQ,MAAM,OAAO,QAAQ;AAChD,eAAO;AACX,UAAI,SAAS;AACT,eAAO;AAAA,IACf;AAAA,EACJ;AAAA,EACA,UAAU,OAAO,SAAS,OAAO;AAC7B,WAAO,KAAK,iBAAiB,OAAO,GAAG,OAAO,KAC1C,KAAK,iBAAiB,OAAO,OAAO,MAAM,IAAI,MAAM;AAAA,EAC5D;AAAA,EACA,eAAe,QAAQ;AACnB,WAAO,KAAK,KAAK,QAAQ,KAAK,KAAK,OAAO,EAAE,QAAQ,iBAAiB,CAAC,GAAG,MAAM;AAC3E,UAAI,KAAK;AACL,eAAO,OAAO,MAAM,CAAC;AACzB,UAAI,KAAK;AACL,eAAO;AACX,eAAS,IAAI,EAAE,QAAQ,IAAI,GAAG,KAAK;AAC/B,YAAI,IAAI,CAAC,EAAE,MAAM,GAAG,CAAC;AACrB,YAAI,IAAI,KAAK,IAAI,OAAO,MAAM;AAC1B,iBAAO,OAAO,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC;AAAA,MAC1C;AACA,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA,EACA,SAAS,OAAO,OAAO;AACnB,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,GAAG,MAAM,IAAI,MAAM,GAAG,SAAS,CAAC;AAC5E,WAAO,CAAC,OAAO,KAAK,EAAE,MAAM;AACxB,UAAI,OAAO,UAAU;AACjB,eAAO;AACX,aAAO,KAAK,OAAO,KAAK;AAAA,IAC5B;AACA,WAAO;AAAA,EACX;AAAA,EACA,UAAU,OAAO,MAAM,IAAI,KAAK;AAC5B,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,KAAK;AAAA,MAAI;AAAA,MAAG,OAAO;AAAA;AAAA,IAAgC,GAAG,KAAK,IAAI,KAAK,KAAkC,MAAM,IAAI,MAAM,CAAC;AACnK,WAAO,CAAC,OAAO,KAAK,EAAE;AAClB,UAAI,OAAO,MAAM,MAAM,OAAO,MAAM,EAAE;AAAA,EAC9C;AACJ;AAQA,IAAM,iBAA8B,YAAY,OAAO;AACvD,IAAM,cAA2B,YAAY,OAAO;AACpD,IAAM,cAA2B,WAAW,OAAO;AAAA,EAC/C,OAAO,OAAO;AACV,WAAO,IAAI,YAAY,aAAa,KAAK,EAAE,OAAO,GAAG,IAAI;AAAA,EAC7D;AAAA,EACA,OAAO,OAAO,IAAI;AACd,aAAS,UAAU,GAAG,SAAS;AAC3B,UAAI,OAAO,GAAG,cAAc;AACxB,gBAAQ,IAAI,YAAY,OAAO,MAAM,OAAO,GAAG,MAAM,KAAK;AAAA,eACrD,OAAO,GAAG,WAAW;AAC1B,gBAAQ,IAAI,YAAY,MAAM,OAAO,OAAO,QAAQ,oBAAoB,IAAI;AAAA,IACpF;AACA,WAAO;AAAA,EACX;AAAA,EACA,SAAS,OAAK,UAAU,KAAK,GAAG,SAAO,IAAI,KAAK;AACpD,CAAC;AAID,SAAS,eAAe,OAAO;AAC3B,MAAI,WAAW,MAAM,MAAM,aAAa,KAAK;AAC7C,SAAO,WAAW,SAAS,MAAM,OAAO,aAAa,KAAK;AAC9D;AAIA,SAAS,gBAAgB,OAAO;AAC5B,MAAI;AACJ,WAAS,KAAK,MAAM,MAAM,aAAa,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU;AACrG;AACA,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,OAAO,OAAO;AACtB,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACjB;AACJ;AACA,IAAM,YAAyB,WAAW,KAAK,EAAE,OAAO,iBAAiB,CAAC;AAA1E,IAA6E,oBAAiC,WAAW,KAAK,EAAE,OAAO,yCAAyC,CAAC;AACjL,IAAM,oBAAiC,WAAW,UAAU,MAAM;AAAA,EAC9D,YAAY,MAAM;AACd,SAAK,OAAO;AACZ,SAAK,cAAc,KAAK,UAAU,KAAK,MAAM,MAAM,WAAW,CAAC;AAAA,EACnE;AAAA,EACA,OAAO,QAAQ;AACX,QAAI,QAAQ,OAAO,MAAM,MAAM,WAAW;AAC1C,QAAI,SAAS,OAAO,WAAW,MAAM,WAAW,KAAK,OAAO,cAAc,OAAO,gBAAgB,OAAO;AACpG,WAAK,cAAc,KAAK,UAAU,KAAK;AAAA,EAC/C;AAAA,EACA,UAAU,EAAE,OAAO,MAAM,GAAG;AACxB,QAAI,CAAC,SAAS,CAAC,MAAM,KAAK;AACtB,aAAO,WAAW;AACtB,QAAI,EAAE,KAAK,IAAI;AACf,QAAI,UAAU,IAAI,gBAAgB;AAClC,aAAS,IAAI,GAAG,SAAS,KAAK,eAAe,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AACxE,UAAI,EAAE,MAAM,GAAG,IAAI,OAAO,CAAC;AAC3B,aAAO,IAAI,IAAI,KAAK,KAAK,OAAO,IAAI,CAAC,EAAE,OAAO,IAAI;AAC9C,aAAK,OAAO,EAAE,CAAC,EAAE;AACrB,YAAM,UAAU,KAAK,OAAO,MAAM,IAAI,CAACC,OAAMC,QAAO;AAChD,YAAI,WAAW,KAAK,MAAM,UAAU,OAAO,KAAK,OAAK,EAAE,QAAQD,SAAQ,EAAE,MAAMC,GAAE;AACjF,gBAAQ,IAAID,OAAMC,KAAI,WAAW,oBAAoB,SAAS;AAAA,MAClE,CAAC;AAAA,IACL;AACA,WAAO,QAAQ,OAAO;AAAA,EAC1B;AACJ,GAAG;AAAA,EACC,aAAa,OAAK,EAAE;AACxB,CAAC;AACD,SAAS,cAAc,GAAG;AACtB,SAAO,UAAQ;AACX,QAAI,QAAQ,KAAK,MAAM,MAAM,aAAa,KAAK;AAC/C,WAAO,SAAS,MAAM,MAAM,KAAK,QAAQ,EAAE,MAAM,KAAK,IAAI,gBAAgB,IAAI;AAAA,EAClF;AACJ;AAOA,IAAM,WAAwB,cAAc,CAAC,MAAM,EAAE,MAAM,MAAM;AAC7D,MAAI,EAAE,GAAG,IAAI,KAAK,MAAM,UAAU;AAClC,MAAI,OAAO,MAAM,UAAU,KAAK,OAAO,IAAI,EAAE;AAC7C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,YAAY,gBAAgB,OAAO,KAAK,MAAM,KAAK,EAAE;AACzD,MAAI,SAAS,KAAK,MAAM,MAAM,iBAAiB;AAC/C,OAAK,SAAS;AAAA,IACV;AAAA,IACA,SAAS,CAAC,cAAc,MAAM,IAAI,GAAG,OAAO,cAAc,UAAU,MAAM,IAAI,CAAC;AAAA,IAC/E,WAAW;AAAA,EACf,CAAC;AACD,oBAAkB,IAAI;AACtB,SAAO;AACX,CAAC;AAMD,IAAM,eAA4B,cAAc,CAAC,MAAM,EAAE,MAAM,MAAM;AACjE,MAAI,EAAE,MAAM,IAAI,MAAM,EAAE,KAAK,IAAI,MAAM,UAAU;AACjD,MAAI,OAAO,MAAM,UAAU,OAAO,MAAM,IAAI;AAC5C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,YAAY,gBAAgB,OAAO,KAAK,MAAM,KAAK,EAAE;AACzD,MAAI,SAAS,KAAK,MAAM,MAAM,iBAAiB;AAC/C,OAAK,SAAS;AAAA,IACV;AAAA,IACA,SAAS,CAAC,cAAc,MAAM,IAAI,GAAG,OAAO,cAAc,UAAU,MAAM,IAAI,CAAC;AAAA,IAC/E,WAAW;AAAA,EACf,CAAC;AACD,oBAAkB,IAAI;AACtB,SAAO;AACX,CAAC;AAID,IAAM,gBAA6B,cAAc,CAAC,MAAM,EAAE,MAAM,MAAM;AAClE,MAAI,SAAS,MAAM,SAAS,KAAK,OAAO,GAAI;AAC5C,MAAI,CAAC,UAAU,CAAC,OAAO;AACnB,WAAO;AACX,OAAK,SAAS;AAAA,IACV,WAAW,gBAAgB,OAAO,OAAO,IAAI,OAAK,gBAAgB,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;AAAA,IACtF,WAAW;AAAA,EACf,CAAC;AACD,SAAO;AACX,CAAC;AAID,IAAM,yBAAyB,CAAC,EAAE,OAAO,SAAS,MAAM;AACpD,MAAI,MAAM,MAAM;AAChB,MAAI,IAAI,OAAO,SAAS,KAAK,IAAI,KAAK;AAClC,WAAO;AACX,MAAI,EAAE,MAAM,GAAG,IAAI,IAAI;AACvB,MAAI,SAAS,CAAC,GAAG,OAAO;AACxB,WAAS,MAAM,IAAI,aAAa,MAAM,KAAK,MAAM,SAAS,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE,QAAO;AACrF,QAAI,OAAO,SAAS;AAChB,aAAO;AACX,QAAI,IAAI,MAAM,QAAQ;AAClB,aAAO,OAAO;AAClB,WAAO,KAAK,gBAAgB,MAAM,IAAI,MAAM,MAAM,IAAI,MAAM,EAAE,CAAC;AAAA,EACnE;AACA,WAAS,MAAM,OAAO;AAAA,IAClB,WAAW,gBAAgB,OAAO,QAAQ,IAAI;AAAA,IAC9C,WAAW;AAAA,EACf,CAAC,CAAC;AACF,SAAO;AACX;AAIA,IAAM,cAA2B,cAAc,CAAC,MAAM,EAAE,MAAM,MAAM;AAChE,MAAI,EAAE,MAAM,IAAI,MAAM,EAAE,MAAM,GAAG,IAAI,MAAM,UAAU;AACrD,MAAI,MAAM;AACN,WAAO;AACX,MAAI,QAAQ,MAAM,UAAU,OAAO,MAAM,IAAI;AAC7C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,OAAO;AACX,MAAI,UAAU,CAAC,GAAG,WAAW;AAC7B,MAAI,UAAU,CAAC;AACf,MAAI,KAAK,QAAQ,QAAQ,KAAK,MAAM,IAAI;AACpC,kBAAc,MAAM,OAAO,MAAM,eAAe,IAAI,CAAC;AACrD,YAAQ,KAAK,EAAE,MAAM,KAAK,MAAM,IAAI,KAAK,IAAI,QAAQ,YAAY,CAAC;AAClE,WAAO,MAAM,UAAU,OAAO,KAAK,MAAM,KAAK,EAAE;AAChD,YAAQ,KAAK,WAAW,SAAS,GAAG,MAAM,OAAO,4BAA4B,MAAM,IAAI,OAAO,IAAI,EAAE,MAAM,IAAI,GAAG,CAAC;AAAA,EACtH;AACA,MAAI,YAAY,KAAK,MAAM,QAAQ,OAAO;AAC1C,MAAI,MAAM;AACN,gBAAY,gBAAgB,OAAO,KAAK,MAAM,KAAK,EAAE,EAAE,IAAI,SAAS;AACpE,YAAQ,KAAK,cAAc,MAAM,IAAI,CAAC;AACtC,YAAQ,KAAK,MAAM,MAAM,iBAAiB,EAAE,cAAc,UAAU,MAAM,IAAI,CAAC;AAAA,EACnF;AACA,OAAK,SAAS;AAAA,IACV,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA,WAAW;AAAA,EACf,CAAC;AACD,SAAO;AACX,CAAC;AAKD,IAAM,aAA0B,cAAc,CAAC,MAAM,EAAE,MAAM,MAAM;AAC/D,MAAI,KAAK,MAAM;AACX,WAAO;AACX,MAAI,UAAU,MAAM,SAAS,KAAK,OAAO,GAAG,EAAE,IAAI,WAAS;AACvD,QAAI,EAAE,MAAM,GAAG,IAAI;AACnB,WAAO,EAAE,MAAM,IAAI,QAAQ,MAAM,eAAe,KAAK,EAAE;AAAA,EAC3D,CAAC;AACD,MAAI,CAAC,QAAQ;AACT,WAAO;AACX,MAAI,eAAe,KAAK,MAAM,OAAO,sBAAsB,QAAQ,MAAM,IAAI;AAC7E,OAAK,SAAS;AAAA,IACV;AAAA,IACA,SAAS,WAAW,SAAS,GAAG,YAAY;AAAA,IAC5C,WAAW;AAAA,EACf,CAAC;AACD,SAAO;AACX,CAAC;AACD,SAAS,kBAAkB,MAAM;AAC7B,SAAO,KAAK,MAAM,MAAM,iBAAiB,EAAE,YAAY,IAAI;AAC/D;AACA,SAAS,aAAa,OAAO,UAAU;AACnC,MAAI,IAAI,IAAI,IAAI,IAAI;AACpB,MAAI,MAAM,MAAM,UAAU;AAC1B,MAAI,UAAU,IAAI,SAAS,IAAI,KAAK,IAAI,OAAO,MAAM,KAAK,MAAM,SAAS,IAAI,MAAM,IAAI,EAAE;AACzF,MAAI,YAAY,CAAC;AACb,WAAO;AACX,MAAI,SAAS,MAAM,MAAM,iBAAiB;AAC1C,SAAO,IAAI,YAAY;AAAA,IACnB,UAAU,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,aAAa,QAAQ,OAAO,SAAS,KAAK,OAAO,WAAW,UAAU,QAAQ,QAAQ,OAAO,KAAK;AAAA,IAC9K,gBAAgB,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,mBAAmB,QAAQ,OAAO,SAAS,KAAK,OAAO;AAAA,IACzI,UAAU,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,aAAa,QAAQ,OAAO,SAAS,KAAK,OAAO;AAAA,IAC7H,SAAS,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,YAAY,QAAQ,OAAO,SAAS,KAAK,OAAO;AAAA,IAC3H,YAAY,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,eAAe,QAAQ,OAAO,SAAS,KAAK,OAAO;AAAA,EACrI,CAAC;AACL;AACA,SAAS,eAAe,MAAM;AAC1B,MAAI,QAAQ,SAAS,MAAM,iBAAiB;AAC5C,SAAO,SAAS,MAAM,IAAI,cAAc,cAAc;AAC1D;AACA,SAAS,kBAAkB,MAAM;AAC7B,MAAI,QAAQ,eAAe,IAAI;AAC/B,MAAI,SAAS,SAAS,KAAK,KAAK;AAC5B,UAAM,OAAO;AACrB;AAIA,IAAM,kBAAkB,UAAQ;AAC5B,MAAI,QAAQ,KAAK,MAAM,MAAM,aAAa,KAAK;AAC/C,MAAI,SAAS,MAAM,OAAO;AACtB,QAAI,cAAc,eAAe,IAAI;AACrC,QAAI,eAAe,eAAe,KAAK,KAAK,eAAe;AACvD,UAAI,QAAQ,aAAa,KAAK,OAAO,MAAM,MAAM,IAAI;AACrD,UAAI,MAAM;AACN,aAAK,SAAS,EAAE,SAAS,eAAe,GAAG,KAAK,EAAE,CAAC;AACvD,kBAAY,MAAM;AAClB,kBAAY,OAAO;AAAA,IACvB;AAAA,EACJ,OACK;AACD,SAAK,SAAS,EAAE,SAAS;AAAA,MACjB,YAAY,GAAG,IAAI;AAAA,MACnB,QAAQ,eAAe,GAAG,aAAa,KAAK,OAAO,MAAM,MAAM,IAAI,CAAC,IAAI,YAAY,aAAa,GAAG,gBAAgB;AAAA,IACxH,EAAE,CAAC;AAAA,EACX;AACA,SAAO;AACX;AAIA,IAAM,mBAAmB,UAAQ;AAC7B,MAAI,QAAQ,KAAK,MAAM,MAAM,aAAa,KAAK;AAC/C,MAAI,CAAC,SAAS,CAAC,MAAM;AACjB,WAAO;AACX,MAAI,QAAQ,SAAS,MAAM,iBAAiB;AAC5C,MAAI,SAAS,MAAM,IAAI,SAAS,KAAK,KAAK,aAAa;AACnD,SAAK,MAAM;AACf,OAAK,SAAS,EAAE,SAAS,YAAY,GAAG,KAAK,EAAE,CAAC;AAChD,SAAO;AACX;AAUA,IAAM,eAAe;AAAA,EACjB,EAAE,KAAK,SAAS,KAAK,iBAAiB,OAAO,sBAAsB;AAAA,EACnE,EAAE,KAAK,MAAM,KAAK,UAAU,OAAO,cAAc,OAAO,uBAAuB,gBAAgB,KAAK;AAAA,EACpG,EAAE,KAAK,SAAS,KAAK,UAAU,OAAO,cAAc,OAAO,uBAAuB,gBAAgB,KAAK;AAAA,EACvG,EAAE,KAAK,UAAU,KAAK,kBAAkB,OAAO,sBAAsB;AAAA,EACrE,EAAE,KAAK,eAAe,KAAK,uBAAuB;AAAA,EAClD,EAAE,KAAK,aAAa,KAAK,SAAS;AAAA,EAClC,EAAE,KAAK,SAAS,KAAK,sBAAsB,gBAAgB,KAAK;AACpE;AACA,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,MAAM;AACd,SAAK,OAAO;AACZ,QAAI,QAAQ,KAAK,QAAQ,KAAK,MAAM,MAAM,WAAW,EAAE,MAAM;AAC7D,SAAK,SAAS,KAAK,OAAO,KAAK,IAAI;AACnC,SAAK,cAAc,MAAI,SAAS;AAAA,MAC5B,OAAO,MAAM;AAAA,MACb,aAAa,OAAO,MAAM,MAAM;AAAA,MAChC,cAAc,OAAO,MAAM,MAAM;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,cAAc;AAAA,MACd,UAAU,KAAK;AAAA,MACf,SAAS,KAAK;AAAA,IAClB,CAAC;AACD,SAAK,eAAe,MAAI,SAAS;AAAA,MAC7B,OAAO,MAAM;AAAA,MACb,aAAa,OAAO,MAAM,SAAS;AAAA,MACnC,cAAc,OAAO,MAAM,SAAS;AAAA,MACpC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU,KAAK;AAAA,MACf,SAAS,KAAK;AAAA,IAClB,CAAC;AACD,SAAK,YAAY,MAAI,SAAS;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,MACf,UAAU,KAAK;AAAA,IACnB,CAAC;AACD,SAAK,UAAU,MAAI,SAAS;AAAA,MACxB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,MACf,UAAU,KAAK;AAAA,IACnB,CAAC;AACD,SAAK,YAAY,MAAI,SAAS;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,MACf,UAAU,KAAK;AAAA,IACnB,CAAC;AACD,aAAS,OAAO,MAAM,SAAS,SAAS;AACpC,aAAO,MAAI,UAAU,EAAE,OAAO,aAAa,MAAM,SAAS,MAAM,SAAS,GAAG,OAAO;AAAA,IACvF;AACA,SAAK,MAAM,MAAI,OAAO,EAAE,WAAW,CAAC,MAAM,KAAK,QAAQ,CAAC,GAAG,OAAO,YAAY,GAAG;AAAA,MAC7E,KAAK;AAAA,MACL,OAAO,QAAQ,MAAM,SAAS,IAAI,GAAG,CAAC,OAAO,MAAM,MAAM,CAAC,CAAC;AAAA,MAC3D,OAAO,QAAQ,MAAM,aAAa,IAAI,GAAG,CAAC,OAAO,MAAM,UAAU,CAAC,CAAC;AAAA,MACnE,OAAO,UAAU,MAAM,cAAc,IAAI,GAAG,CAAC,OAAO,MAAM,KAAK,CAAC,CAAC;AAAA,MACjE,MAAI,SAAS,MAAM,CAAC,KAAK,WAAW,OAAO,MAAM,YAAY,CAAC,CAAC;AAAA,MAC/D,MAAI,SAAS,MAAM,CAAC,KAAK,SAAS,OAAO,MAAM,QAAQ,CAAC,CAAC;AAAA,MACzD,MAAI,SAAS,MAAM,CAAC,KAAK,WAAW,OAAO,MAAM,SAAS,CAAC,CAAC;AAAA,MAC5D,GAAG,KAAK,MAAM,WAAW,CAAC,IAAI;AAAA,QAC1B,MAAI,IAAI;AAAA,QACR,KAAK;AAAA,QACL,OAAO,WAAW,MAAM,YAAY,IAAI,GAAG,CAAC,OAAO,MAAM,SAAS,CAAC,CAAC;AAAA,QACpE,OAAO,cAAc,MAAM,WAAW,IAAI,GAAG,CAAC,OAAO,MAAM,aAAa,CAAC,CAAC;AAAA,MAC9E;AAAA,MACA,MAAI,UAAU;AAAA,QACV,MAAM;AAAA,QACN,SAAS,MAAM,iBAAiB,IAAI;AAAA,QACpC,cAAc,OAAO,MAAM,OAAO;AAAA,QAClC,MAAM;AAAA,MACV,GAAG,CAAC,GAAG,CAAC;AAAA,IACZ,CAAC;AAAA,EACL;AAAA,EACA,SAAS;AACL,QAAI,QAAQ,IAAI,YAAY;AAAA,MACxB,QAAQ,KAAK,YAAY;AAAA,MACzB,eAAe,KAAK,UAAU;AAAA,MAC9B,QAAQ,KAAK,QAAQ;AAAA,MACrB,WAAW,KAAK,UAAU;AAAA,MAC1B,SAAS,KAAK,aAAa;AAAA,IAC/B,CAAC;AACD,QAAI,CAAC,MAAM,GAAG,KAAK,KAAK,GAAG;AACvB,WAAK,QAAQ;AACb,WAAK,KAAK,SAAS,EAAE,SAAS,eAAe,GAAG,KAAK,EAAE,CAAC;AAAA,IAC5D;AAAA,EACJ;AAAA,EACA,QAAQ,GAAG;AACP,QAAI,iBAAiB,KAAK,MAAM,GAAG,cAAc,GAAG;AAChD,QAAE,eAAe;AAAA,IACrB,WACS,EAAE,WAAW,MAAM,EAAE,UAAU,KAAK,aAAa;AACtD,QAAE,eAAe;AACjB,OAAC,EAAE,WAAW,eAAe,UAAU,KAAK,IAAI;AAAA,IACpD,WACS,EAAE,WAAW,MAAM,EAAE,UAAU,KAAK,cAAc;AACvD,QAAE,eAAe;AACjB,kBAAY,KAAK,IAAI;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,OAAO,QAAQ;AACX,aAAS,MAAM,OAAO;AAClB,eAAS,UAAU,GAAG,SAAS;AAC3B,YAAI,OAAO,GAAG,cAAc,KAAK,CAAC,OAAO,MAAM,GAAG,KAAK,KAAK;AACxD,eAAK,SAAS,OAAO,KAAK;AAAA,MAClC;AAAA,EACR;AAAA,EACA,SAAS,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,YAAY,QAAQ,MAAM;AAC/B,SAAK,aAAa,QAAQ,MAAM;AAChC,SAAK,UAAU,UAAU,MAAM;AAC/B,SAAK,QAAQ,UAAU,MAAM;AAC7B,SAAK,UAAU,UAAU,MAAM;AAAA,EACnC;AAAA,EACA,QAAQ;AACJ,SAAK,YAAY,OAAO;AAAA,EAC5B;AAAA,EACA,IAAI,MAAM;AAAE,WAAO;AAAA,EAAI;AAAA,EACvB,IAAI,MAAM;AAAE,WAAO,KAAK,KAAK,MAAM,MAAM,iBAAiB,EAAE;AAAA,EAAK;AACrE;AACA,SAAS,OAAO,MAAMC,SAAQ;AAAE,SAAO,KAAK,MAAM,OAAOA,OAAM;AAAG;AAClE,IAAM,iBAAiB;AACvB,IAAM,QAAQ;AACd,SAAS,cAAc,MAAM,EAAE,MAAM,GAAG,GAAG;AACvC,MAAI,OAAO,KAAK,MAAM,IAAI,OAAO,IAAI,GAAG,UAAU,KAAK,MAAM,IAAI,OAAO,EAAE,EAAE;AAC5E,MAAI,QAAQ,KAAK,IAAI,KAAK,MAAM,OAAO,cAAc,GAAG,MAAM,KAAK,IAAI,SAAS,KAAK,cAAc;AACnG,MAAI,OAAO,KAAK,MAAM,SAAS,OAAO,GAAG;AACzC,MAAI,SAAS,KAAK,MAAM;AACpB,aAAS,IAAI,GAAG,IAAI,gBAAgB;AAChC,UAAI,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,CAAC,KAAK,MAAM,KAAK,KAAK,CAAC,CAAC,GAAG;AACjD,eAAO,KAAK,MAAM,CAAC;AACnB;AAAA,MACJ;AAAA,EACR;AACA,MAAI,OAAO,SAAS;AAChB,aAAS,IAAI,KAAK,SAAS,GAAG,IAAI,KAAK,SAAS,gBAAgB;AAC5D,UAAI,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,CAAC,KAAK,MAAM,KAAK,KAAK,CAAC,CAAC,GAAG;AACjD,eAAO,KAAK,MAAM,GAAG,CAAC;AACtB;AAAA,MACJ;AAAA,EACR;AACA,SAAO,WAAW,SAAS,GAAG,GAAG,KAAK,MAAM,OAAO,eAAe,CAAC,KAAK,IAAI,IAAI,KAAK,MAAM,OAAO,SAAS,CAAC,IAAI,KAAK,MAAM,GAAG;AAClI;AACA,IAAM,YAAyB,WAAW,UAAU;AAAA,EAChD,uBAAuB;AAAA,IACnB,SAAS;AAAA,IACT,UAAU;AAAA,IACV,kBAAkB;AAAA,MACd,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,IACZ;AAAA,IACA,8BAA8B;AAAA,MAC1B,QAAQ;AAAA,IACZ;AAAA,IACA,0BAA0B;AAAA,MACtB,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,MACP,UAAU;AAAA,MACV,YAAY;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,0BAA0B,EAAE,iBAAiB,YAAY;AAAA,EACzD,yBAAyB,EAAE,iBAAiB,YAAY;AAAA,EACxD,mCAAmC,EAAE,iBAAiB,YAAY;AAAA,EAClE,kCAAkC,EAAE,iBAAiB,YAAY;AACrE,CAAC;AACD,IAAM,mBAAmB;AAAA,EACrB;AAAA,EACa,KAAK,IAAI,iBAAiB;AAAA,EACvC;AACJ;", "names": ["line", "word", "from", "to", "phrase"]}