#!/usr/bin/env node

/**
 * 量化投资平台后端服务启动脚本
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs-extra';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 量化投资平台后端服务启动器');
console.log('=====================================');

/**
 * 检查Node.js版本
 */
function checkNodeVersion() {
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 18) {
    console.error('❌ 需要Node.js 18.0.0或更高版本');
    console.error(`   当前版本: ${nodeVersion}`);
    process.exit(1);
  }
  
  console.log(`✅ Node.js版本检查通过: ${nodeVersion}`);
}

/**
 * 检查依赖包
 */
async function checkDependencies() {
  const packageJsonPath = join(__dirname, 'package.json');
  
  if (!await fs.pathExists(packageJsonPath)) {
    console.error('❌ package.json文件不存在');
    process.exit(1);
  }
  
  const nodeModulesPath = join(__dirname, 'node_modules');
  
  if (!await fs.pathExists(nodeModulesPath)) {
    console.log('📦 依赖包不存在，正在安装...');
    
    return new Promise((resolve, reject) => {
      const npm = spawn('npm', ['install'], {
        cwd: __dirname,
        stdio: 'inherit',
        shell: true
      });
      
      npm.on('close', (code) => {
        if (code === 0) {
          console.log('✅ 依赖包安装完成');
          resolve();
        } else {
          console.error('❌ 依赖包安装失败');
          reject(new Error(`npm install failed with code ${code}`));
        }
      });
    });
  }
  
  console.log('✅ 依赖包检查通过');
}

/**
 * 创建环境配置文件
 */
async function createEnvFile() {
  const envPath = join(__dirname, '.env');
  
  if (!await fs.pathExists(envPath)) {
    const envContent = `# 量化投资平台后端配置
PORT=8000
HOST=127.0.0.1
NODE_ENV=development

# 数据库配置（如果需要）
# DB_HOST=localhost
# DB_PORT=3306
# DB_NAME=quant_platform
# DB_USER=root
# DB_PASS=

# JWT密钥
JWT_SECRET=your-jwt-secret-key

# 日志级别
LOG_LEVEL=info

# 是否启用Mock数据
ENABLE_MOCK_DATA=true
`;
    
    await fs.writeFile(envPath, envContent);
    console.log('✅ 环境配置文件创建完成: .env');
  }
}

/**
 * 启动服务器
 */
function startServer() {
  console.log('🚀 正在启动服务器...');
  console.log('');
  
  const server = spawn('node', ['server.js'], {
    cwd: __dirname,
    stdio: 'inherit',
    shell: true,
    env: {
      ...process.env,
      NODE_ENV: process.env.NODE_ENV || 'development'
    }
  });
  
  // 处理进程退出
  process.on('SIGINT', () => {
    console.log('');
    console.log('🛑 收到退出信号，正在关闭服务器...');
    server.kill('SIGINT');
  });
  
  process.on('SIGTERM', () => {
    console.log('');
    console.log('🛑 收到终止信号，正在关闭服务器...');
    server.kill('SIGTERM');
  });
  
  server.on('close', (code) => {
    if (code === 0) {
      console.log('✅ 服务器已正常关闭');
    } else {
      console.error(`❌ 服务器异常退出，退出码: ${code}`);
    }
    process.exit(code);
  });
  
  server.on('error', (error) => {
    console.error('❌ 服务器启动失败:', error.message);
    process.exit(1);
  });
}

/**
 * 主函数
 */
async function main() {
  try {
    // 检查环境
    checkNodeVersion();
    await checkDependencies();
    await createEnvFile();
    
    console.log('');
    console.log('🎉 环境检查完成，启动服务器...');
    console.log('');
    
    // 启动服务器
    startServer();
    
  } catch (error) {
    console.error('❌ 启动失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main();