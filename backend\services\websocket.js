/**
 * WebSocket服务
 * 提供实时数据推送功能
 */
import { WebSocketServer } from 'ws';
import { v4 as uuidv4 } from 'uuid';

export class WebSocketService {
  constructor(server, marketDataService) {
    this.server = server;
    this.marketDataService = marketDataService;
    this.wss = null;
    this.clients = new Map();
    this.subscriptions = new Map();
  }

  /**
   * 初始化WebSocket服务
   */
  async initialize() {
    try {
      // 创建WebSocket服务器
      this.wss = new WebSocketServer({
        server: this.server,
        path: '/api/v1/ws'
      });

      // 设置连接处理
      this.wss.on('connection', (ws, request) => {
        this.handleConnection(ws, request);
      });

      // 监听市场数据更新
      this.marketDataService.on('priceUpdate', (data) => {
        this.broadcastPriceUpdate(data);
      });

      console.log('🌐 WebSocket服务器已启动: /api/v1/ws');
      
    } catch (error) {
      console.error('❌ WebSocket服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 处理WebSocket连接
   */
  handleConnection(ws, request) {
    const clientId = uuidv4();
    const clientInfo = {
      id: clientId,
      ws,
      subscriptions: new Set(),
      connected: true,
      connectedAt: new Date().toISOString(),
      ip: request.socket.remoteAddress,
      userAgent: request.headers['user-agent']
    };

    this.clients.set(clientId, clientInfo);
    
    console.log(`📡 新的WebSocket连接: ${clientId} (${request.socket.remoteAddress})`);

    // 发送连接确认
    this.sendMessage(ws, {
      type: 'connection',
      data: {
        clientId,
        message: 'WebSocket连接成功',
        timestamp: new Date().toISOString()
      }
    });

    // 处理消息
    ws.on('message', (data) => {
      this.handleMessage(clientId, data);
    });

    // 处理连接关闭
    ws.on('close', () => {
      this.handleDisconnection(clientId);
    });

    // 处理错误
    ws.on('error', (error) => {
      console.error(`WebSocket错误 (${clientId}):`, error);
      this.handleDisconnection(clientId);
    });

    // 发送心跳
    this.startHeartbeat(clientId);
  }

  /**
   * 处理客户端消息
   */
  handleMessage(clientId, data) {
    try {
      const client = this.clients.get(clientId);
      if (!client) return;

      const message = JSON.parse(data.toString());
      
      switch (message.type) {
        case 'subscribe':
          this.handleSubscription(clientId, message);
          break;
        case 'unsubscribe':
          this.handleUnsubscription(clientId, message);
          break;
        case 'ping':
          this.handlePing(clientId);
          break;
        case 'getSnapshot':
          this.handleSnapshotRequest(clientId, message);
          break;
        default:
          console.warn(`未知消息类型: ${message.type}`);
      }
      
    } catch (error) {
      console.error(`处理WebSocket消息错误 (${clientId}):`, error);
    }
  }

  /**
   * 处理订阅请求
   */
  async handleSubscription(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { channel, symbols = [], params = {} } = message.data || {};

    if (!channel) {
      this.sendError(client.ws, 'INVALID_CHANNEL', '频道名称不能为空');
      return;
    }

    // 添加订阅
    const subscriptionKey = `${channel}:${symbols.join(',')}`;
    client.subscriptions.add(subscriptionKey);

    // 全局订阅管理
    if (!this.subscriptions.has(subscriptionKey)) {
      this.subscriptions.set(subscriptionKey, new Set());
    }
    this.subscriptions.get(subscriptionKey).add(clientId);

    console.log(`📊 客户端 ${clientId} 订阅: ${subscriptionKey}`);

    // 发送订阅确认
    this.sendMessage(client.ws, {
      type: 'subscribed',
      data: {
        channel,
        symbols,
        message: '订阅成功'
      }
    });

    // 立即发送当前数据
    await this.sendInitialData(clientId, channel, symbols);
  }

  /**
   * 发送初始数据
   */
  async sendInitialData(clientId, channel, symbols) {
    const client = this.clients.get(clientId);
    if (!client) return;

    try {
      switch (channel) {
        case 'quotes':
          if (symbols.length > 0) {
            const quotes = await this.marketDataService.getQuotes(symbols);
            this.sendMessage(client.ws, {
              type: 'quotes',
              data: quotes
            });
          }
          break;
        
        case 'depth':
          for (const symbol of symbols) {
            const depth = await this.marketDataService.getDepthData(symbol);
            if (depth) {
              this.sendMessage(client.ws, {
                type: 'depth',
                data: depth
              });
            }
          }
          break;
        
        case 'market_overview':
          const overview = await this.marketDataService.getMarketOverview();
          this.sendMessage(client.ws, {
            type: 'market_overview',
            data: overview
          });
          break;
      }
    } catch (error) {
      console.error(`发送初始数据错误 (${clientId}):`, error);
    }
  }

  /**
   * 处理取消订阅
   */
  handleUnsubscription(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { channel, symbols = [] } = message.data || {};
    const subscriptionKey = `${channel}:${symbols.join(',')}`;
    
    client.subscriptions.delete(subscriptionKey);
    
    if (this.subscriptions.has(subscriptionKey)) {
      this.subscriptions.get(subscriptionKey).delete(clientId);
      if (this.subscriptions.get(subscriptionKey).size === 0) {
        this.subscriptions.delete(subscriptionKey);
      }
    }

    console.log(`📊 客户端 ${clientId} 取消订阅: ${subscriptionKey}`);

    this.sendMessage(client.ws, {
      type: 'unsubscribed',
      data: {
        channel,
        symbols,
        message: '取消订阅成功'
      }
    });
  }

  /**
   * 处理ping消息
   */
  handlePing(clientId) {
    const client = this.clients.get(clientId);
    if (!client) return;

    this.sendMessage(client.ws, {
      type: 'pong',
      data: {
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * 处理快照数据请求
   */
  async handleSnapshotRequest(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { dataType, symbols = [] } = message.data || {};

    try {
      let data;
      switch (dataType) {
        case 'quotes':
          data = await this.marketDataService.getQuotes(symbols);
          break;
        case 'market_overview':
          data = await this.marketDataService.getMarketOverview();
          break;
        case 'sectors':
          data = await this.marketDataService.getSectors();
          break;
        default:
          this.sendError(client.ws, 'INVALID_DATA_TYPE', '不支持的数据类型');
          return;
      }

      this.sendMessage(client.ws, {
        type: 'snapshot',
        data: {
          dataType,
          data,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error(`获取快照数据错误 (${clientId}):`, error);
      this.sendError(client.ws, 'SNAPSHOT_ERROR', '获取快照数据失败');
    }
  }

  /**
   * 广播价格更新
   */
  broadcastPriceUpdate(updateData) {
    const { symbol, data } = updateData;
    
    // 查找订阅了该股票的客户端
    for (const [subscriptionKey, clientIds] of this.subscriptions) {
      if (subscriptionKey.includes('quotes') && 
          (subscriptionKey.includes(symbol) || subscriptionKey.includes('quotes:'))) {
        
        for (const clientId of clientIds) {
          const client = this.clients.get(clientId);
          if (client && client.connected) {
            this.sendMessage(client.ws, {
              type: 'price_update',
              data: {
                symbol,
                quote: data,
                timestamp: new Date().toISOString()
              }
            });
          }
        }
      }
    }
  }

  /**
   * 启动心跳检测
   */
  startHeartbeat(clientId) {
    const heartbeatInterval = setInterval(() => {
      const client = this.clients.get(clientId);
      if (!client || !client.connected) {
        clearInterval(heartbeatInterval);
        return;
      }

      // 发送心跳
      try {
        this.sendMessage(client.ws, {
          type: 'heartbeat',
          data: {
            timestamp: new Date().toISOString()
          }
        });
      } catch (error) {
        console.error(`心跳发送失败 (${clientId}):`, error);
        clearInterval(heartbeatInterval);
        this.handleDisconnection(clientId);
      }
    }, 30000); // 30秒心跳间隔
  }

  /**
   * 处理连接断开
   */
  handleDisconnection(clientId) {
    const client = this.clients.get(clientId);
    if (client) {
      client.connected = false;
      
      // 清理订阅
      for (const subscriptionKey of client.subscriptions) {
        if (this.subscriptions.has(subscriptionKey)) {
          this.subscriptions.get(subscriptionKey).delete(clientId);
          if (this.subscriptions.get(subscriptionKey).size === 0) {
            this.subscriptions.delete(subscriptionKey);
          }
        }
      }
      
      this.clients.delete(clientId);
      console.log(`📡 WebSocket连接断开: ${clientId}`);
    }
  }

  /**
   * 发送消息给客户端
   */
  sendMessage(ws, message) {
    if (ws.readyState === ws.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  /**
   * 发送错误消息
   */
  sendError(ws, code, message) {
    this.sendMessage(ws, {
      type: 'error',
      data: {
        code,
        message,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * 广播消息给所有连接的客户端
   */
  broadcast(message) {
    for (const client of this.clients.values()) {
      if (client.connected) {
        this.sendMessage(client.ws, message);
      }
    }
  }

  /**
   * 获取连接统计信息
   */
  getStats() {
    return {
      totalConnections: this.clients.size,
      activeSubscriptions: this.subscriptions.size,
      connectedClients: Array.from(this.clients.values())
        .filter(client => client.connected)
        .map(client => ({
          id: client.id,
          connectedAt: client.connectedAt,
          subscriptions: Array.from(client.subscriptions),
          ip: client.ip
        }))
    };
  }
}