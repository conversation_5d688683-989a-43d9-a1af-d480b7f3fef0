import{r as B,c as u,t as P,s as e,i as p,f as M,m as G,b as m,A as H,g as J,e as K}from"./index-B8TYkGLu.js";import{S as O}from"./SimpleSlideVerify-DHA9A53Q.js";var W=P('<div style=min-height:100vh;align-items:center;justify-content:center><div style="border-radius:12px;box-shadow:0 20px 40px rgba(0,0,0,0.1);max-width:400px"><div style=text-align:center;margin-bottom:30px><div style=border-radius:12px;align-items:center;justify-content:center;font-size:24px;font-weight:bold>Q</div><h1 style=font-size:24px;font-weight:bold>量化交易平台</h1><p>请登录您的账户</p></div><form><div style=margin-bottom:20px><label style=font-size:14px;font-weight:500;margin-bottom:6px>用户名</label><input type=text placeholder=请输入用户名 style=border-radius:6px;font-size:14px;box-sizing:border-box></div><div style=margin-bottom:20px><label style=font-size:14px;font-weight:500;margin-bottom:6px>密码</label><input type=password placeholder=请输入密码 style=border-radius:6px;font-size:14px;box-sizing:border-box></div><div style=margin-bottom:20px><label style=font-size:14px;font-weight:500;margin-bottom:6px>安全验证</label></div><button type=submit style=border-radius:6px;font-size:16px;font-weight:500;margin-bottom:16px></button><div style=text-align:center></div></form><div style=margin-top:24px;background-color:#f3f4f6;border-radius:6px;font-size:12px;text-align:center><div style=font-weight:500;margin-bottom:4px>演示账户</div><div>用户名: admin</div><div>密码: 123456'),X=P("<div style=border-radius:6px;font-size:14px;text-align:center;margin-bottom:16px>");function ee(){const R=B(),[h,A]=u(""),[y,D]=u(""),[N,x]=u(!1),[r,a]=u(""),[c,z]=u(!1),Q=()=>{x(!0),a("✅ 滑动验证成功")},U=()=>{x(!1),a("❌ 滑动验证失败，请重试")},q=async s=>{if(s.preventDefault(),!N()){a("❌ 请先完成滑动验证");return}if(!h()||!y()){a("❌ 请输入用户名和密码");return}z(!0),a("🔄 正在登录...");try{await J.login({username:h(),password:y()}),a("✅ 登录成功！正在跳转..."),setTimeout(()=>{R("/")},1e3)}catch{a("❌ 用户名或密码错误"),x(!1)}finally{z(!1)}};return(()=>{var s=W(),g=s.firstChild,k=g.firstChild,b=k.firstChild,w=b.nextSibling,L=w.nextSibling,v=k.nextSibling,T=v.firstChild,$=T.firstChild,o=$.nextSibling,E=T.nextSibling,S=E.firstChild,l=S.nextSibling,_=E.nextSibling,V=_.firstChild,i=_.nextSibling,j=i.nextSibling,C=v.nextSibling;return C.firstChild,e(s,"background","linear-gradient(135deg, #667eea 0%, #764ba2 100%)"),e(s,"display","flex"),e(s,"padding","20px"),e(g,"background","white"),e(g,"padding","40px"),e(g,"width","100%"),e(b,"width","60px"),e(b,"height","60px"),e(b,"background","linear-gradient(135deg, #3b82f6, #1d4ed8)"),e(b,"display","flex"),e(b,"margin","0 auto 16px"),e(b,"color","white"),e(w,"color","#111827"),e(w,"margin","0 0 8px"),e(L,"color","#6b7280"),e(L,"margin","0"),v.addEventListener("submit",q),e($,"display","block"),e($,"color","#374151"),o.addEventListener("blur",t=>t.currentTarget.style.borderColor="#d1d5db"),o.addEventListener("focus",t=>t.currentTarget.style.borderColor="#3b82f6"),o.$$input=t=>A(t.currentTarget.value),e(o,"width","100%"),e(o,"padding","12px"),e(o,"border","1px solid #d1d5db"),e(o,"outline","none"),e(o,"transition","border-color 0.2s"),e(S,"display","block"),e(S,"color","#374151"),l.addEventListener("blur",t=>t.currentTarget.style.borderColor="#d1d5db"),l.addEventListener("focus",t=>t.currentTarget.style.borderColor="#3b82f6"),l.$$input=t=>D(t.currentTarget.value),e(l,"width","100%"),e(l,"padding","12px"),e(l,"border","1px solid #d1d5db"),e(l,"outline","none"),e(l,"transition","border-color 0.2s"),e(V,"display","block"),e(V,"color","#374151"),p(_,M(O,{width:320,height:120,sliderText:"向右滑动完成验证",onSuccess:Q,onFail:U,onRefresh:()=>x(!1)}),null),i.addEventListener("mouseleave",t=>{c()||(t.currentTarget.style.backgroundColor="#3b82f6")}),i.addEventListener("mouseenter",t=>{c()||(t.currentTarget.style.backgroundColor="#2563eb")}),e(i,"width","100%"),e(i,"padding","12px"),e(i,"color","white"),e(i,"border","none"),e(i,"transition","background-color 0.2s"),p(i,()=>c()?"登录中...":"登录"),p(v,(()=>{var t=G(()=>!!r());return()=>t()&&(()=>{var d=X();return e(d,"padding","12px"),p(d,r),m(n=>{var f=r().includes("成功")?"#f0f9ff":r().includes("失败")||r().includes("错误")?"#fef2f2":"#f9fafb",F=r().includes("成功")?"#1e40af":r().includes("失败")||r().includes("错误")?"#dc2626":"#374151",I=`1px solid ${r().includes("成功")?"#bfdbfe":r().includes("失败")||r().includes("错误")?"#fecaca":"#e5e7eb"}`;return f!==n.e&&e(d,"background-color",n.e=f),F!==n.t&&e(d,"color",n.t=F),I!==n.a&&e(d,"border",n.a=I),n},{e:void 0,t:void 0,a:void 0}),d})()})(),j),p(j,M(H,{href:"/",style:{color:"#6b7280","font-size":"14px","text-decoration":"none"},children:"← 返回首页"})),e(C,"padding","12px"),e(C,"color","#6b7280"),m(t=>{var d=c(),n=c()?"#9ca3af":"#3b82f6",f=c()?"not-allowed":"pointer";return d!==t.e&&(i.disabled=t.e=d),n!==t.t&&e(i,"background",t.t=n),f!==t.a&&e(i,"cursor",t.a=f),t},{e:void 0,t:void 0,a:void 0}),m(()=>o.value=h()),m(()=>l.value=y()),s})()}K(["input"]);export{ee as default};
