/**
 * 回测 Worker
 * 在后台线程中执行回测计算
 */

// 技术指标计算函数
function calculateSMA(data, period) {
  const result = [];
  for (let i = period - 1; i < data.length; i++) {
    const sum = data.slice(i - period + 1, i + 1).reduce((acc, val) => acc + val.close, 0);
    result.push({
      time: data[i].time,
      value: sum / period
    });
  }
  return result;
}

function calculateEMA(data, period) {
  const result = [];
  const multiplier = 2 / (period + 1);
  let ema = data[0]?.close || 0;
  
  for (let i = 0; i < data.length; i++) {
    if (i === 0) {
      ema = data[i].close;
    } else {
      ema = (data[i].close - ema) * multiplier + ema;
    }
    result.push({
      time: data[i].time,
      value: ema
    });
  }
  return result;
}

function calculateRSI(data, period = 14) {
  const result = [];
  const gains = [];
  const losses = [];
  
  for (let i = 1; i < data.length; i++) {
    const change = data[i].close - data[i - 1].close;
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? -change : 0);
  }
  
  for (let i = period - 1; i < gains.length; i++) {
    const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
    const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
    
    const rs = avgGain / avgLoss;
    const rsi = 100 - (100 / (1 + rs));
    
    result.push({
      time: data[i + 1].time,
      value: rsi
    });
  }
  
  return result;
}

function calculateMACD(data, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
  const emaFast = calculateEMA(data, fastPeriod);
  const emaSlow = calculateEMA(data, slowPeriod);
  
  const macdLine = [];
  for (let i = 0; i < Math.min(emaFast.length, emaSlow.length); i++) {
    macdLine.push({
      time: emaFast[i].time,
      value: emaFast[i].value - emaSlow[i].value
    });
  }
  
  const signalLine = calculateEMA(macdLine.map(item => ({ time: item.time, close: item.value })), signalPeriod);
  
  const histogram = [];
  for (let i = 0; i < Math.min(macdLine.length, signalLine.length); i++) {
    histogram.push({
      time: macdLine[i].time,
      value: macdLine[i].value - signalLine[i].value
    });
  }
  
  return { macd: macdLine, signal: signalLine, histogram };
}

// 回测引擎
function runBacktest(strategy, data, config) {
  const {
    initialCapital = 100000,
    commission = 0.001,
    slippage = 0.001
  } = config;
  
  let capital = initialCapital;
  let position = 0;
  let trades = [];
  let equity = [{ time: data[0].time, value: capital }];
  
  // 执行策略代码（简化版本）
  const signals = executeStrategy(strategy, data);
  
  for (let i = 1; i < data.length; i++) {
    const currentPrice = data[i].close;
    const signal = signals[i] || 0;
    const prevSignal = signals[i - 1] || 0;
    
    // 检查信号变化
    if (signal !== prevSignal) {
      if (signal > 0 && position <= 0) {
        // 买入信号
        const cost = currentPrice * (1 + slippage + commission);
        const shares = Math.floor(capital / cost);
        if (shares > 0) {
          position = shares;
          capital -= shares * cost;
          trades.push({
            time: data[i].time,
            type: 'buy',
            price: cost,
            shares: shares,
            capital: capital
          });
        }
      } else if (signal < 0 && position > 0) {
        // 卖出信号
        const revenue = currentPrice * (1 - slippage - commission);
        capital += position * revenue;
        trades.push({
          time: data[i].time,
          type: 'sell',
          price: revenue,
          shares: position,
          capital: capital
        });
        position = 0;
      }
    }
    
    // 计算当前权益
    const currentEquity = capital + position * currentPrice;
    equity.push({
      time: data[i].time,
      value: currentEquity
    });
  }
  
  // 计算性能指标
  const finalEquity = equity[equity.length - 1].value;
  const totalReturn = (finalEquity - initialCapital) / initialCapital;
  const returns = equity.slice(1).map((item, i) => 
    (item.value - equity[i].value) / equity[i].value
  );
  
  const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length;
  const stdReturn = Math.sqrt(
    returns.reduce((acc, ret) => acc + Math.pow(ret - avgReturn, 2), 0) / returns.length
  );
  const sharpeRatio = avgReturn / stdReturn * Math.sqrt(252); // 年化
  
  const drawdowns = [];
  let peak = initialCapital;
  let maxDrawdown = 0;
  
  for (const point of equity) {
    if (point.value > peak) {
      peak = point.value;
    }
    const drawdown = (peak - point.value) / peak;
    drawdowns.push(drawdown);
    if (drawdown > maxDrawdown) {
      maxDrawdown = drawdown;
    }
  }
  
  const winningTrades = trades.filter((trade, i) => {
    if (trade.type === 'sell' && i > 0) {
      const buyTrade = trades[i - 1];
      return trade.price > buyTrade.price;
    }
    return false;
  });
  
  const winRate = trades.length > 0 ? winningTrades.length / (trades.length / 2) : 0;
  
  return {
    initialCapital,
    finalCapital: finalEquity,
    totalReturn,
    sharpeRatio: isNaN(sharpeRatio) ? 0 : sharpeRatio,
    maxDrawdown,
    trades: trades.length,
    winRate,
    equity,
    trades: trades,
    drawdowns
  };
}

// 简化的策略执行器
function executeStrategy(strategy, data) {
  // 这里应该执行实际的策略代码
  // 为了演示，我们使用一个简单的移动平均策略
  const signals = new Array(data.length).fill(0);
  
  if (strategy.type === 'moving_average' || !strategy.code) {
    const shortMA = calculateSMA(data, 5);
    const longMA = calculateSMA(data, 20);
    
    for (let i = 20; i < data.length; i++) {
      const shortValue = shortMA.find(ma => ma.time === data[i].time)?.value;
      const longValue = longMA.find(ma => ma.time === data[i].time)?.value;
      
      if (shortValue && longValue) {
        signals[i] = shortValue > longValue ? 1 : -1;
      }
    }
  }
  
  return signals;
}

// 参数优化
function optimizeParameters(strategy, data, parameterRanges) {
  const results = [];
  
  // 简化的网格搜索
  const params = Object.keys(parameterRanges);
  const combinations = generateParameterCombinations(parameterRanges);
  
  for (const combination of combinations) {
    const modifiedStrategy = { ...strategy, parameters: combination };
    const result = runBacktest(modifiedStrategy, data, {});
    results.push({
      parameters: combination,
      performance: {
        totalReturn: result.totalReturn,
        sharpeRatio: result.sharpeRatio,
        maxDrawdown: result.maxDrawdown,
        winRate: result.winRate
      }
    });
  }
  
  // 按夏普比率排序
  results.sort((a, b) => b.performance.sharpeRatio - a.performance.sharpeRatio);
  
  return results;
}

function generateParameterCombinations(ranges) {
  const keys = Object.keys(ranges);
  const combinations = [];
  
  function generate(index, current) {
    if (index === keys.length) {
      combinations.push({ ...current });
      return;
    }
    
    const key = keys[index];
    const range = ranges[key];
    
    for (let value = range.min; value <= range.max; value += range.step) {
      current[key] = value;
      generate(index + 1, current);
    }
  }
  
  generate(0, {});
  return combinations;
}

// Worker 消息处理
self.onmessage = function(event) {
  const { id, type, data } = event.data;
  
  try {
    let result;
    
    switch (type) {
      case 'backtest':
        result = runBacktest(data.strategy, data.data, data.config);
        break;
        
      case 'indicators':
        result = {};
        for (const indicator of data.indicators) {
          switch (indicator) {
            case 'SMA_5':
              result.SMA_5 = calculateSMA(data.data, 5);
              break;
            case 'SMA_20':
              result.SMA_20 = calculateSMA(data.data, 20);
              break;
            case 'EMA_12':
              result.EMA_12 = calculateEMA(data.data, 12);
              break;
            case 'RSI':
              result.RSI = calculateRSI(data.data);
              break;
            case 'MACD':
              result.MACD = calculateMACD(data.data);
              break;
          }
        }
        break;
        
      case 'optimize':
        result = optimizeParameters(data.strategy, data.data, data.parameterRanges);
        break;
        
      default:
        throw new Error(`Unknown task type: ${type}`);
    }
    
    self.postMessage({
      id,
      type,
      success: true,
      data: result,
      timestamp: Date.now()
    });
    
  } catch (error) {
    self.postMessage({
      id,
      type,
      success: false,
      error: error.message,
      timestamp: Date.now()
    });
  }
};
