// This file was generated by lezer-generator. You probably shouldn't edit it.
export const
  noSemi = 315,
  noSemiType = 316,
  incdec = 1,
  incdecPrefix = 2,
  questionDot = 3,
  JSXStartTag = 4,
  insertSemi = 317,
  spaces = 319,
  newline = 320,
  LineComment = 5,
  BlockComment = 6,
  Script = 7,
  Hashbang = 8,
  ExportDeclaration = 9,
  _export = 10,
  Star = 11,
  as = 12,
  VariableName = 13,
  String = 14,
  Escape = 15,
  from = 16,
  _default = 18,
  FunctionDeclaration = 19,
  async = 64,
  _function = 21,
  VariableDefinition = 22,
  TypeParamList = 25,
  _in = 26,
  out = 27,
  _const = 28,
  TypeDefinition = 29,
  _extends = 30,
  _this = 32,
  Number = 35,
  BooleanLiteral = 36,
  TemplateType = 37,
  InterpolationEnd = 38,
  templateType = 39,
  InterpolationStart = 40,
  _null = 42,
  _void = 44,
  _typeof = 46,
  PropertyName = 49,
  TemplateString = 51,
  templateEscape = 52,
  templateExpr = 53,
  _super = 54,
  RegExp = 55,
  ArrayExpression = 57,
  Property = 63,
  get = 65,
  set = 66,
  PropertyDefinition = 67,
  Block = 68,
  _new = 200,
  ArgList = 75,
  UnaryExpression = 76,
  _delete = 77,
  YieldExpression = 80,
  _yield = 81,
  AwaitExpression = 82,
  _await = 83,
  ParenthesizedExpression = 84,
  ClassExpression = 85,
  _class = 86,
  ClassBody = 87,
  MethodDeclaration = 88,
  Decorator = 89,
  PrivatePropertyName = 92,
  TypeArgList = 94,
  LessThan = 95,
  declare = 225,
  Privacy = 117,
  _static = 99,
  abstract = 208,
  override = 101,
  PrivatePropertyDefinition = 102,
  PropertyDeclaration = 103,
  readonly = 118,
  accessor = 105,
  Optional = 106,
  TypeAnnotation = 107,
  StaticBlock = 109,
  FunctionExpression = 110,
  ArrowFunction = 111,
  ParamList = 113,
  ArrayPattern = 114,
  ObjectPattern = 115,
  PatternProperty = 116,
  MemberExpression = 120,
  BinaryExpression = 121,
  divide = 123,
  _instanceof = 128,
  satisfies = 129,
  questionOp = 137,
  AssignmentExpression = 139,
  _import = 146,
  JSXElement = 148,
  JSXSelfCloseEndTag = 149,
  JSXSelfClosingTag = 150,
  JSXIdentifier = 151,
  JSXLowerIdentifier = 153,
  JSXNamespacedName = 154,
  JSXMemberExpression = 155,
  JSXAttributeValue = 158,
  JSXEndTag = 160,
  JSXOpenTag = 161,
  JSXFragmentTag = 162,
  JSXText = 163,
  JSXEscape = 164,
  JSXStartCloseTag = 165,
  JSXCloseTag = 166,
  tsAngleOpen = 168,
  SequenceExpression = 171,
  keyof = 174,
  unique = 176,
  infer = 179,
  TypeName = 180,
  ParamTypeList = 183,
  IndexedType = 185,
  Label = 187,
  ObjectType = 190,
  MethodType = 191,
  PropertyType = 192,
  IndexSignature = 193,
  TypePredicate = 196,
  asserts = 197,
  is = 198,
  unionOp = 202,
  intersectionOp = 204,
  ClassDeclaration = 207,
  _implements = 209,
  type = 210,
  VariableDeclaration = 211,
  _let = 212,
  _var = 213,
  using = 214,
  TypeAliasDeclaration = 215,
  InterfaceDeclaration = 216,
  _interface = 217,
  EnumDeclaration = 218,
  _enum = 219,
  NamespaceDeclaration = 221,
  namespace = 222,
  module = 223,
  AmbientDeclaration = 224,
  global = 227,
  ExportGroup = 231,
  ImportDeclaration = 234,
  ImportGroup = 235,
  _for = 237,
  ForSpec = 238,
  ForInSpec = 239,
  ForOfSpec = 240,
  of = 241,
  _while = 243,
  _with = 245,
  _do = 247,
  _if = 249,
  _else = 250,
  _switch = 252,
  _case = 255,
  _try = 258,
  _catch = 260,
  _finally = 262,
  _return = 264,
  _throw = 266,
  _break = 268,
  _continue = 270,
  _debugger = 272,
  SingleExpression = 275,
  SingleClassItem = 276,
  Dialect_jsx = 0,
  Dialect_ts = 1
