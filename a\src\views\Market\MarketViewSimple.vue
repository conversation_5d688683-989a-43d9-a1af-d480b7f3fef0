<template>
  <div class="market-view-simple">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>📈 市场行情</h1>
      <p>实时股票数据与技术分析</p>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 市场指数 -->
    <div class="market-indices">
      <el-row :gutter="20">
        <el-col v-for="index in indices" :key="index.symbol" :xs="12" :sm="6">
          <el-card class="index-card">
            <div class="index-name">{{ index.name }}</div>
            <div class="index-value">{{ index.value.toFixed(2) }}</div>
            <div :class="['index-change', index.change >= 0 ? 'positive' : 'negative']">
              {{ (index.change >= 0 ? '+' : '') + index.change.toFixed(2) }}
              ({{ (index.changePercent >= 0 ? '+' : '') + index.changePercent.toFixed(2) }}%)
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 热门股票 -->
    <div class="stock-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <h2>热门股票</h2>
            <span class="stock-count">共 {{ stocks.length }} 只</span>
          </div>
        </template>
        
        <el-table :data="stocks" style="width: 100%" v-loading="loading">
          <el-table-column prop="symbol" label="代码" width="80" />
          <el-table-column prop="name" label="名称" width="120" />
          <el-table-column prop="price" label="现价" width="100" align="right">
            <template #default="{ row }">
              <span :class="row.change >= 0 ? 'positive' : 'negative'">
                ¥{{ row.price.toFixed(2) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="change" label="涨跌额" width="100" align="right">
            <template #default="{ row }">
              <span :class="row.change >= 0 ? 'positive' : 'negative'">
                {{ (row.change >= 0 ? '+' : '') + row.change.toFixed(2) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="changePercent" label="涨跌幅" width="100" align="right">
            <template #default="{ row }">
              <el-tag :type="row.changePercent >= 0 ? 'danger' : 'success'" size="small">
                {{ (row.changePercent >= 0 ? '+' : '') + row.changePercent.toFixed(2) }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="volume" label="成交量" width="120" align="right">
            <template #default="{ row }">
              {{ formatVolume(row.volume) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button size="small" @click="viewDetail(row)">详情</el-button>
              <el-button size="small" type="primary" @click="goToTrade(row)">交易</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import axios from 'axios'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const indices = ref([
  { symbol: 'SH000001', name: '上证指数', value: 3234.56, change: 39.82, changePercent: 1.25 },
  { symbol: 'SZ399001', name: '深证成指', value: 12345.67, change: -55.43, changePercent: -0.45 },
  { symbol: 'SZ399006', name: '创业板指', value: 2567.89, change: 52.78, changePercent: 2.10 },
  { symbol: 'SH000688', name: '科创50', value: 1089.45, change: 12.34, changePercent: 1.15 }
])

const stocks = ref([
  { 
    symbol: '000001', name: '平安银行', price: 13.19, change: 0.69, changePercent: 5.52,
    volume: 97784820
  },
  { 
    symbol: '600036', name: '招商银行', price: 36.80, change: 1.60, changePercent: 4.55,
    volume: 54321000
  },
  { 
    symbol: '000858', name: '五粮液', price: 178.90, change: 13.60, changePercent: 8.23,
    volume: 32109870
  },
  { 
    symbol: '600519', name: '贵州茅台', price: 1680.50, change: -25.30, changePercent: -1.48,
    volume: 18765432
  },
  { 
    symbol: '000002', name: '万科A', price: 10.15, change: -0.31, changePercent: -2.96,
    volume: 87654321
  }
])

// 定时器
let updateTimer: number | null = null

// 方法
async function refreshData() {
  try {
    loading.value = true
    
    // 尝试从后端API获取数据
    try {
      const [indicesRes, stocksRes] = await Promise.all([
        axios.get('/api/v1/market/overview'),
        axios.get('/api/v1/market/quotes?limit=20')
      ])
      
      if (indicesRes.data.success && indicesRes.data.data.indices) {
        // 更新指数数据
        const apiIndices = indicesRes.data.data.indices
        indices.value = Object.entries(apiIndices).map(([symbol, data]: [string, any]) => ({
          symbol,
          name: data.name,
          value: data.value,
          change: data.change,
          changePercent: data.changePercent
        }))
      }
      
      if (stocksRes.data.success && stocksRes.data.data) {
        // 更新股票数据
        stocks.value = stocksRes.data.data.map((stock: any) => ({
          symbol: stock.symbol,
          name: stock.name,
          price: stock.price,
          change: stock.change,
          changePercent: stock.changePercent,
          volume: stock.volume
        }))
      }
      
      console.log('✅ 从API成功获取数据')
      
    } catch (apiError) {
      console.log('⚠️ API请求失败，使用模拟数据更新:', apiError)
      
      // API失败时使用模拟数据更新
      simulateDataUpdate()
    }
    
    ElMessage.success('数据已更新')
    
  } catch (error) {
    console.error('数据更新失败:', error)
    ElMessage.error('数据更新失败')
  } finally {
    loading.value = false
  }
}

function simulateDataUpdate() {
  // 模拟指数数据更新
  indices.value.forEach(index => {
    const changeRate = (Math.random() - 0.5) * 0.02 // ±1% 变化
    const newValue = index.value * (1 + changeRate)
    const change = newValue - index.value
    
    index.value = Number(newValue.toFixed(2))
    index.change = Number(change.toFixed(2))
    index.changePercent = Number((change / (index.value - change) * 100).toFixed(2))
  })
  
  // 模拟股票数据更新
  stocks.value.forEach(stock => {
    const changeRate = (Math.random() - 0.5) * 0.04 // ±2% 变化
    const newPrice = stock.price * (1 + changeRate)
    const change = newPrice - stock.price
    
    stock.price = Number(newPrice.toFixed(2))
    stock.change = Number(change.toFixed(2))
    stock.changePercent = Number((change / (stock.price - change) * 100).toFixed(2))
    stock.volume += Math.floor(Math.random() * 100000)
  })
}

function startAutoRefresh() {
  updateTimer = setInterval(() => {
    if (!loading.value) {
      simulateDataUpdate()
    }
  }, 10000) // 10秒更新一次
}

function formatVolume(volume: number): string {
  if (volume >= 100000000) {
    return (volume / 100000000).toFixed(1) + '亿'
  } else if (volume >= 10000) {
    return (volume / 10000).toFixed(1) + '万'
  }
  return volume.toString()
}

function viewDetail(stock: any) {
  router.push(`/market/detail/${stock.symbol}`)
}

function goToTrade(stock: any) {
  router.push(`/trading?symbol=${stock.symbol}`)
}

// 生命周期
onMounted(() => {
  console.log('📊 市场行情页面已加载')
  refreshData()
  startAutoRefresh()
})

onUnmounted(() => {
  if (updateTimer) {
    clearInterval(updateTimer)
  }
})
</script>

<style scoped>
.market-view-simple {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  position: relative;
}

.page-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 20px;
}

.header-actions {
  position: absolute;
  right: 0;
  top: 0;
}

.market-indices {
  margin-bottom: 30px;
}

.index-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.index-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.index-name {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.index-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.index-change {
  font-size: 14px;
  font-weight: 500;
}

.positive {
  color: #f56c6c;
}

.negative {
  color: #67c23a;
}

.stock-section {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.stock-count {
  color: #666;
  font-size: 14px;
}

/* 表格中的颜色 */
:deep(.el-table .positive) {
  color: #f56c6c;
}

:deep(.el-table .negative) {
  color: #67c23a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .market-view-simple {
    padding: 10px;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .header-actions {
    position: static;
    margin-top: 10px;
  }
}
</style>