(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))o(r);new MutationObserver(r=>{for(const i of r)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&o(s)}).observe(document,{childList:!0,subtree:!0});function n(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function o(r){if(r.ep)return;r.ep=!0;const i=n(r);fetch(r.href,i)}})();const w={context:void 0,registry:void 0,effects:void 0,done:!1,getContextId(){return lt(this.context.count)},getNextContextId(){return lt(this.context.count++)}};function lt(e){const t=String(e),n=t.length-1;return w.context.id+(n?String.fromCharCode(96+n):"")+t}function le(e){w.context=e}const xt=!1,Kt=(e,t)=>e===t,me=Symbol("solid-proxy"),wt=typeof Proxy=="function",Jt=Symbol("solid-track"),be={equals:Kt};let vt=At;const J=1,ye=2,St={owned:null,cleanups:null,context:null,owner:null},Ee={};var v=null;let Ae=null,Zt=null,A=null,M=null,K=null,Se=0;function ae(e,t){const n=A,o=v,r=e.length===0,i=t===void 0?o:t,s=r?St:{owned:null,cleanups:null,context:i?i.context:null,owner:i},l=r?e:()=>e(()=>F(()=>ce(s)));v=s,A=null;try{return q(l,!0)}finally{A=n,v=o}}function U(e,t){t=t?Object.assign({},be,t):be;const n={value:e,observers:null,observerSlots:null,comparator:t.equals||void 0},o=r=>(typeof r=="function"&&(r=r(n.value)),Et(n,r));return[Ct.bind(n),o]}function Qt(e,t,n){const o=_e(e,t,!0,J);ie(o)}function N(e,t,n){const o=_e(e,t,!1,J);ie(o)}function en(e,t,n){vt=cn;const o=_e(e,t,!1,J);o.user=!0,K?K.push(o):ie(o)}function $(e,t,n){n=n?Object.assign({},be,n):be;const o=_e(e,t,!0,0);return o.observers=null,o.observerSlots=null,o.comparator=n.equals||void 0,ie(o),Ct.bind(o)}function tn(e){return e&&typeof e=="object"&&"then"in e}function nn(e,t,n){let o,r,i;o=!0,r=e,i={};let s=null,l=Ee,a=null,c=!1,u="initialValue"in i,d=typeof o=="function"&&$(o);const h=new Set,[p,P]=(i.storage||U)(i.initialValue),[g,b]=U(void 0),[x,m]=U(void 0,{equals:!1}),[T,R]=U(u?"ready":"unresolved");w.context&&(a=w.getNextContextId(),i.ssrLoadFrom==="initial"?l=i.initialValue:w.load&&w.has(a)&&(l=w.load(a)));function I(B,L,W,y){return s===B&&(s=null,y!==void 0&&(u=!0),(B===l||L===l)&&i.onHydrated&&queueMicrotask(()=>i.onHydrated(y,{value:L})),l=Ee,X(L,W)),L}function X(B,L){q(()=>{L===void 0&&P(()=>B),R(L!==void 0?"errored":u?"ready":"unresolved"),b(L);for(const W of h.keys())W.decrement();h.clear()},!1)}function H(){const B=sn,L=p(),W=g();if(W!==void 0&&!s)throw W;return A&&A.user,L}function Y(B=!0){if(B!==!1&&c)return;c=!1;const L=d?d():o;if(L==null||L===!1){I(s,F(p));return}let W;const y=l!==Ee?l:F(()=>{try{return r(L,{value:p(),refetching:B})}catch(f){W=f}});if(W!==void 0){I(s,void 0,ge(W),L);return}else if(!tn(y))return I(s,y,void 0,L),y;return s=y,"v"in y?(y.s===1?I(s,y.v,void 0,L):I(s,void 0,ge(y.v),L),y):(c=!0,queueMicrotask(()=>c=!1),q(()=>{R(u?"refreshing":"pending"),m()},!1),y.then(f=>I(y,f,void 0,L),f=>I(y,void 0,ge(f),L)))}Object.defineProperties(H,{state:{get:()=>T()},error:{get:()=>g()},loading:{get(){const B=T();return B==="pending"||B==="refreshing"}},latest:{get(){if(!u)return H();const B=g();if(B&&!s)throw B;return p()}}});let te=v;return d?Qt(()=>(te=v,Y(!1))):Y(!1),[H,{refetch:B=>ze(te,()=>Y(B)),mutate:P}]}function rn(e){return q(e,!1)}function F(e){if(A===null)return e();const t=A;A=null;try{return e()}finally{A=t}}function De(e,t,n){const o=Array.isArray(e);let r,i=n&&n.defer;return s=>{let l;if(o){l=Array(e.length);for(let c=0;c<e.length;c++)l[c]=e[c]()}else l=e();if(i)return i=!1,s;const a=F(()=>t(l,r,s));return r=l,a}}function Qr(e){en(()=>F(e))}function Me(e){return v===null||(v.cleanups===null?v.cleanups=[e]:v.cleanups.push(e)),e}function _t(){return v}function ze(e,t){const n=v,o=A;v=e,A=null;try{return q(t,!0)}catch(r){Ue(r)}finally{v=n,A=o}}function on(e){const t=A,n=v;return Promise.resolve().then(()=>{A=t,v=n;let o;return q(e,!1),A=v=null,o?o.done:void 0})}const[eo,to]=U(!1);function kt(e,t){const n=Symbol("context");return{id:n,Provider:dn(n),defaultValue:e}}function Fe(e){let t;return v&&v.context&&(t=v.context[e.id])!==void 0?t:e.defaultValue}function We(e){const t=$(e),n=$(()=>Le(t()));return n.toArray=()=>{const o=n();return Array.isArray(o)?o:o!=null?[o]:[]},n}let sn;function Ct(){if(this.sources&&this.state)if(this.state===J)ie(this);else{const e=M;M=null,q(()=>we(this),!1),M=e}if(A){const e=this.observers?this.observers.length:0;A.sources?(A.sources.push(this),A.sourceSlots.push(e)):(A.sources=[this],A.sourceSlots=[e]),this.observers?(this.observers.push(A),this.observerSlots.push(A.sources.length-1)):(this.observers=[A],this.observerSlots=[A.sources.length-1])}return this.value}function Et(e,t,n){let o=e.value;return(!e.comparator||!e.comparator(o,t))&&(e.value=t,e.observers&&e.observers.length&&q(()=>{for(let r=0;r<e.observers.length;r+=1){const i=e.observers[r],s=Ae&&Ae.running;s&&Ae.disposed.has(i),(s?!i.tState:!i.state)&&(i.pure?M.push(i):K.push(i),i.observers&&Pt(i)),s||(i.state=J)}if(M.length>1e6)throw M=[],new Error},!1)),t}function ie(e){if(!e.fn)return;ce(e);const t=Se;ln(e,e.value,t)}function ln(e,t,n){let o;const r=v,i=A;A=v=e;try{o=e.fn(t)}catch(s){return e.pure&&(e.state=J,e.owned&&e.owned.forEach(ce),e.owned=null),e.updatedAt=n+1,Ue(s)}finally{A=i,v=r}(!e.updatedAt||e.updatedAt<=n)&&(e.updatedAt!=null&&"observers"in e?Et(e,o):e.value=o,e.updatedAt=n)}function _e(e,t,n,o=J,r){const i={fn:e,state:o,updatedAt:null,owned:null,sources:null,sourceSlots:null,cleanups:null,value:t,owner:v,context:v?v.context:null,pure:n};return v===null||v!==St&&(v.owned?v.owned.push(i):v.owned=[i]),i}function xe(e){if(e.state===0)return;if(e.state===ye)return we(e);if(e.suspense&&F(e.suspense.inFallback))return e.suspense.effects.push(e);const t=[e];for(;(e=e.owner)&&(!e.updatedAt||e.updatedAt<Se);)e.state&&t.push(e);for(let n=t.length-1;n>=0;n--)if(e=t[n],e.state===J)ie(e);else if(e.state===ye){const o=M;M=null,q(()=>we(e,t[0]),!1),M=o}}function q(e,t){if(M)return e();let n=!1;t||(M=[]),K?n=!0:K=[],Se++;try{const o=e();return an(n),o}catch(o){n||(K=null),M=null,Ue(o)}}function an(e){if(M&&(At(M),M=null),e)return;const t=K;K=null,t.length&&q(()=>vt(t),!1)}function At(e){for(let t=0;t<e.length;t++)xe(e[t])}function cn(e){let t,n=0;for(t=0;t<e.length;t++){const o=e[t];o.user?e[n++]=o:xe(o)}if(w.context){if(w.count){w.effects||(w.effects=[]),w.effects.push(...e.slice(0,n));return}le()}for(w.effects&&(w.done||!w.count)&&(e=[...w.effects,...e],n+=w.effects.length,delete w.effects),t=0;t<n;t++)xe(e[t])}function we(e,t){e.state=0;for(let n=0;n<e.sources.length;n+=1){const o=e.sources[n];if(o.sources){const r=o.state;r===J?o!==t&&(!o.updatedAt||o.updatedAt<Se)&&xe(o):r===ye&&we(o,t)}}}function Pt(e){for(let t=0;t<e.observers.length;t+=1){const n=e.observers[t];n.state||(n.state=ye,n.pure?M.push(n):K.push(n),n.observers&&Pt(n))}}function ce(e){let t;if(e.sources)for(;e.sources.length;){const n=e.sources.pop(),o=e.sourceSlots.pop(),r=n.observers;if(r&&r.length){const i=r.pop(),s=n.observerSlots.pop();o<r.length&&(i.sourceSlots[s]=o,r[o]=i,n.observerSlots[o]=s)}}if(e.tOwned){for(t=e.tOwned.length-1;t>=0;t--)ce(e.tOwned[t]);delete e.tOwned}if(e.owned){for(t=e.owned.length-1;t>=0;t--)ce(e.owned[t]);e.owned=null}if(e.cleanups){for(t=e.cleanups.length-1;t>=0;t--)e.cleanups[t]();e.cleanups=null}e.state=0}function ge(e){return e instanceof Error?e:new Error(typeof e=="string"?e:"Unknown error",{cause:e})}function Ue(e,t=v){throw ge(e)}function Le(e){if(typeof e=="function"&&!e.length)return Le(e());if(Array.isArray(e)){const t=[];for(let n=0;n<e.length;n++){const o=Le(e[n]);Array.isArray(o)?t.push.apply(t,o):t.push(o)}return t}return e}function dn(e,t){return function(o){let r;return N(()=>r=F(()=>(v.context={...v.context,[e]:o.value},We(()=>o.children))),void 0),r}}const un=Symbol("fallback");function at(e){for(let t=0;t<e.length;t++)e[t]()}function fn(e,t,n={}){let o=[],r=[],i=[],s=0,l=t.length>1?[]:null;return Me(()=>at(i)),()=>{let a=e()||[],c=a.length,u,d;return a[Jt],F(()=>{let p,P,g,b,x,m,T,R,I;if(c===0)s!==0&&(at(i),i=[],o=[],r=[],s=0,l&&(l=[])),n.fallback&&(o=[un],r[0]=ae(X=>(i[0]=X,n.fallback())),s=1);else if(s===0){for(r=new Array(c),d=0;d<c;d++)o[d]=a[d],r[d]=ae(h);s=c}else{for(g=new Array(c),b=new Array(c),l&&(x=new Array(c)),m=0,T=Math.min(s,c);m<T&&o[m]===a[m];m++);for(T=s-1,R=c-1;T>=m&&R>=m&&o[T]===a[R];T--,R--)g[R]=r[T],b[R]=i[T],l&&(x[R]=l[T]);for(p=new Map,P=new Array(R+1),d=R;d>=m;d--)I=a[d],u=p.get(I),P[d]=u===void 0?-1:u,p.set(I,d);for(u=m;u<=T;u++)I=o[u],d=p.get(I),d!==void 0&&d!==-1?(g[d]=r[u],b[d]=i[u],l&&(x[d]=l[u]),d=P[d],p.set(I,d)):i[u]();for(d=m;d<c;d++)d in g?(r[d]=g[d],i[d]=b[d],l&&(l[d]=x[d],l[d](d))):r[d]=ae(h);r=r.slice(0,s=c),o=a.slice(0)}return r});function h(p){if(i[d]=p,l){const[P,g]=U(d);return l[d]=g,t(a[d],P)}return t(a[d])}}}function O(e,t){return F(()=>e(t||{}))}function fe(){return!0}const Te={get(e,t,n){return t===me?n:e.get(t)},has(e,t){return t===me?!0:e.has(t)},set:fe,deleteProperty:fe,getOwnPropertyDescriptor(e,t){return{configurable:!0,enumerable:!0,get(){return e.get(t)},set:fe,deleteProperty:fe}},ownKeys(e){return e.keys()}};function Pe(e){return(e=typeof e=="function"?e():e)?e:{}}function hn(){for(let e=0,t=this.length;e<t;++e){const n=this[e]();if(n!==void 0)return n}}function $e(...e){let t=!1;for(let s=0;s<e.length;s++){const l=e[s];t=t||!!l&&me in l,e[s]=typeof l=="function"?(t=!0,$(l)):l}if(wt&&t)return new Proxy({get(s){for(let l=e.length-1;l>=0;l--){const a=Pe(e[l])[s];if(a!==void 0)return a}},has(s){for(let l=e.length-1;l>=0;l--)if(s in Pe(e[l]))return!0;return!1},keys(){const s=[];for(let l=0;l<e.length;l++)s.push(...Object.keys(Pe(e[l])));return[...new Set(s)]}},Te);const n={},o=Object.create(null);for(let s=e.length-1;s>=0;s--){const l=e[s];if(!l)continue;const a=Object.getOwnPropertyNames(l);for(let c=a.length-1;c>=0;c--){const u=a[c];if(u==="__proto__"||u==="constructor")continue;const d=Object.getOwnPropertyDescriptor(l,u);if(!o[u])o[u]=d.get?{enumerable:!0,configurable:!0,get:hn.bind(n[u]=[d.get.bind(l)])}:d.value!==void 0?d:void 0;else{const h=n[u];h&&(d.get?h.push(d.get.bind(l)):d.value!==void 0&&h.push(()=>d.value))}}}const r={},i=Object.keys(o);for(let s=i.length-1;s>=0;s--){const l=i[s],a=o[l];a&&a.get?Object.defineProperty(r,l,a):r[l]=a?a.value:void 0}return r}function gn(e,...t){if(wt&&me in e){const r=new Set(t.length>1?t.flat():t[0]),i=t.map(s=>new Proxy({get(l){return s.includes(l)?e[l]:void 0},has(l){return s.includes(l)&&l in e},keys(){return s.filter(l=>l in e)}},Te));return i.push(new Proxy({get(s){return r.has(s)?void 0:e[s]},has(s){return r.has(s)?!1:s in e},keys(){return Object.keys(e).filter(s=>!r.has(s))}},Te)),i}const n={},o=t.map(()=>({}));for(const r of Object.getOwnPropertyNames(e)){const i=Object.getOwnPropertyDescriptor(e,r),s=!i.get&&!i.set&&i.enumerable&&i.writable&&i.configurable;let l=!1,a=0;for(const c of t)c.includes(r)&&(l=!0,s?o[a][r]=i.value:Object.defineProperty(o[a],r,i)),++a;l||(s?n[r]=i.value:Object.defineProperty(n,r,i))}return[...o,n]}function ke(e){let t,n;const o=r=>{const i=w.context;if(i){const[l,a]=U();w.count||(w.count=0),w.count++,(n||(n=e())).then(c=>{!w.done&&le(i),w.count--,a(()=>c.default),le()}),t=l}else if(!t){const[l]=nn(()=>(n||(n=e())).then(a=>a.default));t=l}let s;return $(()=>(s=t())?F(()=>{if(!i||w.done)return s(r);const l=w.context;le(i);const a=s(r);return le(l),a}):"")};return o.preload=()=>n||((n=e()).then(r=>t=()=>r.default),n),o}const pn=e=>`Stale read from <${e}>.`;function Re(e){const t="fallback"in e&&{fallback:()=>e.fallback};return $(fn(()=>e.each,e.children,t||void 0))}function ee(e){const t=e.keyed,n=$(()=>e.when,void 0,void 0),o=t?n:$(n,void 0,{equals:(r,i)=>!r==!i});return $(()=>{const r=o();if(r){const i=e.children;return typeof i=="function"&&i.length>0?F(()=>i(t?r:()=>{if(!F(o))throw pn("Show");return n()})):i}return e.fallback},void 0,void 0)}const mn=["allowfullscreen","async","alpha","autofocus","autoplay","checked","controls","default","disabled","formnovalidate","hidden","indeterminate","inert","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","seamless","selected","adauctionheaders","browsingtopics","credentialless","defaultchecked","defaultmuted","defaultselected","defer","disablepictureinpicture","disableremoteplayback","preservespitch","shadowrootclonable","shadowrootcustomelementregistry","shadowrootdelegatesfocus","shadowrootserializable","sharedstoragewritable"],bn=new Set(["className","value","readOnly","noValidate","formNoValidate","isMap","noModule","playsInline","adAuctionHeaders","allowFullscreen","browsingTopics","defaultChecked","defaultMuted","defaultSelected","disablePictureInPicture","disableRemotePlayback","preservesPitch","shadowRootClonable","shadowRootCustomElementRegistry","shadowRootDelegatesFocus","shadowRootSerializable","sharedStorageWritable",...mn]),yn=new Set(["innerHTML","textContent","innerText","children"]),xn=Object.assign(Object.create(null),{className:"class",htmlFor:"for"}),wn=Object.assign(Object.create(null),{class:"className",novalidate:{$:"noValidate",FORM:1},formnovalidate:{$:"formNoValidate",BUTTON:1,INPUT:1},ismap:{$:"isMap",IMG:1},nomodule:{$:"noModule",SCRIPT:1},playsinline:{$:"playsInline",VIDEO:1},readonly:{$:"readOnly",INPUT:1,TEXTAREA:1},adauctionheaders:{$:"adAuctionHeaders",IFRAME:1},allowfullscreen:{$:"allowFullscreen",IFRAME:1},browsingtopics:{$:"browsingTopics",IMG:1},defaultchecked:{$:"defaultChecked",INPUT:1},defaultmuted:{$:"defaultMuted",AUDIO:1,VIDEO:1},defaultselected:{$:"defaultSelected",OPTION:1},disablepictureinpicture:{$:"disablePictureInPicture",VIDEO:1},disableremoteplayback:{$:"disableRemotePlayback",AUDIO:1,VIDEO:1},preservespitch:{$:"preservesPitch",AUDIO:1,VIDEO:1},shadowrootclonable:{$:"shadowRootClonable",TEMPLATE:1},shadowrootdelegatesfocus:{$:"shadowRootDelegatesFocus",TEMPLATE:1},shadowrootserializable:{$:"shadowRootSerializable",TEMPLATE:1},sharedstoragewritable:{$:"sharedStorageWritable",IFRAME:1,IMG:1}});function vn(e,t){const n=wn[e];return typeof n=="object"?n[t]?n.$:void 0:n}const Sn=new Set(["beforeinput","click","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"]),Rt=e=>$(()=>e());function _n(e,t,n){let o=n.length,r=t.length,i=o,s=0,l=0,a=t[r-1].nextSibling,c=null;for(;s<r||l<i;){if(t[s]===n[l]){s++,l++;continue}for(;t[r-1]===n[i-1];)r--,i--;if(r===s){const u=i<o?l?n[l-1].nextSibling:n[i-l]:a;for(;l<i;)e.insertBefore(n[l++],u)}else if(i===l)for(;s<r;)(!c||!c.has(t[s]))&&t[s].remove(),s++;else if(t[s]===n[i-1]&&n[l]===t[r-1]){const u=t[--r].nextSibling;e.insertBefore(n[l++],t[s++].nextSibling),e.insertBefore(n[--i],u),t[r]=n[i]}else{if(!c){c=new Map;let d=l;for(;d<i;)c.set(n[d],d++)}const u=c.get(t[s]);if(u!=null)if(l<u&&u<i){let d=s,h=1,p;for(;++d<r&&d<i&&!((p=c.get(t[d]))==null||p!==u+h);)h++;if(h>u-l){const P=t[s];for(;l<u;)e.insertBefore(n[l++],P)}else e.replaceChild(n[l++],t[s++])}else s++;else t[s++].remove()}}}const ct="_$DX_DELEGATE";function kn(e,t,n,o={}){let r;return ae(i=>{r=i,t===document?e():z(t,e(),t.firstChild?null:void 0,n)},o.owner),()=>{r(),t.textContent=""}}function Z(e,t,n,o){let r;const i=()=>{const l=document.createElement("template");return l.innerHTML=e,l.content.firstChild},s=()=>(r||(r=i())).cloneNode(!0);return s.cloneNode=s,s}function Ve(e,t=window.document){const n=t[ct]||(t[ct]=new Set);for(let o=0,r=e.length;o<r;o++){const i=e[o];n.has(i)||(n.add(i),t.addEventListener(i,Tn))}}function Be(e,t,n){ue(e)||(n==null?e.removeAttribute(t):e.setAttribute(t,n))}function Cn(e,t,n){ue(e)||(n?e.setAttribute(t,""):e.removeAttribute(t))}function _(e,t){ue(e)||(t==null?e.removeAttribute("class"):e.className=t)}function En(e,t,n,o){if(o)Array.isArray(n)?(e[`$$${t}`]=n[0],e[`$$${t}Data`]=n[1]):e[`$$${t}`]=n;else if(Array.isArray(n)){const r=n[0];e.addEventListener(t,n[0]=i=>r.call(e,n[1],i))}else e.addEventListener(t,n,typeof n!="function"&&n)}function An(e,t,n={}){const o=Object.keys(t||{}),r=Object.keys(n);let i,s;for(i=0,s=r.length;i<s;i++){const l=r[i];!l||l==="undefined"||t[l]||(dt(e,l,!1),delete n[l])}for(i=0,s=o.length;i<s;i++){const l=o[i],a=!!t[l];!l||l==="undefined"||n[l]===a||!a||(dt(e,l,!0),n[l]=a)}return n}function Pn(e,t,n){if(!t)return n?Be(e,"style"):t;const o=e.style;if(typeof t=="string")return o.cssText=t;typeof n=="string"&&(o.cssText=n=void 0),n||(n={}),t||(t={});let r,i;for(i in n)t[i]==null&&o.removeProperty(i),delete n[i];for(i in t)r=t[i],r!==n[i]&&(o.setProperty(i,r),n[i]=r);return n}function E(e,t,n){n!=null?e.style.setProperty(t,n):e.style.removeProperty(t)}function Rn(e,t={},n,o){const r={};return N(()=>r.children=de(e,t.children,r.children)),N(()=>typeof t.ref=="function"&&On(t.ref,e)),N(()=>In(e,t,n,!0,r,!0)),r}function On(e,t,n){return F(()=>e(t,n))}function z(e,t,n,o){if(n!==void 0&&!o&&(o=[]),typeof t!="function")return de(e,t,o,n);N(r=>de(e,t(),r,n),o)}function In(e,t,n,o,r={},i=!1){t||(t={});for(const s in r)if(!(s in t)){if(s==="children")continue;r[s]=ut(e,s,null,r[s],n,i,t)}for(const s in t){if(s==="children")continue;const l=t[s];r[s]=ut(e,s,l,r[s],n,i,t)}}function ue(e){return!!w.context&&!w.done&&(!e||e.isConnected)}function Ln(e){return e.toLowerCase().replace(/-([a-z])/g,(t,n)=>n.toUpperCase())}function dt(e,t,n){const o=t.trim().split(/\s+/);for(let r=0,i=o.length;r<i;r++)e.classList.toggle(o[r],n)}function ut(e,t,n,o,r,i,s){let l,a,c,u,d;if(t==="style")return Pn(e,n,o);if(t==="classList")return An(e,n,o);if(n===o)return o;if(t==="ref")i||n(e);else if(t.slice(0,3)==="on:"){const h=t.slice(3);o&&e.removeEventListener(h,o,typeof o!="function"&&o),n&&e.addEventListener(h,n,typeof n!="function"&&n)}else if(t.slice(0,10)==="oncapture:"){const h=t.slice(10);o&&e.removeEventListener(h,o,!0),n&&e.addEventListener(h,n,!0)}else if(t.slice(0,2)==="on"){const h=t.slice(2).toLowerCase(),p=Sn.has(h);if(!p&&o){const P=Array.isArray(o)?o[0]:o;e.removeEventListener(h,P)}(p||n)&&(En(e,h,n,p),p&&Ve([h]))}else if(t.slice(0,5)==="attr:")Be(e,t.slice(5),n);else if(t.slice(0,5)==="bool:")Cn(e,t.slice(5),n);else if((d=t.slice(0,5)==="prop:")||(c=yn.has(t))||(u=vn(t,e.tagName))||(a=bn.has(t))||(l=e.nodeName.includes("-")||"is"in s)){if(d)t=t.slice(5),a=!0;else if(ue(e))return n;t==="class"||t==="className"?_(e,n):l&&!a&&!c?e[Ln(t)]=n:e[u||t]=n}else Be(e,xn[t]||t,n);return n}function Tn(e){if(w.registry&&w.events&&w.events.find(([a,c])=>c===e))return;let t=e.target;const n=`$$${e.type}`,o=e.target,r=e.currentTarget,i=a=>Object.defineProperty(e,"target",{configurable:!0,value:a}),s=()=>{const a=t[n];if(a&&!t.disabled){const c=t[`${n}Data`];if(c!==void 0?a.call(t,c,e):a.call(t,e),e.cancelBubble)return}return t.host&&typeof t.host!="string"&&!t.host._$host&&t.contains(e.target)&&i(t.host),!0},l=()=>{for(;s()&&(t=t._$host||t.parentNode||t.host););};if(Object.defineProperty(e,"currentTarget",{configurable:!0,get(){return t||document}}),w.registry&&!w.done&&(w.done=_$HY.done=!0),e.composedPath){const a=e.composedPath();i(a[0]);for(let c=0;c<a.length-2&&(t=a[c],!!s());c++){if(t._$host){t=t._$host,l();break}if(t.parentNode===r)break}}else l();i(o)}function de(e,t,n,o,r){const i=ue(e);if(i){!n&&(n=[...e.childNodes]);let a=[];for(let c=0;c<n.length;c++){const u=n[c];u.nodeType===8&&u.data.slice(0,2)==="!$"?u.remove():a.push(u)}n=a}for(;typeof n=="function";)n=n();if(t===n)return n;const s=typeof t,l=o!==void 0;if(e=l&&n[0]&&n[0].parentNode||e,s==="string"||s==="number"){if(i||s==="number"&&(t=t.toString(),t===n))return n;if(l){let a=n[0];a&&a.nodeType===3?a.data!==t&&(a.data=t):a=document.createTextNode(t),n=oe(e,n,o,a)}else n!==""&&typeof n=="string"?n=e.firstChild.data=t:n=e.textContent=t}else if(t==null||s==="boolean"){if(i)return n;n=oe(e,n,o)}else{if(s==="function")return N(()=>{let a=t();for(;typeof a=="function";)a=a();n=de(e,a,n,o)}),()=>n;if(Array.isArray(t)){const a=[],c=n&&Array.isArray(n);if(je(a,t,n,r))return N(()=>n=de(e,a,n,o,!0)),()=>n;if(i){if(!a.length)return n;if(o===void 0)return n=[...e.childNodes];let u=a[0];if(u.parentNode!==e)return n;const d=[u];for(;(u=u.nextSibling)!==o;)d.push(u);return n=d}if(a.length===0){if(n=oe(e,n,o),l)return n}else c?n.length===0?ft(e,a,o):_n(e,n,a):(n&&oe(e),ft(e,a));n=a}else if(t.nodeType){if(i&&t.parentNode)return n=l?[t]:t;if(Array.isArray(n)){if(l)return n=oe(e,n,o,t);oe(e,n,null,t)}else n==null||n===""||!e.firstChild?e.appendChild(t):e.replaceChild(t,e.firstChild);n=t}}return n}function je(e,t,n,o){let r=!1;for(let i=0,s=t.length;i<s;i++){let l=t[i],a=n&&n[e.length],c;if(!(l==null||l===!0||l===!1))if((c=typeof l)=="object"&&l.nodeType)e.push(l);else if(Array.isArray(l))r=je(e,l,a)||r;else if(c==="function")if(o){for(;typeof l=="function";)l=l();r=je(e,Array.isArray(l)?l:[l],Array.isArray(a)?a:[a])||r}else e.push(l),r=!0;else{const u=String(l);a&&a.nodeType===3&&a.data===u?e.push(a):e.push(document.createTextNode(u))}}return r}function ft(e,t,n=null){for(let o=0,r=t.length;o<r;o++)e.insertBefore(t[o],n)}function oe(e,t,n,o){if(n===void 0)return e.textContent="";const r=o||document.createTextNode("");if(t.length){let i=!1;for(let s=t.length-1;s>=0;s--){const l=t[s];if(r!==l){const a=l.parentNode===e;!i&&!s?a?e.replaceChild(r,l):e.insertBefore(r,n):a&&l.remove()}else i=!0}}else e.insertBefore(r,n);return[r]}const $n=!1,Bn="modulepreload",jn=function(e){return"/"+e},ht={},Ce=function(t,n,o){let r=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),l=s?.nonce||s?.getAttribute("nonce");r=Promise.allSettled(n.map(a=>{if(a=jn(a),a in ht)return;ht[a]=!0;const c=a.endsWith(".css"),u=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${a}"]${u}`))return;const d=document.createElement("link");if(d.rel=c?"stylesheet":Bn,c||(d.as="script"),d.crossOrigin="",d.href=a,l&&d.setAttribute("nonce",l),document.head.appendChild(d),c)return new Promise((h,p)=>{d.addEventListener("load",h),d.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${a}`)))})}))}function i(s){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=s,window.dispatchEvent(l),!l.defaultPrevented)throw s}return r.then(s=>{for(const l of s||[])l.status==="rejected"&&i(l.reason);return t().catch(i)})};function Ot(){let e=new Set;function t(r){return e.add(r),()=>e.delete(r)}let n=!1;function o(r,i){if(n)return!(n=!1);const s={to:r,options:i,defaultPrevented:!1,preventDefault:()=>s.defaultPrevented=!0};for(const l of e)l.listener({...s,from:l.location,retry:a=>{a&&(n=!0),l.navigate(r,{...i,resolve:!1})}});return!s.defaultPrevented}return{subscribe:t,confirm:o}}let Ne;function Xe(){(!window.history.state||window.history.state._depth==null)&&window.history.replaceState({...window.history.state,_depth:window.history.length-1},""),Ne=window.history.state._depth}Xe();function Nn(e){return{...e,_depth:window.history.state&&window.history.state._depth}}function Dn(e,t){let n=!1;return()=>{const o=Ne;Xe();const r=o==null?null:Ne-o;if(n){n=!1;return}r&&t(r)?(n=!0,window.history.go(-r)):e()}}const Mn=/^(?:[a-z0-9]+:)?\/\//i,zn=/^\/+|(\/)\/+$/g,It="http://sr";function re(e,t=!1){const n=e.replace(zn,"$1");return n?t||/^[?#]/.test(n)?n:"/"+n:""}function pe(e,t,n){if(Mn.test(t))return;const o=re(e),r=n&&re(n);let i="";return!r||t.startsWith("/")?i=o:r.toLowerCase().indexOf(o.toLowerCase())!==0?i=o+r:i=r,(i||"/")+re(t,!i)}function Fn(e,t){if(e==null)throw new Error(t);return e}function Wn(e,t){return re(e).replace(/\/*(\*.*)?$/g,"")+re(t)}function Lt(e){const t={};return e.searchParams.forEach((n,o)=>{t[o]=n}),t}function Un(e,t,n){const[o,r]=e.split("/*",2),i=o.split("/").filter(Boolean),s=i.length;return l=>{const a=l.split("/").filter(Boolean),c=a.length-s;if(c<0||c>0&&r===void 0&&!t)return null;const u={path:s?"":"/",params:{}},d=h=>n===void 0?void 0:n[h];for(let h=0;h<s;h++){const p=i[h],P=a[h],g=p[0]===":",b=g?p.slice(1):p;if(g&&Oe(P,d(b)))u.params[b]=P;else if(g||!Oe(P,p))return null;u.path+=`/${P}`}if(r){const h=c?a.slice(-c).join("/"):"";if(Oe(h,d(r)))u.params[r]=h;else return null}return u}}function Oe(e,t){const n=o=>o.localeCompare(e,void 0,{sensitivity:"base"})===0;return t===void 0?!0:typeof t=="string"?n(t):typeof t=="function"?t(e):Array.isArray(t)?t.some(n):t instanceof RegExp?t.test(e):!1}function Vn(e){const[t,n]=e.pattern.split("/*",2),o=t.split("/").filter(Boolean);return o.reduce((r,i)=>r+(i.startsWith(":")?2:3),o.length-(n===void 0?0:1))}function Tt(e){const t=new Map,n=_t();return new Proxy({},{get(o,r){return t.has(r)||ze(n,()=>t.set(r,$(()=>e()[r]))),t.get(r)()},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}},ownKeys(){return Reflect.ownKeys(e())}})}function $t(e){let t=/(\/?\:[^\/]+)\?/.exec(e);if(!t)return[e];let n=e.slice(0,t.index),o=e.slice(t.index+t[0].length);const r=[n,n+=t[1]];for(;t=/^(\/\:[^\/]+)\?/.exec(o);)r.push(n+=t[1]),o=o.slice(t[0].length);return $t(o).reduce((i,s)=>[...i,...r.map(l=>l+s)],[])}const Xn=100,Bt=kt(),qe=kt(),He=()=>Fn(Fe(Bt),"<A> and 'use' router primitives can be only used inside a Route."),qn=()=>Fe(qe)||He().base,Hn=e=>{const t=qn();return $(()=>t.resolvePath(e()))},Yn=e=>{const t=He();return $(()=>{const n=e();return n!==void 0?t.renderPath(n):n})},jt=()=>He().location;function Gn(e,t=""){const{component:n,load:o,children:r,info:i}=e,s=!r||Array.isArray(r)&&!r.length,l={key:e,component:n,load:o,info:i};return Nt(e.path).reduce((a,c)=>{for(const u of $t(c)){const d=Wn(t,u);let h=s?d:d.split("/*",1)[0];h=h.split("/").map(p=>p.startsWith(":")||p.startsWith("*")?p:encodeURIComponent(p)).join("/"),a.push({...l,originalPath:c,pattern:h,matcher:Un(h,!s,e.matchFilters)})}return a},[])}function Kn(e,t=0){return{routes:e,score:Vn(e[e.length-1])*1e4-t,matcher(n){const o=[];for(let r=e.length-1;r>=0;r--){const i=e[r],s=i.matcher(n);if(!s)return null;o.unshift({...s,route:i})}return o}}}function Nt(e){return Array.isArray(e)?e:[e]}function Dt(e,t="",n=[],o=[]){const r=Nt(e);for(let i=0,s=r.length;i<s;i++){const l=r[i];if(l&&typeof l=="object"){l.hasOwnProperty("path")||(l.path="");const a=Gn(l,t);for(const c of a){n.push(c);const u=Array.isArray(l.children)&&l.children.length===0;if(l.children&&!u)Dt(l.children,c.pattern,n,o);else{const d=Kn([...n],o.length);o.push(d)}n.pop()}}}return n.length?o:o.sort((i,s)=>s.score-i.score)}function Ie(e,t){for(let n=0,o=e.length;n<o;n++){const r=e[n].matcher(t);if(r)return r}return[]}function Jn(e,t){const n=new URL(It),o=$(a=>{const c=e();try{return new URL(c,n)}catch{return console.error(`Invalid path ${c}`),a}},n,{equals:(a,c)=>a.href===c.href}),r=$(()=>o().pathname),i=$(()=>o().search,!0),s=$(()=>o().hash),l=()=>"";return{get pathname(){return r()},get search(){return i()},get hash(){return s()},get state(){return t()},get key(){return l()},query:Tt(De(i,()=>Lt(o())))}}let ne;function Zn(){return ne}function Qn(e,t,n,o={}){const{signal:[r,i],utils:s={}}=e,l=s.parsePath||(y=>y),a=s.renderPath||(y=>y),c=s.beforeLeave||Ot(),u=pe("",o.base||"");if(u===void 0)throw new Error(`${u} is not a valid base path`);u&&!r().value&&i({value:u,replace:!0,scroll:!1});const[d,h]=U(!1);let p;const P=(y,f)=>{f.value===g()&&f.state===x()||(p===void 0&&h(!0),ne=y,p=f,on(()=>{p===f&&(b(p.value),m(p.state),I[1]([]))}).finally(()=>{p===f&&rn(()=>{ne=void 0,y==="navigate"&&L(p),h(!1),p=void 0})}))},[g,b]=U(r().value),[x,m]=U(r().state),T=Jn(g,x),R=[],I=U([]),X=$(()=>typeof o.transformUrl=="function"?Ie(t(),o.transformUrl(T.pathname)):Ie(t(),T.pathname)),H=Tt(()=>{const y=X(),f={};for(let k=0;k<y.length;k++)Object.assign(f,y[k].params);return f}),Y={pattern:u,path:()=>u,outlet:()=>null,resolvePath(y){return pe(u,y)}};return N(De(r,y=>P("native",y),{defer:!0})),{base:Y,location:T,params:H,isRouting:d,renderPath:a,parsePath:l,navigatorFactory:B,matches:X,beforeLeave:c,preloadRoute:W,singleFlight:o.singleFlight===void 0?!0:o.singleFlight,submissions:I};function te(y,f,k){F(()=>{if(typeof f=="number"){f&&(s.go?s.go(f):console.warn("Router integration does not support relative routing"));return}const{replace:j,resolve:G,scroll:C,state:D}={replace:!1,resolve:!0,scroll:!0,...k},V=G?y.resolvePath(f):pe("",f);if(V===void 0)throw new Error(`Path '${f}' is not a routable path`);if(R.length>=Xn)throw new Error("Too many redirects");const Q=g();(V!==Q||D!==x())&&($n||c.confirm(V,k)&&(R.push({value:Q,replace:j,scroll:C,state:x()}),P("navigate",{value:V,state:D})))})}function B(y){return y=y||Fe(qe)||Y,(f,k)=>te(y,f,k)}function L(y){const f=R[0];f&&(i({...y,replace:f.replace,scroll:f.scroll}),R.length=0)}function W(y,f={}){const k=Ie(t(),y.pathname),j=ne;ne="preload";for(let G in k){const{route:C,params:D}=k[G];C.component&&C.component.preload&&C.component.preload();const{load:V}=C;f.preloadData&&V&&ze(n(),()=>V({params:D,location:{pathname:y.pathname,search:y.search,hash:y.hash,query:Lt(y),state:null,key:""},intent:"preload"}))}ne=j}}function er(e,t,n,o){const{base:r,location:i,params:s}=e,{pattern:l,component:a,load:c}=o().route,u=$(()=>o().path);a&&a.preload&&a.preload();const d=c?c({params:s,location:i,intent:ne||"initial"}):void 0;return{parent:t,pattern:l,path:u,outlet:()=>a?O(a,{params:s,location:i,data:d,get children(){return n()}}):n(),resolvePath(p){return pe(r.path(),p,u())}}}const tr=e=>t=>{const{base:n}=t,o=We(()=>t.children),r=$(()=>Dt(o(),t.base||""));let i;const s=Qn(e,r,()=>i,{base:n,singleFlight:t.singleFlight,transformUrl:t.transformUrl});return e.create&&e.create(s),O(Bt.Provider,{value:s,get children(){return O(nr,{routerState:s,get root(){return t.root},get load(){return t.rootLoad},get children(){return[Rt(()=>(i=_t())&&null),O(rr,{routerState:s,get branches(){return r()}})]}})}})};function nr(e){const t=e.routerState.location,n=e.routerState.params,o=$(()=>e.load&&F(()=>{e.load({params:n,location:t,intent:Zn()||"initial"})}));return O(ee,{get when(){return e.root},keyed:!0,get fallback(){return e.children},children:r=>O(r,{params:n,location:t,get data(){return o()},get children(){return e.children}})})}function rr(e){const t=[];let n;const o=$(De(e.routerState.matches,(r,i,s)=>{let l=i&&r.length===i.length;const a=[];for(let c=0,u=r.length;c<u;c++){const d=i&&i[c],h=r[c];s&&d&&h.route.key===d.route.key?a[c]=s[c]:(l=!1,t[c]&&t[c](),ae(p=>{t[c]=p,a[c]=er(e.routerState,a[c-1]||e.routerState.base,gt(()=>o()[c+1]),()=>e.routerState.matches()[c])}))}return t.splice(r.length).forEach(c=>c()),s&&l?s:(n=a[0],a)}));return gt(()=>o()&&n)()}const gt=e=>()=>O(ee,{get when(){return e()},keyed:!0,children:t=>O(qe.Provider,{value:t,get children(){return t.outlet()}})}),se=e=>{const t=We(()=>e.children);return $e(e,{get children(){return t()}})};function or([e,t],n,o){return[e,o?r=>t(o(r)):t]}function ir(e){if(e==="#")return null;try{return document.querySelector(e)}catch{return null}}function sr(e){let t=!1;const n=r=>typeof r=="string"?{value:r}:r,o=or(U(n(e.get()),{equals:(r,i)=>r.value===i.value&&r.state===i.state}),void 0,r=>(!t&&e.set(r),r));return e.init&&Me(e.init((r=e.get())=>{t=!0,o[1](n(r)),t=!1})),tr({signal:o,create:e.create,utils:e.utils})}function lr(e,t,n){return e.addEventListener(t,n),()=>e.removeEventListener(t,n)}function ar(e,t){const n=ir(`#${e}`);n?n.scrollIntoView():t&&window.scrollTo(0,0)}const cr=new Map;function dr(e=!0,t=!1,n="/_server",o){return r=>{const i=r.base.path(),s=r.navigatorFactory(r.base);let l={};function a(g){return g.namespaceURI==="http://www.w3.org/2000/svg"}function c(g){if(g.defaultPrevented||g.button!==0||g.metaKey||g.altKey||g.ctrlKey||g.shiftKey)return;const b=g.composedPath().find(X=>X instanceof Node&&X.nodeName.toUpperCase()==="A");if(!b||t&&!b.hasAttribute("link"))return;const x=a(b),m=x?b.href.baseVal:b.href;if((x?b.target.baseVal:b.target)||!m&&!b.hasAttribute("state"))return;const R=(b.getAttribute("rel")||"").split(/\s+/);if(b.hasAttribute("download")||R&&R.includes("external"))return;const I=x?new URL(m,document.baseURI):new URL(m);if(!(I.origin!==window.location.origin||i&&I.pathname&&!I.pathname.toLowerCase().startsWith(i.toLowerCase())))return[b,I]}function u(g){const b=c(g);if(!b)return;const[x,m]=b,T=r.parsePath(m.pathname+m.search+m.hash),R=x.getAttribute("state");g.preventDefault(),s(T,{resolve:!1,replace:x.hasAttribute("replace"),scroll:!x.hasAttribute("noscroll"),state:R&&JSON.parse(R)})}function d(g){const b=c(g);if(!b)return;const[x,m]=b;typeof o=="function"&&(m.pathname=o(m.pathname)),l[m.pathname]||r.preloadRoute(m,{preloadData:x.getAttribute("preload")!=="false"})}function h(g){const b=c(g);if(!b)return;const[x,m]=b;typeof o=="function"&&(m.pathname=o(m.pathname)),!l[m.pathname]&&(l[m.pathname]=setTimeout(()=>{r.preloadRoute(m,{preloadData:x.getAttribute("preload")!=="false"}),delete l[m.pathname]},200))}function p(g){const b=c(g);if(!b)return;const[,x]=b;typeof o=="function"&&(x.pathname=o(x.pathname)),l[x.pathname]&&(clearTimeout(l[x.pathname]),delete l[x.pathname])}function P(g){if(g.defaultPrevented)return;let b=g.submitter&&g.submitter.hasAttribute("formaction")?g.submitter.getAttribute("formaction"):g.target.getAttribute("action");if(!b)return;if(!b.startsWith("https://action/")){const m=new URL(b,It);if(b=r.parsePath(m.pathname+m.search),!b.startsWith(n))return}if(g.target.method.toUpperCase()!=="POST")throw new Error("Only POST forms are supported for Actions");const x=cr.get(b);if(x){g.preventDefault();const m=new FormData(g.target);g.submitter&&g.submitter.name&&m.append(g.submitter.name,g.submitter.value),x.call({r,f:g.target},m)}}Ve(["click","submit"]),document.addEventListener("click",u),e&&(document.addEventListener("mouseover",h),document.addEventListener("mouseout",p),document.addEventListener("focusin",d),document.addEventListener("touchstart",d)),document.addEventListener("submit",P),Me(()=>{document.removeEventListener("click",u),e&&(document.removeEventListener("mouseover",h),document.removeEventListener("mouseout",p),document.removeEventListener("focusin",d),document.removeEventListener("touchstart",d)),document.removeEventListener("submit",P)})}}function ur(e){const t=()=>{const o=window.location.pathname+window.location.search;return{value:e.transformUrl?e.transformUrl(o)+window.location.hash:o+window.location.hash,state:window.history.state}},n=Ot();return sr({get:t,set({value:o,replace:r,scroll:i,state:s}){r?window.history.replaceState(Nn(s),"",o):window.history.pushState(s,"",o),ar(decodeURIComponent(window.location.hash.slice(1)),i),Xe()},init:o=>lr(window,"popstate",Dn(o,r=>{if(r&&r<0)return!n.confirm(r);{const i=t();return!n.confirm(i.value,{state:i.state})}})),create:dr(e.preload,e.explicitLinks,e.actionBase,e.transformUrl),utils:{go:o=>window.history.go(o),beforeLeave:n}})(e)}var fr=Z("<a>");function pt(e){e=$e({inactiveClass:"inactive",activeClass:"active"},e);const[,t]=gn(e,["href","state","class","activeClass","inactiveClass","end"]),n=Hn(()=>e.href),o=Yn(n),r=jt(),i=$(()=>{const s=n();if(s===void 0)return[!1,!1];const l=re(s.split(/[?#]/,1)[0]).toLowerCase(),a=re(r.pathname).toLowerCase();return[e.end?l===a:a.startsWith(l+"/")||a===l,l===a]});return(()=>{var s=fr();return Rn(s,$e(t,{get href(){return o()||e.href},get state(){return JSON.stringify(e.state)},get classList(){return{...e.class&&{[e.class]:!0},[e.inactiveClass]:!i()[0],[e.activeClass]:i()[0],...t.classList}},link:"",get"aria-current"(){return i()[1]?"page":void 0}}),!1),s})()}function ve(e){return typeof e=="object"&&e!=null&&!Array.isArray(e)}function hr(e){return Object.fromEntries(Object.entries(e??{}).filter(([t,n])=>n!==void 0))}var gr=e=>e==="base";function pr(e){return e.slice().filter(t=>!gr(t))}function mt(e){return String.fromCharCode(e+(e>25?39:97))}function mr(e){let t="",n;for(n=Math.abs(e);n>52;n=n/52|0)t=mt(n%52)+t;return mt(n%52)+t}function br(e,t){let n=t.length;for(;n;)e=e*33^t.charCodeAt(--n);return e}function yr(e){return mr(br(5381,e)>>>0)}var Mt=/\s*!(important)?/i;function xr(e){return typeof e=="string"?Mt.test(e):!1}function wr(e){return typeof e=="string"?e.replace(Mt,"").trim():e}function zt(e){return typeof e=="string"?e.replaceAll(" ","_"):e}var Ye=e=>{const t=new Map;return(...o)=>{const r=JSON.stringify(o);if(t.has(r))return t.get(r);const i=e(...o);return t.set(r,i),i}};function Ft(...e){return e.filter(Boolean).reduce((n,o)=>(Object.keys(o).forEach(r=>{const i=n[r],s=o[r];ve(i)&&ve(s)?n[r]=Ft(i,s):n[r]=s}),n),{})}var vr=e=>e!=null;function Wt(e,t,n={}){const{stop:o,getKey:r}=n;function i(s,l=[]){if(ve(s)||Array.isArray(s)){const a={};for(const[c,u]of Object.entries(s)){const d=r?.(c,u)??c,h=[...l,d];if(o?.(s,h))return t(s,l);const p=i(u,h);vr(p)&&(a[d]=p)}return a}return t(s,l)}return i(e)}function Sr(e,t){return e.reduce((n,o,r)=>{const i=t[r];return o!=null&&(n[i]=o),n},{})}function Ut(e,t,n=!0){const{utility:o,conditions:r}=t,{hasShorthand:i,resolveShorthand:s}=o;return Wt(e,l=>Array.isArray(l)?Sr(l,r.breakpoints.keys):l,{stop:l=>Array.isArray(l),getKey:n?l=>i?s(l):l:void 0})}var _r={shift:e=>e,finalize:e=>e,breakpoints:{keys:[]}},kr=e=>typeof e=="string"?e.replaceAll(/[\n\s]+/g," "):e;function Cr(e){const{utility:t,hash:n,conditions:o=_r}=e,r=s=>[t.prefix,s].filter(Boolean).join("-"),i=(s,l)=>{let a;if(n){const c=[...o.finalize(s),l];a=r(t.toHash(c,yr))}else a=[...o.finalize(s),r(l)].join(":");return a};return Ye(({base:s,...l}={})=>{const a=Object.assign(l,s),c=Ut(a,e),u=new Set;return Wt(c,(d,h)=>{const p=xr(d);if(d==null)return;const[P,...g]=o.shift(h),b=pr(g),x=t.transform(P,wr(kr(d)));let m=i(b,x.className);p&&(m=`${m}!`),u.add(m)}),Array.from(u).join(" ")})}function Er(...e){return e.flat().filter(t=>ve(t)&&Object.keys(hr(t)).length>0)}function Ar(e){function t(r){const i=Er(...r);return i.length===1?i:i.map(s=>Ut(s,e))}function n(...r){return Ft(...t(r))}function o(...r){return Object.assign({},...t(r))}return{mergeCss:Ye(n),assignCss:o}}var Pr=/([A-Z])/g,Rr=/^ms-/,Or=Ye(e=>e.startsWith("--")?e:e.replace(Pr,"-$1").replace(Rr,"-ms-").toLowerCase()),Ir="cm,mm,Q,in,pc,pt,px,em,ex,ch,rem,lh,rlh,vw,vh,vmin,vmax,vb,vi,svw,svh,lvw,lvh,dvw,dvh,cqw,cqh,cqi,cqb,cqmin,cqmax,%";`${Ir.split(",").join("|")}`;const Lr="_dark,_light,_hover,_focus,_focusWithin,_focusVisible,_disabled,_active,_visited,_target,_readOnly,_readWrite,_empty,_checked,_enabled,_expanded,_highlighted,_before,_after,_firstLetter,_firstLine,_marker,_selection,_file,_backdrop,_first,_last,_only,_even,_odd,_firstOfType,_lastOfType,_onlyOfType,_peerFocus,_peerHover,_peerActive,_peerFocusWithin,_peerFocusVisible,_peerDisabled,_peerChecked,_peerInvalid,_peerExpanded,_peerPlaceholderShown,_groupFocus,_groupHover,_groupActive,_groupFocusWithin,_groupFocusVisible,_groupDisabled,_groupChecked,_groupExpanded,_groupInvalid,_indeterminate,_required,_valid,_invalid,_autofill,_inRange,_outOfRange,_placeholder,_placeholderShown,_pressed,_selected,_default,_optional,_open,_closed,_fullscreen,_loading,_currentPage,_currentStep,_motionReduce,_motionSafe,_print,_landscape,_portrait,_osDark,_osLight,_highContrast,_lessContrast,_moreContrast,_ltr,_rtl,_scrollbar,_scrollbarThumb,_scrollbarTrack,_horizontal,_vertical,_starting,sm,smOnly,smDown,md,mdOnly,mdDown,lg,lgOnly,lgDown,xl,xlOnly,xlDown,2xl,2xlOnly,2xlDown,smToMd,smToLg,smToXl,smTo2xl,mdToLg,mdToXl,mdTo2xl,lgToXl,lgTo2xl,xlTo2xl,@/xs,@/sm,@/md,@/lg,@/xl,@/2xl,@/3xl,@/4xl,@/5xl,@/6xl,@/7xl,@/8xl,base",Vt=new Set(Lr.split(","));function bt(e){return Vt.has(e)||/^@|&|&$/.test(e)}const Tr=/^_/,$r=/&|@/;function Br(e){return e.map(t=>Vt.has(t)?t.replace(Tr,""):$r.test(t)?`[${zt(t.trim())}]`:t)}function jr(e){return e.sort((t,n)=>{const o=bt(t),r=bt(n);return o&&!r?1:!o&&r?-1:0})}const Nr="aspectRatio:aspect,boxDecorationBreak:decoration,zIndex:z,boxSizing:box,objectPosition:obj-pos,objectFit:obj-fit,overscrollBehavior:overscroll,overscrollBehaviorX:overscroll-x,overscrollBehaviorY:overscroll-y,position:pos/1,top:top,left:left,insetInline:inset-x/insetX,insetBlock:inset-y/insetY,inset:inset,insetBlockEnd:inset-b,insetBlockStart:inset-t,insetInlineEnd:end/insetEnd/1,insetInlineStart:start/insetStart/1,right:right,bottom:bottom,float:float,visibility:vis,display:d,hideFrom:hide,hideBelow:show,flexBasis:basis,flex:flex,flexDirection:flex/flexDir,flexGrow:grow,flexShrink:shrink,gridTemplateColumns:grid-cols,gridTemplateRows:grid-rows,gridColumn:col-span,gridRow:row-span,gridColumnStart:col-start,gridColumnEnd:col-end,gridAutoFlow:grid-flow,gridAutoColumns:auto-cols,gridAutoRows:auto-rows,gap:gap,gridGap:gap,gridRowGap:gap-x,gridColumnGap:gap-y,rowGap:gap-x,columnGap:gap-y,justifyContent:justify,alignContent:content,alignItems:items,alignSelf:self,padding:p/1,paddingLeft:pl/1,paddingRight:pr/1,paddingTop:pt/1,paddingBottom:pb/1,paddingBlock:py/1/paddingY,paddingBlockEnd:pb,paddingBlockStart:pt,paddingInline:px/paddingX/1,paddingInlineEnd:pe/1/paddingEnd,paddingInlineStart:ps/1/paddingStart,marginLeft:ml/1,marginRight:mr/1,marginTop:mt/1,marginBottom:mb/1,margin:m/1,marginBlock:my/1/marginY,marginBlockEnd:mb,marginBlockStart:mt,marginInline:mx/1/marginX,marginInlineEnd:me/1/marginEnd,marginInlineStart:ms/1/marginStart,spaceX:space-x,spaceY:space-y,outlineWidth:ring-width/ringWidth,outlineColor:ring-color/ringColor,outline:ring/1,outlineOffset:ring-offset/ringOffset,divideX:divide-x,divideY:divide-y,divideColor:divide-color,divideStyle:divide-style,width:w/1,inlineSize:w,minWidth:min-w/minW,minInlineSize:min-w,maxWidth:max-w/maxW,maxInlineSize:max-w,height:h/1,blockSize:h,minHeight:min-h/minH,minBlockSize:min-h,maxHeight:max-h/maxH,maxBlockSize:max-b,color:text,fontFamily:font,fontSize:fs,fontWeight:fw,fontSmoothing:smoothing,fontVariantNumeric:numeric,letterSpacing:tracking,lineHeight:leading,textAlign:text-align,textDecoration:text-decor,textDecorationColor:text-decor-color,textEmphasisColor:text-emphasis-color,textDecorationStyle:decoration-style,textDecorationThickness:decoration-thickness,textUnderlineOffset:underline-offset,textTransform:text-transform,textIndent:indent,textShadow:text-shadow,textShadowColor:text-shadow/textShadowColor,textOverflow:text-overflow,verticalAlign:v-align,wordBreak:break,textWrap:text-wrap,truncate:truncate,lineClamp:clamp,listStyleType:list-type,listStylePosition:list-pos,listStyleImage:list-img,backgroundPosition:bg-pos/bgPosition,backgroundPositionX:bg-pos-x/bgPositionX,backgroundPositionY:bg-pos-y/bgPositionY,backgroundAttachment:bg-attach/bgAttachment,backgroundClip:bg-clip/bgClip,background:bg/1,backgroundColor:bg/bgColor,backgroundOrigin:bg-origin/bgOrigin,backgroundImage:bg-img/bgImage,backgroundRepeat:bg-repeat/bgRepeat,backgroundBlendMode:bg-blend/bgBlendMode,backgroundSize:bg-size/bgSize,backgroundGradient:bg-gradient/bgGradient,textGradient:text-gradient,gradientFromPosition:gradient-from-pos,gradientToPosition:gradient-to-pos,gradientFrom:gradient-from,gradientTo:gradient-to,gradientVia:gradient-via,gradientViaPosition:gradient-via-pos,borderRadius:rounded/1,borderTopLeftRadius:rounded-tl/roundedTopLeft,borderTopRightRadius:rounded-tr/roundedTopRight,borderBottomRightRadius:rounded-br/roundedBottomRight,borderBottomLeftRadius:rounded-bl/roundedBottomLeft,borderTopRadius:rounded-t/roundedTop,borderRightRadius:rounded-r/roundedRight,borderBottomRadius:rounded-b/roundedBottom,borderLeftRadius:rounded-l/roundedLeft,borderStartStartRadius:rounded-ss/roundedStartStart,borderStartEndRadius:rounded-se/roundedStartEnd,borderStartRadius:rounded-s/roundedStart,borderEndStartRadius:rounded-es/roundedEndStart,borderEndEndRadius:rounded-ee/roundedEndEnd,borderEndRadius:rounded-e/roundedEnd,border:border,borderWidth:border-w,borderTopWidth:border-tw,borderLeftWidth:border-lw,borderRightWidth:border-rw,borderBottomWidth:border-bw,borderColor:border,borderInline:border-x/borderX,borderInlineWidth:border-x/borderXWidth,borderInlineColor:border-x/borderXColor,borderBlock:border-y/borderY,borderBlockWidth:border-y/borderYWidth,borderBlockColor:border-y/borderYColor,borderLeft:border-l,borderLeftColor:border-l,borderInlineStart:border-s/borderStart,borderInlineStartWidth:border-s/borderStartWidth,borderInlineStartColor:border-s/borderStartColor,borderRight:border-r,borderRightColor:border-r,borderInlineEnd:border-e/borderEnd,borderInlineEndWidth:border-e/borderEndWidth,borderInlineEndColor:border-e/borderEndColor,borderTop:border-t,borderTopColor:border-t,borderBottom:border-b,borderBottomColor:border-b,borderBlockEnd:border-be,borderBlockEndColor:border-be,borderBlockStart:border-bs,borderBlockStartColor:border-bs,boxShadow:shadow/1,boxShadowColor:shadow-color/shadowColor,mixBlendMode:mix-blend,filter:filter,brightness:brightness,contrast:contrast,grayscale:grayscale,hueRotate:hue-rotate,invert:invert,saturate:saturate,sepia:sepia,dropShadow:drop-shadow,blur:blur,backdropFilter:backdrop,backdropBlur:backdrop-blur,backdropBrightness:backdrop-brightness,backdropContrast:backdrop-contrast,backdropGrayscale:backdrop-grayscale,backdropHueRotate:backdrop-hue-rotate,backdropInvert:backdrop-invert,backdropOpacity:backdrop-opacity,backdropSaturate:backdrop-saturate,backdropSepia:backdrop-sepia,borderCollapse:border,borderSpacing:border-spacing,borderSpacingX:border-spacing-x,borderSpacingY:border-spacing-y,tableLayout:table,transitionTimingFunction:ease,transitionDelay:delay,transitionDuration:duration,transitionProperty:transition-prop,transition:transition,animation:animation,animationName:animation-name,animationTimingFunction:animation-ease,animationDuration:animation-duration,animationDelay:animation-delay,transformOrigin:origin,rotate:rotate,rotateX:rotate-x,rotateY:rotate-y,rotateZ:rotate-z,scale:scale,scaleX:scale-x,scaleY:scale-y,translate:translate,translateX:translate-x/x,translateY:translate-y/y,translateZ:translate-z/z,accentColor:accent,caretColor:caret,scrollBehavior:scroll,scrollbar:scrollbar,scrollMargin:scroll-m,scrollMarginLeft:scroll-ml,scrollMarginRight:scroll-mr,scrollMarginTop:scroll-mt,scrollMarginBottom:scroll-mb,scrollMarginBlock:scroll-my/scrollMarginY,scrollMarginBlockEnd:scroll-mb,scrollMarginBlockStart:scroll-mt,scrollMarginInline:scroll-mx/scrollMarginX,scrollMarginInlineEnd:scroll-me,scrollMarginInlineStart:scroll-ms,scrollPadding:scroll-p,scrollPaddingBlock:scroll-pb/scrollPaddingY,scrollPaddingBlockStart:scroll-pt,scrollPaddingBlockEnd:scroll-pb,scrollPaddingInline:scroll-px/scrollPaddingX,scrollPaddingInlineEnd:scroll-pe,scrollPaddingInlineStart:scroll-ps,scrollPaddingLeft:scroll-pl,scrollPaddingRight:scroll-pr,scrollPaddingTop:scroll-pt,scrollPaddingBottom:scroll-pb,scrollSnapAlign:snap-align,scrollSnapStop:snap-stop,scrollSnapType:snap-type,scrollSnapStrictness:snap-strictness,scrollSnapMargin:snap-m,scrollSnapMarginTop:snap-mt,scrollSnapMarginBottom:snap-mb,scrollSnapMarginLeft:snap-ml,scrollSnapMarginRight:snap-mr,touchAction:touch,userSelect:select,fill:fill,stroke:stroke,strokeWidth:stroke-w,srOnly:sr,debug:debug,appearance:appearance,backfaceVisibility:backface,clipPath:clip-path,hyphens:hyphens,mask:mask,maskImage:mask-image,maskSize:mask-size,textSizeAdjust:text-adjust,container:cq,containerName:cq-name,containerType:cq-type,textStyle:textStyle",Xt=new Map,qt=new Map;Nr.split(",").forEach(e=>{const[t,n]=e.split(":"),[o,...r]=n.split("/");Xt.set(t,o),r.length&&r.forEach(i=>{qt.set(i==="1"?o:i,t)})});const yt=e=>qt.get(e)||e,Ht={conditions:{shift:jr,finalize:Br,breakpoints:{keys:["base","sm","md","lg","xl","2xl"]}},utility:{transform:(e,t)=>{const n=yt(e);return{className:`${Xt.get(n)||Or(n)}_${zt(t)}`}},hasShorthand:!0,toHash:(e,t)=>t(e.join(":")),resolveShorthand:yt}},Dr=Cr(Ht),S=(...e)=>Dr(Yt(...e));S.raw=(...e)=>Yt(...e);const{mergeCss:Yt}=Ar(Ht);var Mr=Z("<span>量化平台"),zr=Z("<div><aside><div><div><div>V</div></div></div><nav><div></div></nav><div><button type=button></button></div></aside><div><header><div><div><h1></h1><div></div></div><div><button type=button>🔔</button><button type=button>⚙️</button><div>U</div></div></div></header><main>"),he=Z("<span>"),Fr=Z("<span>▼"),Wr=Z("<div>"),Ur=Z("<div><div><span>"),Vr=Z("<span>/");function Xr(e){const[t,n]=U(!1),[o,r]=U({market:!0,trading:!1,strategy:!1}),i=jt(),s=[{name:"仪表盘",href:"/",icon:"📊",type:"single",color:"#1890ff"},{name:"行情中心",href:"/market",icon:"📈",type:"single",color:"#52c41a"},{name:"交易中心",href:"/trading",icon:"💰",type:"single",color:"#fa8c16"},{name:"策略中心",href:"/strategy",icon:"🧠",type:"single",color:"#722ed1"},{name:"回测分析",href:"/backtest",icon:"🔄",type:"single",color:"#13c2c2"},{name:"投资组合",href:"/portfolio",icon:"📋",type:"single",color:"#eb2f96"},{name:"风险管理",href:"/risk",icon:"🛡️",type:"single",color:"#f5222d"},{name:"系统设置",href:"/settings",icon:"⚙️",type:"single",color:"#666"}],l=()=>{n(!t())},a=()=>({"/":"投资收益盘","/market":"行情中心","/trading":"交易中心","/strategy":"策略中心","/backtest":"回测分析","/portfolio":"投资组合","/risk":"风险管理","/settings":"系统设置"})[i.pathname]||"量化平台",c=()=>{const u=i.pathname;return u==="/"?["首页","投资收益盘"]:u==="/market"?["首页","行情中心"]:u==="/trading"?["首页","交易中心"]:u==="/strategy"?["首页","策略中心"]:u==="/backtest"?["首页","回测分析"]:u==="/portfolio"?["首页","投资组合"]:u==="/risk"?["首页","风险管理"]:u==="/settings"?["首页","系统设置"]:["首页",a()]};return(()=>{var u=zr(),d=u.firstChild,h=d.firstChild,p=h.firstChild,P=p.firstChild,g=h.nextSibling,b=g.firstChild,x=g.nextSibling,m=x.firstChild,T=d.nextSibling,R=T.firstChild,I=R.firstChild,X=I.firstChild,H=X.firstChild,Y=H.nextSibling,te=X.nextSibling,B=te.firstChild,L=B.nextSibling,W=L.nextSibling,y=R.nextSibling;return z(p,O(ee,{get when(){return!t()},get children(){var f=Mr();return N(()=>_(f,S({fontSize:"16px",fontWeight:"600",color:"#333"}))),f}}),null),z(b,O(Re,{each:s,children:f=>O(ee,{get when(){return f.type==="single"},get fallback(){return(()=>{var k=Ur(),j=k.firstChild,G=j.firstChild;return j.$$click=()=>toggleGroup(f.key),z(G,()=>f.icon),z(j,O(ee,{get when(){return!t()},get children(){return[(()=>{var C=he();return z(C,()=>f.name),N(()=>_(C,S({flex:1}))),C})(),(()=>{var C=Fr();return N(()=>_(C,S({fontSize:"12px",transform:o()[f.key]?"rotate(180deg)":"rotate(0deg)",transition:"transform 0.2s ease"}))),C})()]}}),null),z(k,O(ee,{get when(){return Rt(()=>!t())()&&o()[f.key]},get children(){var C=Wr();return z(C,O(Re,{get each(){return f.children},children:D=>O(pt,{get href(){return D.href},get class(){return S({display:"block",px:"12px",py:"6px",mx:"4px",borderRadius:"4px",fontSize:"13px",color:i.pathname===D.href?"#1890ff":"#666",bg:i.pathname===D.href?"#e6f7ff":"transparent",textDecoration:"none",_hover:{bg:i.pathname===D.href?"#e6f7ff":"#f5f5f5"}})},get children(){return D.name}})})),N(()=>_(C,S({ml:"20px",mt:"4px"}))),C}}),null),N(C=>{var D=S({mb:"4px"}),V=S({display:"flex",alignItems:"center",px:"12px",py:"8px",mx:"4px",borderRadius:"6px",cursor:"pointer",fontSize:"14px",fontWeight:"500",color:"#666",_hover:{bg:"#f5f5f5"}}),Q=S({mr:"8px",fontSize:"16px"});return D!==C.e&&_(k,C.e=D),V!==C.t&&_(j,C.t=V),Q!==C.a&&_(G,C.a=Q),C},{e:void 0,t:void 0,a:void 0}),k})()},get children(){return O(pt,{get href(){return f.href},get class(){return S({display:"flex",alignItems:"center",px:"12px",py:"10px",mx:"4px",mb:"4px",borderRadius:"6px",fontSize:"14px",fontWeight:"500",color:i.pathname===f.href?"#1890ff":"#666",bg:i.pathname===f.href?"#e6f7ff":"transparent",textDecoration:"none",_hover:{bg:i.pathname===f.href?"#e6f7ff":"#f5f5f5"}})},get children(){return[(()=>{var k=he();return z(k,()=>f.icon),N(()=>_(k,S({mr:"8px",fontSize:"16px"}))),k})(),O(ee,{get when(){return!t()},get children(){var k=he();return z(k,()=>f.name),k}})]}})}})})),m.$$click=l,z(m,()=>t()?"→":"←"),z(H,a),z(Y,O(Re,{get each(){return c()},children:(f,k)=>[O(ee,{get when(){return k()>0},get children(){var j=Vr();return N(()=>_(j,S({mx:"4px"}))),j}}),(()=>{var j=he();return z(j,f),N(()=>_(j,S({color:k()===c().length-1?"#1890ff":"#999"}))),j})()]})),z(y,()=>e.children),N(f=>{var k=S({display:"flex",height:"100vh",bg:"#f5f7fa"}),j=S({width:t()?"64px":"240px",bg:"white",borderRight:"1px solid #e8e8e8",display:"flex",flexDirection:"column",transition:"width 0.3s ease",boxShadow:"2px 0 8px rgba(0, 0, 0, 0.1)",position:"relative",zIndex:10}),G=S({height:"64px",display:"flex",alignItems:"center",px:"16px",borderBottom:"1px solid #e8e8e8"}),C=S({display:"flex",alignItems:"center",gap:"12px"}),D=S({width:"32px",height:"32px",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"18px"}),V=S({flex:1,py:"16px",overflowY:"auto"}),Q=S({px:"8px"}),Ge=S({p:"16px",borderTop:"1px solid #e8e8e8"}),Ke=S({width:"100%",p:"8px",border:"none",bg:"transparent",borderRadius:"6px",cursor:"pointer",fontSize:"16px",_hover:{bg:"#f5f5f5"}}),Je=S({flex:1,display:"flex",flexDirection:"column"}),Ze=S({bg:"white",borderBottom:"1px solid #e8e8e8",px:"24px",py:"16px"}),Qe=S({display:"flex",justifyContent:"space-between",alignItems:"center"}),et=S({fontSize:"20px",fontWeight:"600",color:"#333",mb:"4px"}),tt=S({fontSize:"12px",color:"#999"}),nt=S({display:"flex",alignItems:"center",gap:"12px"}),rt=S({p:"8px",border:"none",bg:"transparent",borderRadius:"6px",cursor:"pointer",fontSize:"16px",_hover:{bg:"#f5f5f5"}}),ot=S({p:"8px",border:"none",bg:"transparent",borderRadius:"6px",cursor:"pointer",fontSize:"16px",_hover:{bg:"#f5f5f5"}}),it=S({width:"32px",height:"32px",bg:"#1890ff",color:"white",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"14px",fontWeight:"bold"}),st=S({flex:1,p:"24px",overflowY:"auto"});return k!==f.e&&_(u,f.e=k),j!==f.t&&_(d,f.t=j),G!==f.a&&_(h,f.a=G),C!==f.o&&_(p,f.o=C),D!==f.i&&_(P,f.i=D),V!==f.n&&_(g,f.n=V),Q!==f.s&&_(b,f.s=Q),Ge!==f.h&&_(x,f.h=Ge),Ke!==f.r&&_(m,f.r=Ke),Je!==f.d&&_(T,f.d=Je),Ze!==f.l&&_(R,f.l=Ze),Qe!==f.u&&_(I,f.u=Qe),et!==f.c&&_(H,f.c=et),tt!==f.w&&_(Y,f.w=tt),nt!==f.m&&_(te,f.m=nt),rt!==f.f&&_(B,f.f=rt),ot!==f.y&&_(L,f.y=ot),it!==f.g&&_(W,f.g=it),st!==f.p&&_(y,f.p=st),f},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0}),u})()}Ve(["click"]);var qr=Z('<div style="border-radius:8px;box-shadow:0 2px 8px rgba(0,0,0,0.1)"><h1 style=margin-bottom:16px>🚀 新界面测试成功！</h1><p style=font-size:16px>如果您看到这个消息，说明新的应用架构已经正常工作了！</p><div style="grid-template-columns:repeat(auto-fit, minmax(200px, 1fr));margin-top:20px"><div style=border-radius:8px><h3>✅ SolidJS</h3><p style=font-size:14px>框架正常运行</p></div><div style=border-radius:8px><h3>✅ 路由</h3><p style=font-size:14px>路由系统正常</p></div><div style=border-radius:8px><h3>✅ 布局</h3><p style=font-size:14px>布局组件正常');const Hr=ke(()=>Ce(()=>import("./Dashboard-BtIWQLDr.js"),[])),Yr=ke(()=>Ce(()=>import("./StrategyEditor-yefHS0za.js"),[])),Gr=ke(()=>Ce(()=>import("./BacktestAnalysis-C9iHprcP.js"),[])),Kr=ke(()=>Ce(()=>import("./MarketData-DSJ4MKZb.js"),[]));function Jr(){return(()=>{var e=qr(),t=e.firstChild,n=t.nextSibling,o=n.nextSibling,r=o.firstChild,i=r.firstChild,s=i.nextSibling,l=r.nextSibling,a=l.firstChild,c=a.nextSibling,u=l.nextSibling,d=u.firstChild,h=d.nextSibling;return E(e,"padding","20px"),E(e,"background","white"),E(e,"margin","20px"),E(t,"color","#1890ff"),E(n,"color","#666"),E(o,"display","grid"),E(o,"gap","16px"),E(r,"padding","16px"),E(r,"background","#f0f9ff"),E(r,"border","1px solid #0ea5e9"),E(i,"margin","0 0 8px 0"),E(i,"color","#0ea5e9"),E(s,"margin","0"),E(s,"color","#666"),E(l,"padding","16px"),E(l,"background","#f0fdf4"),E(l,"border","1px solid #22c55e"),E(a,"margin","0 0 8px 0"),E(a,"color","#22c55e"),E(c,"margin","0"),E(c,"color","#666"),E(u,"padding","16px"),E(u,"background","#fefce8"),E(u,"border","1px solid #eab308"),E(d,"margin","0 0 8px 0"),E(d,"color","#eab308"),E(h,"margin","0"),E(h,"color","#666"),e})()}function Zr(){return O(ur,{root:Xr,get children(){return[O(se,{path:"/",component:Hr}),O(se,{path:"/test",component:Jr}),O(se,{path:"/strategy",component:Yr}),O(se,{path:"/backtest",component:Gr}),O(se,{path:"/market",component:Kr})]}})}const Gt=document.getElementById("root");if(!Gt)throw new Error("Root element not found");kn(()=>O(Zr,{}),Gt);export{Re as F,Me as a,O as b,U as c,N as d,S as e,_ as f,Ve as g,en as h,z as i,$ as j,Rt as m,Qr as o,E as s,Z as t,On as u};
