/**
 * 数据处理 Worker
 * 处理大量数据的计算和转换
 */

// 数据清洗函数
function cleanData(data, rules) {
  let cleaned = [...data];
  
  // 移除空值
  if (rules.removeNull) {
    cleaned = cleaned.filter(item => 
      item.open != null && 
      item.high != null && 
      item.low != null && 
      item.close != null
    );
  }
  
  // 移除异常值
  if (rules.removeOutliers) {
    cleaned = removeOutliers(cleaned, rules.outlierMethod || 'iqr');
  }
  
  // 填充缺失值
  if (rules.fillMissing) {
    cleaned = fillMissingValues(cleaned, rules.fillMethod || 'forward');
  }
  
  // 数据验证
  if (rules.validate) {
    cleaned = validateData(cleaned);
  }
  
  return cleaned;
}

function removeOutliers(data, method) {
  if (method === 'iqr') {
    return removeOutliersIQR(data);
  } else if (method === 'zscore') {
    return removeOutliersZScore(data);
  }
  return data;
}

function removeOutliersIQR(data) {
  const prices = data.map(item => item.close).sort((a, b) => a - b);
  const q1 = prices[Math.floor(prices.length * 0.25)];
  const q3 = prices[Math.floor(prices.length * 0.75)];
  const iqr = q3 - q1;
  const lowerBound = q1 - 1.5 * iqr;
  const upperBound = q3 + 1.5 * iqr;
  
  return data.filter(item => 
    item.close >= lowerBound && item.close <= upperBound
  );
}

function removeOutliersZScore(data) {
  const prices = data.map(item => item.close);
  const mean = prices.reduce((a, b) => a + b, 0) / prices.length;
  const std = Math.sqrt(
    prices.reduce((acc, price) => acc + Math.pow(price - mean, 2), 0) / prices.length
  );
  
  return data.filter(item => {
    const zScore = Math.abs((item.close - mean) / std);
    return zScore < 3; // 保留 3 个标准差内的数据
  });
}

function fillMissingValues(data, method) {
  const result = [...data];
  
  for (let i = 1; i < result.length; i++) {
    if (result[i].close == null) {
      if (method === 'forward') {
        result[i].close = result[i - 1].close;
        result[i].open = result[i - 1].open;
        result[i].high = result[i - 1].high;
        result[i].low = result[i - 1].low;
      } else if (method === 'interpolate' && i < result.length - 1) {
        // 线性插值
        let nextValidIndex = i + 1;
        while (nextValidIndex < result.length && result[nextValidIndex].close == null) {
          nextValidIndex++;
        }
        
        if (nextValidIndex < result.length) {
          const prevValue = result[i - 1].close;
          const nextValue = result[nextValidIndex].close;
          const steps = nextValidIndex - i + 1;
          const stepSize = (nextValue - prevValue) / steps;
          
          result[i].close = prevValue + stepSize;
          result[i].open = prevValue + stepSize;
          result[i].high = prevValue + stepSize;
          result[i].low = prevValue + stepSize;
        }
      }
    }
  }
  
  return result;
}

function validateData(data) {
  return data.filter(item => {
    // 基本验证：高价 >= 低价，收盘价在高低价之间
    return (
      item.high >= item.low &&
      item.close >= item.low &&
      item.close <= item.high &&
      item.open >= item.low &&
      item.open <= item.high &&
      item.volume >= 0
    );
  });
}

// 统计计算函数
function calculateStatistics(data) {
  const prices = data.map(item => item.close);
  const returns = [];
  
  for (let i = 1; i < prices.length; i++) {
    returns.push((prices[i] - prices[i - 1]) / prices[i - 1]);
  }
  
  const mean = returns.reduce((a, b) => a + b, 0) / returns.length;
  const variance = returns.reduce((acc, ret) => acc + Math.pow(ret - mean, 2), 0) / returns.length;
  const std = Math.sqrt(variance);
  
  // 计算偏度
  const skewness = returns.reduce((acc, ret) => acc + Math.pow((ret - mean) / std, 3), 0) / returns.length;
  
  // 计算峰度
  const kurtosis = returns.reduce((acc, ret) => acc + Math.pow((ret - mean) / std, 4), 0) / returns.length - 3;
  
  // 计算最大回撤
  let peak = prices[0];
  let maxDrawdown = 0;
  const drawdowns = [];
  
  for (const price of prices) {
    if (price > peak) {
      peak = price;
    }
    const drawdown = (peak - price) / peak;
    drawdowns.push(drawdown);
    if (drawdown > maxDrawdown) {
      maxDrawdown = drawdown;
    }
  }
  
  // 计算夏普比率（假设无风险利率为 0）
  const sharpeRatio = mean / std * Math.sqrt(252); // 年化
  
  // 计算 VaR (Value at Risk)
  const sortedReturns = [...returns].sort((a, b) => a - b);
  const var95 = sortedReturns[Math.floor(sortedReturns.length * 0.05)];
  const var99 = sortedReturns[Math.floor(sortedReturns.length * 0.01)];
  
  return {
    count: data.length,
    priceStats: {
      min: Math.min(...prices),
      max: Math.max(...prices),
      mean: prices.reduce((a, b) => a + b, 0) / prices.length,
      median: prices.sort((a, b) => a - b)[Math.floor(prices.length / 2)]
    },
    returnStats: {
      mean: mean,
      std: std,
      variance: variance,
      skewness: skewness,
      kurtosis: kurtosis,
      min: Math.min(...returns),
      max: Math.max(...returns)
    },
    riskMetrics: {
      maxDrawdown: maxDrawdown,
      sharpeRatio: isNaN(sharpeRatio) ? 0 : sharpeRatio,
      var95: var95,
      var99: var99
    },
    volumeStats: {
      mean: data.reduce((acc, item) => acc + (item.volume || 0), 0) / data.length,
      total: data.reduce((acc, item) => acc + (item.volume || 0), 0)
    }
  };
}

// 市场数据处理
function processMarketData(rawData) {
  // 数据格式标准化
  const standardized = rawData.map(item => ({
    time: typeof item.time === 'string' ? new Date(item.time).getTime() / 1000 : item.time,
    open: parseFloat(item.open),
    high: parseFloat(item.high),
    low: parseFloat(item.low),
    close: parseFloat(item.close),
    volume: parseFloat(item.volume || 0)
  }));
  
  // 按时间排序
  standardized.sort((a, b) => a.time - b.time);
  
  // 计算技术指标
  const indicators = calculateBasicIndicators(standardized);
  
  // 计算价格变化
  const priceChanges = [];
  for (let i = 1; i < standardized.length; i++) {
    const change = standardized[i].close - standardized[i - 1].close;
    const changePercent = change / standardized[i - 1].close;
    priceChanges.push({
      time: standardized[i].time,
      change: change,
      changePercent: changePercent
    });
  }
  
  return {
    data: standardized,
    indicators: indicators,
    priceChanges: priceChanges,
    summary: {
      count: standardized.length,
      timeRange: {
        start: standardized[0]?.time,
        end: standardized[standardized.length - 1]?.time
      },
      priceRange: {
        min: Math.min(...standardized.map(item => item.low)),
        max: Math.max(...standardized.map(item => item.high))
      }
    }
  };
}

function calculateBasicIndicators(data) {
  const indicators = {};
  
  // 简单移动平均
  indicators.sma5 = calculateSMA(data, 5);
  indicators.sma20 = calculateSMA(data, 20);
  indicators.sma50 = calculateSMA(data, 50);
  
  // 布林带
  indicators.bollinger = calculateBollingerBands(data, 20, 2);
  
  return indicators;
}

function calculateSMA(data, period) {
  const result = [];
  for (let i = period - 1; i < data.length; i++) {
    const sum = data.slice(i - period + 1, i + 1).reduce((acc, val) => acc + val.close, 0);
    result.push({
      time: data[i].time,
      value: sum / period
    });
  }
  return result;
}

function calculateBollingerBands(data, period, multiplier) {
  const sma = calculateSMA(data, period);
  const result = [];
  
  for (let i = period - 1; i < data.length; i++) {
    const slice = data.slice(i - period + 1, i + 1);
    const mean = slice.reduce((acc, val) => acc + val.close, 0) / period;
    const variance = slice.reduce((acc, val) => acc + Math.pow(val.close - mean, 2), 0) / period;
    const std = Math.sqrt(variance);
    
    result.push({
      time: data[i].time,
      upper: mean + multiplier * std,
      middle: mean,
      lower: mean - multiplier * std
    });
  }
  
  return result;
}

// Worker 消息处理
self.onmessage = function(event) {
  const { id, type, data } = event.data;
  
  try {
    let result;
    
    switch (type) {
      case 'process_market_data':
        result = processMarketData(data);
        break;
        
      case 'calculate_statistics':
        result = calculateStatistics(data);
        break;
        
      case 'clean_data':
        result = cleanData(data.data, data.rules);
        break;
        
      default:
        throw new Error(`Unknown task type: ${type}`);
    }
    
    self.postMessage({
      id,
      type,
      success: true,
      data: result,
      timestamp: Date.now()
    });
    
  } catch (error) {
    self.postMessage({
      id,
      type,
      success: false,
      error: error.message,
      timestamp: Date.now()
    });
  }
};
