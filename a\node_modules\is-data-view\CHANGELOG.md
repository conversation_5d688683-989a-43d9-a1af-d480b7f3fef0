# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.2](https://github.com/inspect-js/is-data-view/compare/v1.0.1...v1.0.2) - 2024-12-11

### Commits

- [types] use shared config [`3a80072`](https://github.com/inspect-js/is-data-view/commit/3a800720dd322ea4656f29d28f1d28117fb94537)
- [actions] split out node 10-20, and 20+ [`e2f37fd`](https://github.com/inspect-js/is-data-view/commit/e2f37fdf421c4629730406c2b61d5481dce4e448)
- [<PERSON>] update `@ljharb/eslint-config`, `@ljharb/tsconfig`, `@types/node`, `@types/object-inspect`, `@types/tape`, `auto-changelog`, `es-value-fixtures`, `object-inspect`, `tape` [`6f8e9dd`](https://github.com/inspect-js/is-data-view/commit/6f8e9dda376e421d30dd94e891bea3fbae7967ee)
- [Dev Deps] update `@types/node`, `available-typed-arrays`, `tape` [`37be591`](https://github.com/inspect-js/is-data-view/commit/37be59163c8df9abb016bbb4c7d76f3af549f1d2)
- [Fix] add missing dependencies; use `call-bound` directly [`34de4b5`](https://github.com/inspect-js/is-data-view/commit/34de4b5f5448ca1bb95c34686c51b5351904a317)
- [Tests] replace `aud` with `npm audit` [`aa7060a`](https://github.com/inspect-js/is-data-view/commit/aa7060ad77c9ba2d500b124b73662c3b069fc721)
- [Dev Deps] add missing peer dep [`3d302d7`](https://github.com/inspect-js/is-data-view/commit/3d302d7dbf99d75fcf4a3de14687e423ec88728f)

## [v1.0.1](https://github.com/inspect-js/is-data-view/compare/v1.0.0...v1.0.1) - 2024-02-02

### Commits

- [patch] add types [`c2728ef`](https://github.com/inspect-js/is-data-view/commit/c2728ef20064bba2588eed503a0c2e36985b638a)
- [Dev Deps] update `aud`, `available-typed-arrays`, `has-tostringtag`, `npmignore`, `object-inspect`, `tape` [`e7f9ebc`](https://github.com/inspect-js/is-data-view/commit/e7f9ebccf9aacdc112dd4f665271c96417ddfa64)
- [Deps] update `is-typed-array` [`2ca9333`](https://github.com/inspect-js/is-data-view/commit/2ca9333516afac321431ddae02d6791d50e8d5c2)

## v1.0.0 - 2024-01-31

### Commits

- Initial implementation, tests, readme [`6f7e424`](https://github.com/inspect-js/is-data-view/commit/6f7e4244ae9d766309b8f050c0b786e9c0692825)
- Initial commit [`4b7ea57`](https://github.com/inspect-js/is-data-view/commit/4b7ea57d6942dd268bcda990a96b8cd663b19eb8)
- npm init [`25130e2`](https://github.com/inspect-js/is-data-view/commit/25130e2dbecc91d398cf74c39878aa89f5e604ab)
- Only apps should have lockfiles [`18cde47`](https://github.com/inspect-js/is-data-view/commit/18cde474201a292ebdaa704d232127c814cb1d0e)
