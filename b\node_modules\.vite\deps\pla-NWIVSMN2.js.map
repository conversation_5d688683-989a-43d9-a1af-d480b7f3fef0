{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/pla/pla.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/pla/pla.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"[\", \"]\"],\n    [\"<\", \">\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"<\", close: \">\" },\n    { open: \"(\", close: \")\" }\n  ],\n  surroundingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"<\", close: \">\" },\n    { open: \"(\", close: \")\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".pla\",\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \".i\",\n    \".o\",\n    \".mv\",\n    \".ilb\",\n    \".ob\",\n    \".label\",\n    \".type\",\n    \".phase\",\n    \".pair\",\n    \".symbolic\",\n    \".symbolic-output\",\n    \".kiss\",\n    \".p\",\n    \".e\",\n    \".end\"\n  ],\n  comment: /#.*$/,\n  identifier: /[a-zA-Z]+[a-zA-Z0-9_\\-]*/,\n  plaContent: /[01\\-~\\|]+/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      [/@comment/, \"comment\"],\n      [\n        /\\.([a-zA-Z_\\-]+)/,\n        {\n          cases: {\n            \"@eos\": { token: \"keyword.$1\" },\n            \"@keywords\": {\n              cases: {\n                \".type\": { token: \"keyword.$1\", next: \"@type\" },\n                \"@default\": { token: \"keyword.$1\", next: \"@keywordArg\" }\n              }\n            },\n            \"@default\": { token: \"keyword.$1\" }\n          }\n        }\n      ],\n      [/@identifier/, \"identifier\"],\n      [/@plaContent/, \"string\"]\n    ],\n    whitespace: [[/[ \\t\\r\\n]+/, \"\"]],\n    type: [{ include: \"@whitespace\" }, [/\\w+/, { token: \"type\", next: \"@pop\" }]],\n    keywordArg: [\n      [\n        /[ \\t\\r\\n]+/,\n        {\n          cases: {\n            \"@eos\": { token: \"\", next: \"@pop\" },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/@comment/, \"comment\", \"@pop\"],\n      [\n        /[<>()\\[\\]]/,\n        {\n          cases: {\n            \"@eos\": { token: \"@brackets\", next: \"@pop\" },\n            \"@default\": \"@brackets\"\n          }\n        }\n      ],\n      [\n        /\\-?\\d+/,\n        {\n          cases: {\n            \"@eos\": { token: \"number\", next: \"@pop\" },\n            \"@default\": \"number\"\n          }\n        }\n      ],\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@eos\": { token: \"identifier\", next: \"@pop\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [\n        /[;=]/,\n        {\n          cases: {\n            \"@eos\": { token: \"delimiter\", next: \"@pop\" },\n            \"@default\": \"delimiter\"\n          }\n        }\n      ]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,EAC1D;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,YAAY,SAAS;AAAA,MACtB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,aAAa;AAAA,YAC9B,aAAa;AAAA,cACX,OAAO;AAAA,gBACL,SAAS,EAAE,OAAO,cAAc,MAAM,QAAQ;AAAA,gBAC9C,YAAY,EAAE,OAAO,cAAc,MAAM,cAAc;AAAA,cACzD;AAAA,YACF;AAAA,YACA,YAAY,EAAE,OAAO,aAAa;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,eAAe,YAAY;AAAA,MAC5B,CAAC,eAAe,QAAQ;AAAA,IAC1B;AAAA,IACA,YAAY,CAAC,CAAC,cAAc,EAAE,CAAC;AAAA,IAC/B,MAAM,CAAC,EAAE,SAAS,cAAc,GAAG,CAAC,OAAO,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC,CAAC;AAAA,IAC3E,YAAY;AAAA,MACV;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,IAAI,MAAM,OAAO;AAAA,YAClC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,YAAY,WAAW,MAAM;AAAA,MAC9B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,aAAa,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YACxC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,cAAc,MAAM,OAAO;AAAA,YAC5C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,aAAa,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;", "names": []}