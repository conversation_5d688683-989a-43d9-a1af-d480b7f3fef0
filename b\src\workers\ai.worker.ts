// AI Worker for running machine learning models in background
// This worker handles AI model loading and inference to avoid blocking the main thread

interface AIWorkerMessage {
  type: 'load-model' | 'generate-strategy' | 'analyze-market' | 'optimize-parameters';
  data: any;
  id: string;
}

interface AIWorkerResponse {
  type: 'model-loaded' | 'strategy-generated' | 'market-analyzed' | 'parameters-optimized' | 'error';
  data: any;
  id: string;
}

// 模拟AI模型状态
let isModelLoaded = false;
let modelLoadingProgress = 0;

// 监听主线程消息
self.addEventListener('message', async (event: MessageEvent<AIWorkerMessage>) => {
  const { type, data, id } = event.data;
  
  try {
    switch (type) {
      case 'load-model':
        await loadAIModel(id);
        break;
        
      case 'generate-strategy':
        await generateStrategy(data, id);
        break;
        
      case 'analyze-market':
        await analyzeMarket(data, id);
        break;
        
      case 'optimize-parameters':
        await optimizeParameters(data, id);
        break;
        
      default:
        throw new Error(`Unknown message type: ${type}`);
    }
  } catch (error) {
    postMessage({
      type: 'error',
      data: { error: error instanceof Error ? error.message : 'Unknown error' },
      id
    } as AIWorkerResponse);
  }
});

// 加载AI模型（模拟）
async function loadAIModel(id: string) {
  if (isModelLoaded) {
    postMessage({
      type: 'model-loaded',
      data: { status: 'already-loaded' },
      id
    } as AIWorkerResponse);
    return;
  }
  
  // 模拟模型加载过程
  for (let i = 0; i <= 100; i += 10) {
    modelLoadingProgress = i;
    
    // 发送加载进度
    postMessage({
      type: 'model-loaded',
      data: { status: 'loading', progress: i },
      id
    } as AIWorkerResponse);
    
    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  isModelLoaded = true;
  
  postMessage({
    type: 'model-loaded',
    data: { status: 'loaded', progress: 100 },
    id
  } as AIWorkerResponse);
}

// 生成AI策略
async function generateStrategy(data: { prompt: string; parameters?: any }, id: string) {
  if (!isModelLoaded) {
    throw new Error('AI model not loaded');
  }
  
  const { prompt, parameters = {} } = data;
  
  // 模拟AI推理延迟
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 基于提示词生成策略代码
  const strategyTemplates = {
    'rsi': {
      name: 'RSI反转策略',
      code: generateRSIStrategy(parameters),
      description: '基于RSI指标的均值回归策略'
    },
    'ma': {
      name: '移动平均策略',
      code: generateMAStrategy(parameters),
      description: '基于移动平均线的趋势跟踪策略'
    },
    'bollinger': {
      name: '布林带策略',
      code: generateBollingerStrategy(parameters),
      description: '基于布林带的突破策略'
    }
  };
  
  // 简单的关键词匹配来选择策略模板
  let selectedTemplate = strategyTemplates.ma; // 默认
  
  if (prompt.toLowerCase().includes('rsi') || prompt.toLowerCase().includes('超买') || prompt.toLowerCase().includes('超卖')) {
    selectedTemplate = strategyTemplates.rsi;
  } else if (prompt.toLowerCase().includes('布林') || prompt.toLowerCase().includes('bollinger')) {
    selectedTemplate = strategyTemplates.bollinger;
  } else if (prompt.toLowerCase().includes('均线') || prompt.toLowerCase().includes('移动平均')) {
    selectedTemplate = strategyTemplates.ma;
  }
  
  // 根据提示词调整策略参数
  const adjustedCode = adjustStrategyParameters(selectedTemplate.code, prompt, parameters);
  
  postMessage({
    type: 'strategy-generated',
    data: {
      name: selectedTemplate.name,
      code: adjustedCode,
      description: `${selectedTemplate.description} - 基于AI分析: "${prompt}"`,
      confidence: Math.random() * 0.3 + 0.7, // 70-100%的置信度
      parameters: extractParameters(adjustedCode)
    },
    id
  } as AIWorkerResponse);
}

// 市场分析
async function analyzeMarket(data: { marketData: any; timeframe: string }, id: string) {
  if (!isModelLoaded) {
    throw new Error('AI model not loaded');
  }
  
  // 模拟AI分析延迟
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // 生成模拟的市场分析结果
  const analysis = {
    sentiment: Math.random() > 0.5 ? 'bullish' : 'bearish',
    confidence: Math.random() * 0.4 + 0.6, // 60-100%
    signals: [
      {
        type: 'trend',
        direction: Math.random() > 0.5 ? 'up' : 'down',
        strength: Math.random() * 0.5 + 0.5,
        description: '基于技术指标分析的趋势信号'
      },
      {
        type: 'momentum',
        direction: Math.random() > 0.5 ? 'up' : 'down',
        strength: Math.random() * 0.5 + 0.5,
        description: '动量指标显示的市场动能'
      }
    ],
    recommendations: [
      '建议关注技术指标的突破信号',
      '注意风险管理，设置合理的止损位',
      '考虑分批建仓以降低风险'
    ]
  };
  
  postMessage({
    type: 'market-analyzed',
    data: analysis,
    id
  } as AIWorkerResponse);
}

// 参数优化
async function optimizeParameters(data: { strategy: string; parameters: any; objective: string }, id: string) {
  if (!isModelLoaded) {
    throw new Error('AI model not loaded');
  }
  
  // 模拟参数优化过程
  const iterations = 10;
  
  for (let i = 1; i <= iterations; i++) {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    postMessage({
      type: 'parameters-optimized',
      data: {
        status: 'optimizing',
        progress: (i / iterations) * 100,
        currentBest: {
          parameters: generateRandomParameters(),
          score: Math.random() * 0.3 + 0.7
        }
      },
      id
    } as AIWorkerResponse);
  }
  
  // 最终优化结果
  postMessage({
    type: 'parameters-optimized',
    data: {
      status: 'completed',
      progress: 100,
      optimizedParameters: generateOptimizedParameters(data.parameters),
      improvement: Math.random() * 0.2 + 0.1, // 10-30%的改进
      iterations: iterations
    },
    id
  } as AIWorkerResponse);
}

// 生成RSI策略代码
function generateRSIStrategy(parameters: any) {
  const period = parameters.rsiPeriod || 14;
  const oversold = parameters.oversoldLevel || 30;
  const overbought = parameters.overboughtLevel || 70;
  
  return `# AI生成的RSI反转策略
import talib

def initialize(context):
    context.rsi_period = ${period}
    context.oversold_level = ${oversold}
    context.overbought_level = ${overbought}
    context.symbol = 'AAPL'

def handle_data(context, data):
    # 获取历史价格数据
    prices = data.history(context.symbol, 'price', context.rsi_period + 10, '1d')
    
    # 计算RSI
    rsi = talib.RSI(prices.values, timeperiod=context.rsi_period)[-1]
    
    # 获取当前持仓
    current_position = context.portfolio.positions[context.symbol].amount
    
    # AI优化的交易逻辑
    if rsi < context.oversold_level and current_position <= 0:
        # RSI超卖，买入信号
        order_target_percent(context.symbol, 1.0)
        log.info(f"AI买入信号: RSI({rsi:.2f}) < 超卖线({context.oversold_level})")
    elif rsi > context.overbought_level and current_position > 0:
        # RSI超买，卖出信号
        order_target_percent(context.symbol, 0.0)
        log.info(f"AI卖出信号: RSI({rsi:.2f}) > 超买线({context.overbought_level})")
`;
}

// 生成移动平均策略代码
function generateMAStrategy(parameters: any) {
  const shortPeriod = parameters.shortPeriod || 5;
  const longPeriod = parameters.longPeriod || 20;
  
  return `# AI生成的双均线策略
def initialize(context):
    context.short_period = ${shortPeriod}
    context.long_period = ${longPeriod}
    context.symbol = 'AAPL'

def handle_data(context, data):
    # 获取历史价格数据
    prices = data.history(context.symbol, 'price', context.long_period + 1, '1d')
    
    # 计算移动平均线
    short_ma = prices.tail(context.short_period).mean()
    long_ma = prices.tail(context.long_period).mean()
    
    # 获取当前持仓
    current_position = context.portfolio.positions[context.symbol].amount
    
    # AI优化的交易逻辑
    if short_ma > long_ma and current_position <= 0:
        # 金叉买入
        order_target_percent(context.symbol, 1.0)
        log.info(f"AI买入信号: 短期均线({short_ma:.2f}) > 长期均线({long_ma:.2f})")
    elif short_ma < long_ma and current_position > 0:
        # 死叉卖出
        order_target_percent(context.symbol, 0.0)
        log.info(f"AI卖出信号: 短期均线({short_ma:.2f}) < 长期均线({long_ma:.2f})")
`;
}

// 生成布林带策略代码
function generateBollingerStrategy(parameters: any) {
  const period = parameters.period || 20;
  const stdDev = parameters.stdDev || 2;
  
  return `# AI生成的布林带策略
import talib

def initialize(context):
    context.period = ${period}
    context.std_dev = ${stdDev}
    context.symbol = 'AAPL'

def handle_data(context, data):
    # 获取历史价格数据
    prices = data.history(context.symbol, 'price', context.period + 5, '1d')
    
    # 计算布林带
    upper, middle, lower = talib.BBANDS(
        prices.values, 
        timeperiod=context.period, 
        nbdevup=context.std_dev, 
        nbdevdn=context.std_dev
    )
    
    current_price = prices.iloc[-1]
    current_upper = upper[-1]
    current_lower = lower[-1]
    
    # 获取当前持仓
    current_position = context.portfolio.positions[context.symbol].amount
    
    # AI优化的交易逻辑
    if current_price < current_lower and current_position <= 0:
        # 价格跌破下轨，买入
        order_target_percent(context.symbol, 1.0)
        log.info(f"AI买入信号: 价格({current_price:.2f}) < 下轨({current_lower:.2f})")
    elif current_price > current_upper and current_position > 0:
        # 价格突破上轨，卖出
        order_target_percent(context.symbol, 0.0)
        log.info(f"AI卖出信号: 价格({current_price:.2f}) > 上轨({current_upper:.2f})")
`;
}

// 调整策略参数
function adjustStrategyParameters(code: string, prompt: string, parameters: any): string {
  // 这里可以根据提示词进一步调整策略参数
  // 例如，如果提示词中包含"保守"，可以调整风险参数
  
  if (prompt.toLowerCase().includes('保守') || prompt.toLowerCase().includes('低风险')) {
    // 调整为更保守的参数
    code = code.replace(/order_target_percent\(.*?, 1\.0\)/, 'order_target_percent(context.symbol, 0.5)');
  } else if (prompt.toLowerCase().includes('激进') || prompt.toLowerCase().includes('高风险')) {
    // 调整为更激进的参数
    code = code.replace(/order_target_percent\(.*?, 1\.0\)/, 'order_target_percent(context.symbol, 1.5)');
  }
  
  return code;
}

// 提取策略参数
function extractParameters(code: string): Record<string, any> {
  const parameters: Record<string, any> = {};
  
  // 简单的正则表达式提取参数
  const matches = code.match(/context\.(\w+)\s*=\s*(\d+(?:\.\d+)?)/g);
  
  if (matches) {
    matches.forEach(match => {
      const [, key, value] = match.match(/context\.(\w+)\s*=\s*(\d+(?:\.\d+)?)/) || [];
      if (key && value) {
        parameters[key] = parseFloat(value);
      }
    });
  }
  
  return parameters;
}

// 生成随机参数
function generateRandomParameters(): Record<string, any> {
  return {
    rsiPeriod: Math.floor(Math.random() * 20) + 10,
    oversoldLevel: Math.floor(Math.random() * 10) + 25,
    overboughtLevel: Math.floor(Math.random() * 10) + 70,
    shortPeriod: Math.floor(Math.random() * 10) + 5,
    longPeriod: Math.floor(Math.random() * 20) + 15
  };
}

// 生成优化后的参数
function generateOptimizedParameters(originalParameters: any): Record<string, any> {
  const optimized = { ...originalParameters };
  
  // 对每个参数进行小幅调整
  Object.keys(optimized).forEach(key => {
    if (typeof optimized[key] === 'number') {
      const variation = (Math.random() - 0.5) * 0.2; // ±10%的变化
      optimized[key] = Math.round(optimized[key] * (1 + variation));
    }
  });
  
  return optimized;
}
