/**
 * 市场数据路由
 */
import express from 'express';
import { authenticateToken } from './auth.js';

const router = express.Router();

export default function(marketDataService) {
  
  /**
   * 获取实时行情
   */
  router.get('/quotes', async (req, res) => {
    try {
      const { symbols, limit = 20 } = req.query;
      
      let quotes;
      if (symbols) {
        const symbolList = symbols.split(',');
        quotes = await marketDataService.getQuotes(symbolList);
      } else {
        quotes = await marketDataService.getTopQuotes(parseInt(limit));
      }
      
      res.json({
        success: true,
        data: quotes,
        count: quotes.length,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('获取行情错误:', error);
      res.status(500).json({
        success: false,
        message: '获取市场行情失败',
        code: 'MARKET_DATA_ERROR'
      });
    }
  });

  /**
   * 获取K线数据
   */
  router.get('/kline/:symbol', async (req, res) => {
    try {
      const { symbol } = req.params;
      const { period = '1d', limit = 100, startTime, endTime } = req.query;
      
      if (!symbol) {
        return res.status(400).json({
          success: false,
          message: '股票代码不能为空',
          code: 'INVALID_SYMBOL'
        });
      }
      
      const klineData = await marketDataService.getKlineData(
        symbol,
        period,
        parseInt(limit),
        startTime,
        endTime
      );
      
      res.json({
        success: true,
        data: {
          symbol,
          period,
          klines: klineData,
          count: klineData.length
        },
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('获取K线数据错误:', error);
      res.status(500).json({
        success: false,
        message: '获取K线数据失败',
        code: 'KLINE_DATA_ERROR'
      });
    }
  });

  /**
   * 搜索股票
   */
  router.get('/search', async (req, res) => {
    try {
      const { q, limit = 10 } = req.query;
      
      if (!q || q.length < 1) {
        return res.status(400).json({
          success: false,
          message: '搜索关键词不能为空',
          code: 'INVALID_QUERY'
        });
      }
      
      const results = await marketDataService.searchStocks(q, parseInt(limit));
      
      res.json({
        success: true,
        data: results,
        count: results.length,
        query: q
      });
      
    } catch (error) {
      console.error('搜索股票错误:', error);
      res.status(500).json({
        success: false,
        message: '搜索股票失败',
        code: 'SEARCH_ERROR'
      });
    }
  });

  /**
   * 获取股票详细信息
   */
  router.get('/detail/:symbol', async (req, res) => {
    try {
      const { symbol } = req.params;
      
      if (!symbol) {
        return res.status(400).json({
          success: false,
          message: '股票代码不能为空',
          code: 'INVALID_SYMBOL'
        });
      }
      
      const detail = await marketDataService.getStockDetail(symbol);
      
      if (!detail) {
        return res.status(404).json({
          success: false,
          message: '股票不存在',
          code: 'STOCK_NOT_FOUND'
        });
      }
      
      res.json({
        success: true,
        data: detail
      });
      
    } catch (error) {
      console.error('获取股票详情错误:', error);
      res.status(500).json({
        success: false,
        message: '获取股票详情失败',
        code: 'STOCK_DETAIL_ERROR'
      });
    }
  });

  /**
   * 获取市场概览
   */
  router.get('/overview', async (req, res) => {
    try {
      const overview = await marketDataService.getMarketOverview();
      
      res.json({
        success: true,
        data: overview,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('获取市场概览错误:', error);
      res.status(500).json({
        success: false,
        message: '获取市场概览失败',
        code: 'MARKET_OVERVIEW_ERROR'
      });
    }
  });

  /**
   * 获取板块数据
   */
  router.get('/sectors', async (req, res) => {
    try {
      const sectors = await marketDataService.getSectors();
      
      res.json({
        success: true,
        data: sectors,
        count: sectors.length
      });
      
    } catch (error) {
      console.error('获取板块数据错误:', error);
      res.status(500).json({
        success: false,
        message: '获取板块数据失败',
        code: 'SECTORS_ERROR'
      });
    }
  });

  /**
   * 获取涨跌排行榜
   */
  router.get('/ranking', async (req, res) => {
    try {
      const { type = 'gainers', limit = 20 } = req.query;
      
      const ranking = await marketDataService.getRanking(type, parseInt(limit));
      
      res.json({
        success: true,
        data: ranking,
        type,
        count: ranking.length
      });
      
    } catch (error) {
      console.error('获取排行榜错误:', error);
      res.status(500).json({
        success: false,
        message: '获取排行榜失败',
        code: 'RANKING_ERROR'
      });
    }
  });

  /**
   * 获取深度数据
   */
  router.get('/depth/:symbol', async (req, res) => {
    try {
      const { symbol } = req.params;
      
      if (!symbol) {
        return res.status(400).json({
          success: false,
          message: '股票代码不能为空',
          code: 'INVALID_SYMBOL'
        });
      }
      
      const depth = await marketDataService.getDepthData(symbol);
      
      res.json({
        success: true,
        data: depth,
        symbol
      });
      
    } catch (error) {
      console.error('获取深度数据错误:', error);
      res.status(500).json({
        success: false,
        message: '获取深度数据失败',
        code: 'DEPTH_DATA_ERROR'
      });
    }
  });

  return router;
}