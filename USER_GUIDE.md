# 量化投资平台 - 用户使用指南

## 🎯 平台概述

这是一个专业的量化投资交易平台，为投资者和交易员提供：
- 📊 **实时市场数据** - 股票价格、K线图表、技术指标
- 💰 **智能交易系统** - 算法交易、订单管理、风险控制
- 🧠 **策略研发** - 量化策略开发、回测验证、参数优化
- 📋 **投资组合管理** - 资产配置、收益追踪、风险分析
- 🛡️ **风险管理** - 风险监控、止损设置、资金管理

## 🚀 快速开始

### 1. 访问平台

**开发环境:**
- 前端应用: http://localhost:5173
- 后端API: http://localhost:8000

**生产环境:**
- 统一入口: http://localhost
- API接口: http://localhost/api/v1

### 2. 平台启动

选择以下方式之一启动平台：

**方式1: 自动部署脚本**
```bash
# Linux/Mac
./deploy.sh dev

# Windows
deploy.bat
```

**方式2: Docker Compose**
```bash
# 开发环境
docker-compose up -d

# 生产环境  
docker-compose -f docker-compose.prod.yml up -d
```

**方式3: 本地开发**
```bash
# 启动后端
cd backend && npm run dev

# 启动前端
cd a && npm run dev
```

## 📖 功能模块详解

### 📊 投资仪表盘

**路径**: `/dashboard` 或 `/`

**功能描述:**
- 总资产概览和今日收益
- 持仓数量和策略收益统计
- 市场概览(上证、深证、创业板指数)
- 快速访问各功能模块

**使用说明:**
1. 登录后自动跳转到仪表盘
2. 查看核心财务指标
3. 点击功能模块卡片快速跳转

### 📈 市场行情

**路径**: `/market`

**功能描述:**
- 实时股票价格更新(每10秒)
- 涨跌幅排序和股票筛选
- 股票详情查看
- 市场趋势分析

**使用说明:**
1. 浏览股票列表，查看实时价格
2. 点击表头进行排序(价格、涨跌幅)
3. 点击股票代码查看详细信息
4. 系统每10秒自动更新数据

**API接口:**
- 获取股票列表: `GET /api/v1/market/stocks`
- 实时价格: WebSocket `/api/v1/ws`

### 💰 智能交易

**路径**: `/trading`

**功能描述:**
- 股票买卖交易下单
- 订单状态跟踪
- 持仓管理和账户信息
- 实时交易数据

**使用说明:**
1. 选择股票代码(如: 000001)
2. 设置交易类型(买入/卖出)
3. 输入交易数量和价格
4. 提交订单并跟踪状态
5. 查看当前持仓和账户余额

**交易流程:**
```
选择股票 → 设置参数 → 提交订单 → 确认执行 → 跟踪状态
```

**API接口:**
- 提交订单: `POST /api/v1/trading/orders`
- 查询持仓: `GET /api/v1/trading/positions`
- 账户信息: `GET /api/v1/trading/account`

### 🧠 策略管理

**路径**: `/strategy`

**功能描述:**
- 查看预置量化策略
- 策略参数配置
- 策略启用/停用
- 策略收益统计

**使用说明:**
1. 浏览可用策略列表
2. 查看策略详细说明
3. 配置策略参数
4. 启用策略并监控表现

**预置策略:**
- 移动平均线策略
- 布林带策略  
- MACD指标策略
- RSI动量策略

**API接口:**
- 策略列表: `GET /api/v1/strategy/list`
- 策略配置: `POST /api/v1/strategy/config`

### 🔄 策略回测

**路径**: `/backtest`

**功能描述:**
- 历史数据回测验证
- 回测结果可视化
- 收益曲线分析
- 风险指标计算

**使用说明:**
1. 选择回测策略
2. 设置回测参数(时间范围、初始资金)
3. 启动回测计算
4. 查看回测报告和图表

**回测指标:**
- 总收益率、年化收益率
- 最大回撤、夏普比率
- 胜率、盈亏比
- 交易次数、手续费

**API接口:**
- 创建回测: `POST /api/v1/backtest/create`
- 回测结果: `GET /api/v1/backtest/results/:id`

### 📋 投资组合

**路径**: `/portfolio`

**功能描述:**
- 资产配置概览
- 持仓分析和收益统计
- 行业分布和风险评估

**使用说明:**
1. 查看投资组合总览
2. 分析持仓结构
3. 监控收益表现
4. 调整资产配置

### 🛡️ 风险管理

**路径**: `/risk`

**功能描述:**
- 实时风险监控
- 止损止盈设置
- 仓位控制
- 风险预警

**使用说明:**
1. 设置风险控制参数
2. 监控实时风险指标
3. 接收风险预警通知
4. 执行风险控制措施

### ⚙️ 系统设置

**路径**: `/settings`

**功能描述:**
- 用户个人信息
- 交易参数配置
- 通知设置
- 系统偏好设置

## 🔧 高级功能

### WebSocket实时数据

平台通过WebSocket提供实时数据推送：

**连接地址**: `ws://localhost:8000/api/v1/ws`

**数据类型:**
- 股票价格更新
- 订单状态变化
- 策略执行结果
- 风险预警通知

### API接口调用

**基础URL**: `http://localhost/api/v1`

**认证方式**: JWT Token (可选)

**常用接口:**
```javascript
// 获取市场数据
fetch('/api/v1/market/stocks')

// 提交交易订单
fetch('/api/v1/trading/orders', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({
    symbol: '000001',
    side: 'buy',
    quantity: 100,
    price: 10.50
  })
})
```

## 🚨 故障排除

### 常见问题

1. **页面加载失败**
   - 检查服务器是否启动: `docker-compose ps`
   - 查看错误日志: `docker-compose logs -f`

2. **实时数据不更新**
   - 检查WebSocket连接状态
   - 刷新页面重新建立连接

3. **交易功能异常**
   - 确认后端API服务正常
   - 检查网络连接
   - 查看浏览器控制台错误

4. **Docker容器启动失败**
   - 检查端口占用: `netstat -tulpn | grep :80`
   - 清理Docker资源: `docker system prune`
   - 重新构建镜像: `./deploy.sh rebuild`

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend

# 查看最近日志
docker-compose logs --tail=100 backend
```

### 性能优化

1. **前端优化**
   - 启用浏览器缓存
   - 使用生产构建版本
   - 启用Gzip压缩

2. **后端优化**
   - 调整数据更新频率
   - 启用Redis缓存
   - 优化数据库查询

## 📞 技术支持

### 自助服务

1. **重启服务**: `./deploy.sh stop && ./deploy.sh dev`
2. **健康检查**: `./deploy.sh status`
3. **查看文档**: 阅读 `DEPLOYMENT.md`

### 开发者资源

- **API文档**: `/api/v1/docs` (计划中)
- **源码结构**: 详见项目目录说明
- **技术栈**: Vue 3 + Node.js + Docker

---

## 📝 版本信息

- **当前版本**: v1.0.0
- **更新时间**: 2025-08-08
- **兼容性**: Chrome 88+, Firefox 85+, Safari 14+

## 📄 法律声明

本平台仅用于学习和研究目的，不构成投资建议。投资有风险，入市需谨慎。