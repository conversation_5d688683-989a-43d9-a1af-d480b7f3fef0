/**
 * 用户管理路由
 */
import express from 'express';
import { authenticateToken } from './auth.js';

const router = express.Router();

/**
 * 获取用户设置
 */
router.get('/settings', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    
    // 模拟用户设置
    const settings = {
      userId,
      preferences: {
        theme: 'light',
        language: 'zh-CN',
        timezone: 'Asia/Shanghai',
        dateFormat: 'YYYY-MM-DD',
        numberFormat: 'cn',
        autoRefresh: true,
        refreshInterval: 5000
      },
      trading: {
        confirmOrders: true,
        defaultOrderType: 'limit',
        riskWarnings: true,
        positionSizeLimit: 0.1,
        dailyLossLimit: 0.05
      },
      notifications: {
        email: true,
        sms: false,
        push: true,
        priceAlerts: true,
        orderAlerts: true,
        systemAlerts: true
      },
      privacy: {
        showProfile: false,
        shareTradeHistory: false,
        analyticsTracking: true
      }
    };
    
    res.json({
      success: true,
      data: settings
    });
    
  } catch (error) {
    console.error('获取用户设置错误:', error);
    res.status(500).json({
      success: false,
      message: '获取用户设置失败',
      code: 'USER_SETTINGS_ERROR'
    });
  }
});

/**
 * 更新用户设置
 */
router.put('/settings', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const settings = req.body;
    
    // 这里应该保存到数据库，现在只是模拟
    console.log(`用户 ${userId} 更新设置:`, settings);
    
    res.json({
      success: true,
      message: '设置更新成功',
      data: settings
    });
    
  } catch (error) {
    console.error('更新用户设置错误:', error);
    res.status(500).json({
      success: false,
      message: '更新用户设置失败',
      code: 'UPDATE_SETTINGS_ERROR'
    });
  }
});

export default router;