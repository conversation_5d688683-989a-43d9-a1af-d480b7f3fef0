/**
 * 投资组合路由
 */
import express from 'express';
import { authenticateToken } from './auth.js';

const router = express.Router();

/**
 * 获取投资组合概览
 */
router.get('/overview', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    
    // 模拟投资组合数据
    const portfolio = {
      totalValue: 1234567.89,
      totalProfit: 84567.89,
      totalProfitPercent: 7.35,
      todayProfit: 8765.43,
      todayProfitPercent: 0.71,
      cash: 450000.00,
      positions: [
        {
          symbol: '000001',
          name: '平安银行',
          quantity: 10000,
          avgPrice: 12.50,
          currentPrice: 13.19,
          marketValue: 131900,
          profit: 6900,
          profitPercent: 5.52,
          weight: 10.68
        },
        {
          symbol: '600036',
          name: '招商银行',
          quantity: 5000,
          avgPrice: 35.20,
          currentPrice: 36.80,
          marketValue: 184000,
          profit: 8000,
          profitPercent: 4.55,
          weight: 14.90
        },
        {
          symbol: '000858',
          name: '五粮液',
          quantity: 1000,
          avgPrice: 165.30,
          currentPrice: 178.90,
          marketValue: 178900,
          profit: 13600,
          profitPercent: 8.23,
          weight: 14.49
        }
      ],
      assetAllocation: {
        stocks: 0.635,
        cash: 0.365,
        bonds: 0,
        funds: 0
      },
      sectorAllocation: {
        '银行': 0.256,
        '食品饮料': 0.145,
        '房地产': 0.089,
        '医药生物': 0.067,
        '其他': 0.443
      },
      riskMetrics: {
        beta: 1.15,
        sharpe: 1.23,
        volatility: 0.18,
        maxDrawdown: 0.12
      }
    };
    
    res.json({
      success: true,
      data: portfolio
    });
    
  } catch (error) {
    console.error('获取投资组合错误:', error);
    res.status(500).json({
      success: false,
      message: '获取投资组合失败',
      code: 'PORTFOLIO_ERROR'
    });
  }
});

/**
 * 获取持仓明细
 */
router.get('/positions', authenticateToken, async (req, res) => {
  try {
    const positions = [
      {
        symbol: '000001',
        name: '平安银行',
        quantity: 10000,
        availableQuantity: 10000,
        avgPrice: 12.50,
        currentPrice: 13.19,
        marketValue: 131900,
        profit: 6900,
        profitPercent: 5.52,
        todayProfit: 659,
        todayProfitPercent: 0.50,
        weight: 10.68,
        sector: '银行',
        openDate: '2024-01-15'
      },
      {
        symbol: '600036',
        name: '招商银行',
        quantity: 5000,
        availableQuantity: 5000,
        avgPrice: 35.20,
        currentPrice: 36.80,
        marketValue: 184000,
        profit: 8000,
        profitPercent: 4.55,
        todayProfit: 920,
        todayProfitPercent: 0.50,
        weight: 14.90,
        sector: '银行',
        openDate: '2024-01-20'
      }
    ];
    
    res.json({
      success: true,
      data: {
        positions,
        summary: {
          totalPositions: positions.length,
          totalMarketValue: positions.reduce((sum, p) => sum + p.marketValue, 0),
          totalProfit: positions.reduce((sum, p) => sum + p.profit, 0),
          todayProfit: positions.reduce((sum, p) => sum + p.todayProfit, 0)
        }
      }
    });
    
  } catch (error) {
    console.error('获取持仓明细错误:', error);
    res.status(500).json({
      success: false,
      message: '获取持仓明细失败',
      code: 'POSITIONS_ERROR'
    });
  }
});

/**
 * 获取历史收益
 */
router.get('/performance', authenticateToken, async (req, res) => {
  try {
    const { period = '1M' } = req.query;
    
    // 生成历史收益数据
    const now = new Date();
    const data = [];
    let days;
    
    switch (period) {
      case '1W': days = 7; break;
      case '1M': days = 30; break;
      case '3M': days = 90; break;
      case '6M': days = 180; break;
      case '1Y': days = 365; break;
      default: days = 30;
    }
    
    let value = 1150000; // 初始值
    
    for (let i = days; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      
      // 模拟收益波动
      const dailyReturn = (Math.random() - 0.5) * 0.04; // ±2% 日波动
      value *= (1 + dailyReturn);
      
      data.push({
        date: date.toISOString().split('T')[0],
        value: Number(value.toFixed(2)),
        return: Number(((value - 1150000) / 1150000 * 100).toFixed(2))
      });
    }
    
    res.json({
      success: true,
      data: {
        period,
        performance: data,
        summary: {
          totalReturn: data[data.length - 1].return,
          maxValue: Math.max(...data.map(d => d.value)),
          minValue: Math.min(...data.map(d => d.value)),
          volatility: Number((Math.random() * 0.2).toFixed(4))
        }
      }
    });
    
  } catch (error) {
    console.error('获取历史收益错误:', error);
    res.status(500).json({
      success: false,
      message: '获取历史收益失败',
      code: 'PERFORMANCE_ERROR'
    });
  }
});

export default router;